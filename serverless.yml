service: prs

provider:
  name: aws
  runtime: nodejs20.x
  region: ap-east-1
  profile: serverless
  stage: ${file(./env.yml):stage}
  memorySize: ${file(./env.yml):memorySize, 256}
  timeout: 30
  apiGateway:
    apiKeys:
      - ${self:custom.config.namespace}-ck-api
  iam:
    role:
      statements:
        - Effect: "Allow"
          Action:
            - "ec2:CreateNetworkInterface"
            - "ec2:DescribeNetworkInterfaces"
            - "ec2:DetachNetworkInterface"
            - "ec2:DeleteNetworkInterface"
          Resource: "*"
        - Effect: "Allow"
          Action:
            - "lambda:InvokeFunction"
          Resource: "*"
        - Effect: "Allow"
          Action: "sns:*"
          Resource:
            - "arn:aws:sns:${self:custom.config.awsRegion}:${self:custom.config.awsAccountId}:${self:custom.config.namespace}-*"
        - Effect: "Allow"
          Action:
            - "sqs:SendMessage"
            - "sqs:SendMessageBatch"
            - "sqs:ReceiveMessage"
            - "sqs:DeleteMessage"
          Resource:
            - "arn:aws:sqs:${self:custom.config.awsRegion}:${self:custom.config.awsAccountId}:${self:custom.config.namespace}-*"
        - Effect: "Allow"
          Action: "s3:*"
          Resource: "arn:aws:s3:::${self:custom.config.dataBucketName}/*"

plugins:
  - serverless-vpc-discovery
  - serverless-webpack

custom:
  config:
    accountProductPriceChangeTopic: ${self:service}-${file(./env.yml):stage}-account-product-price-change
    accountUpdatesTopic: ${file(./env.yml):accountUpdatesTopic, "catk-account-updates"}
    awsAccountId: ${file(./env.yml):awsAccountId}
    awsAccessKeyId: ${file(./env.yml):awsAccessKeyId, ""}
    awsRegion: ${file(./env.yml):awsRegion, "us-east-1"}
    awsSecretAccessKey: ${file(./env.yml):awsSecretAccessKey, ""}
    batchJobCollection: ${file(./env.yml):batchJobCollection, "batchJob"}
    dbConnectionUri: ${file(./env.yml):dbConnectionUri}
    dbName: ${file(./env.yml):dbName, "product-prices"}
#    This var name is required for use with serverless-services AwsS3Service when using writeToS3
    dataBucketName: ${self:service}-${file(./env.yml):stage}-prices
#    The below is required for use with serverless-services AwsS3Service when using writeToS3
    dataBucketPathPrefix: ""
    priceElementChangeJobQueueName: ${self:service}-${file(./env.yml):stage}-price-element-change
    priceElementChangeJobTopic: ${self:service}-${file(./env.yml):stage}-price-element-change
    productBranchChangeTopic: ${file(./env.yml):productBrandChangeTopic, "catk-product-brand-change-request"}
    promoPriceListActionTopic: ${self:service}-${file(./env.yml):stage}-promo-price-list-action
    pssApiKey: ${file(./env.yml):pssApiKey}
    pssUrl: ${file(./env.yml):pssUrl}
    namespace: ${self:service}-${file(./env.yml):stage}
    updatePricesForProductsQueueName: ${self:service}-${file(./env.yml):stage}-update-price-for-products

  vpcDiscovery:
    vpcName: catk-vpc
    subnets:
      - tagKey: Name
        tagValues:
          - catk-lambda-subnet-a
          - catk-lambda-subnet-b
    securityGroups:
      - names:
        - catk-lambda-sg
  webpack:
    includeModules: true

functions:
  AccountOnSnsEvent:
    handler: src/controllers/account.onSnsEvent
    name: ${self:custom.config.namespace}-account-on-sns-event
    environment: ${self:custom.config}
    timeout: 900
    events:
      - sns:
          arn:
            Fn::Join:
              - ':'
              - - 'arn:aws:sns'
                - Ref: 'AWS::Region'
                - Ref: 'AWS::AccountId'
                - ${self:custom.config.accountUpdatesTopic}
          topicName: ${self:custom.config.accountUpdatesTopic}
  AccountSettingsGetOne:
    handler: src/controllers/account-settings.getOne
    name: ${self:custom.config.namespace}-account-settings-get-one
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: account-settings
          method: get
          private: true
          cors: true
  AccountSettingsReplaceOne:
    handler: src/controllers/account-settings.replaceOne
    name: ${self:custom.config.namespace}-account-settings-replace-one
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: account-settings
          method: put
          private: true
          cors: true
  MigrationRun:
    handler: src/controllers/migration.run
    name: ${self:custom.config.namespace}-migration-run
    environment: ${self:custom.config}
  MigrationUpdateCategoryIds:
    handler: src/controllers/migration.updateCategoryIds
    name: ${self:custom.config.namespace}-migration-update-category-ids
    environment: ${self:custom.config}
  PriceListsCheckPromoSchedule:
    handler: src/controllers/price-lists.checkPromoSchedule
    name: ${self:custom.config.namespace}-price-lists-check-promo-schedule
    environment: ${self:custom.config}
    events:
      - schedule: rate(1 minute)
  PriceListsCloneOne:
    handler: src/controllers/price-lists.cloneOne
    name: ${self:custom.config.namespace}-price-lists-clone-one
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: price-lists/clone
          method: post
          private: true
          cors: true
  PriceListsCreateOne:
    handler: src/controllers/price-lists.createOne
    name: ${self:custom.config.namespace}-price-lists-create-one
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: price-lists
          method: post
          private: true
          cors: true
  PriceListsDeleteOne:
    handler: src/controllers/price-lists.deleteOne
    name: ${self:custom.config.namespace}-price-lists-delete-one
    environment: ${self:custom.config}
    events:
      - http:
          path: price-lists/{priceListId}
          method: delete
          private: true
          cors: true
          request:
            parameters:
              paths:
                priceListId: true
  PriceListsGetHistory:
    handler: src/controllers/price-lists.getHistory
    name: ${self:custom.config.namespace}-price-lists-get-history
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: price-lists/{priceListId}/history
          method: get
          private: true
          cors: true
          request:
            parameters:
              paths:
                priceListId: true
  PriceListsGetOne:
    handler: src/controllers/price-lists.getOne
    name: ${self:custom.config.namespace}-price-lists-get-one
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: price-lists/{priceListId}
          method: get
          private: true
          cors: true
          request:
            parameters:
              paths:
                priceListId: true
  PriceListsGetPromoStatus:
    handler: src/controllers/price-lists.getPromoStatus
    name: ${self:custom.config.namespace}-price-lists-get-promo-status
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: price-lists/promo-status/{accountId}
          method: get
          private: true
          cors: true
          request:
            parameters:
              paths:
                accountId: true
  PriceListsReplaceOne:
    handler: src/controllers/price-lists.replaceOne
    name: ${self:custom.config.namespace}-price-lists-replace-one
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: price-lists/{priceListId}
          method: put
          private: true
          cors: true
          request:
            parameters:
              paths:
                priceListId: true
  PriceListsSearch:
    handler: src/controllers/price-lists.search
    name: ${self:custom.config.namespace}-price-lists-search
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: price-lists/search
          method: post
          private: true
          cors: true
  PriceListsSetOrder:
    handler: src/controllers/price-lists.setOrder
    name: ${self:custom.config.namespace}-price-lists-set-order
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: price-lists/order
          method: put
          private: true
          cors: true
  PriceListsUpdateOne:
    handler: src/controllers/price-lists.updateOne
    name: ${self:custom.config.namespace}-price-lists-update-one
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: price-lists/{priceListId}
          method: patch
          private: true
          cors: true
          request:
            parameters:
              paths:
                priceListId: true
  PriceRulesCreateOne:
    handler: src/controllers/price-rules.createOne
    name: ${self:custom.config.namespace}-price-rules-create-one
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: price-rules
          method: post
          private: true
          cors: true
  PriceRulesDeleteOne:
    handler: src/controllers/price-rules.deleteOne
    name: ${self:custom.config.namespace}-price-rules-delete-one
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: price-rules/{priceRuleId}
          method: delete
          private: true
          cors: true
          request:
            parameters:
              paths:
                priceRuleId: true
  PriceRulesGetOne:
    handler: src/controllers/price-rules.getOne
    name: ${self:custom.config.namespace}-price-rules-get-one
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: price-rules/{priceRuleId}
          method: get
          private: true
          cors: true
          request:
            parameters:
              paths:
                priceRuleId: true
  PriceRulesReplaceOne:
    handler: src/controllers/price-rules.replaceOne
    name: ${self:custom.config.namespace}-price-rules-replace-one
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: price-rules/{priceRuleId}
          method: put
          private: true
          cors: true
          request:
            parameters:
              paths:
                priceRuleId: true
  PriceRulesSearch:
    handler: src/controllers/price-rules.search
    name: ${self:custom.config.namespace}-price-rules-search
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: price-rules/search
          method: post
          private: true
          cors: true
  PriceRulesUpdateOne:
    handler: src/controllers/price-rules.updateOne
    name: ${self:custom.config.namespace}-price-rules-update-one
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: price-rules/{priceRuleId}
          method: patch
          private: true
          cors: true
          request:
            parameters:
              paths:
                priceRuleId: true
  ProductCalcBasePrice:
    handler: src/controllers/product.calcBasePrice
    name: ${self:custom.config.namespace}-product-calc-base-price
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: product/calc/base-price
          method: post
          private: true
          cors: true
  ProductCalcDetail:
    handler: src/controllers/product.calcDetail
    name: ${self:custom.config.namespace}-product-calc-detail
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: product/calc/detail
          method: post
          private: true
          cors: true
  ProductCalcFromPriceRule:
    handler: src/controllers/product.calcFromPriceRule
    name: ${self:custom.config.namespace}-product-calc-from-price-rule
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: product/calc/prices-by-rule/{priceRuleId}
          method: post
          private: true
          cors: true
          request:
            parameters:
              paths:
                priceRuleId: true
  ProductCountPricedProducts:
    handler: src/controllers/product.countPricedProducts
    name: ${self:custom.config.namespace}-product-count-priced-products
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: product/count
          method: get
          private: true
          cors: true
  ProductEventBatch:
    handler: src/controllers/product.eventBatch
    name: ${self:custom.config.namespace}-product-event-batch
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: product/event/batch
          method: post
          private: true
          cors: true
  ProductEventSingle:
    handler: src/controllers/product.eventSingle
    name: ${self:custom.config.namespace}-product-event-single
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: product/event
          method: post
          private: true
          cors: true
  ProductFixedPricesDelete:
    handler: src/controllers/product.fixedPricesDelete
    name: ${self:custom.config.namespace}-product-fixed-prices-delete
    environment: ${self:custom.config}
    events:
      - http:
          path: product/fixed-prices/{priceListId}/{brand}/{sku}
          method: delete
          private: true
          cors: true
          request:
            parameters:
              paths:
                brand: true
                priceListId: true
                sku: true
  ProductFixedPricesDeleteAll:
    handler: src/controllers/product.fixedPricesDeleteAll
    name: ${self:custom.config.namespace}-product-fixed-prices-delete-all
    environment: ${self:custom.config}
    events:
      - http:
          path: product/fixed-prices/{priceListId}
          method: delete
          private: true
          cors: true
          request:
            parameters:
              paths:
                priceListId: true
  ProductFixedPricesGet:
    handler: src/controllers/product.fixedPricesGet
    name: ${self:custom.config.namespace}-product-fixed-prices-get
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: product/fixed-prices/{priceListId}/{brand}/{sku}
          method: get
          private: true
          cors: true
          request:
            parameters:
              paths:
                brand: true
                priceListId: true
                sku: true
  ProductFixedPricesSet:
    handler: src/controllers/product.fixedPricesSet
    name: ${self:custom.config.namespace}-product-fixed-prices-set
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: product/fixed-prices/{priceListId}/{brand}/{sku}
          method: put
          private: true
          cors: true
          request:
            parameters:
              paths:
                brand: true
                priceListId: true
                sku: true
  ProductFixedPricesSetBatch:
    handler: src/controllers/product.fixedPricesSetBatch
    name: ${self:custom.config.namespace}-product-fixed-prices-set-batch
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: product/fixed-prices/{priceListId}/batch
          method: post
          private: true
          cors: true
          request:
            parameters:
              paths:
                priceListId: true
  ProductGetActivePrices:
    handler: src/controllers/product.getActivePrices
    name: ${self:custom.config.namespace}-product-get-active-prices
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: product/prices/active
          method: post
          private: true
          cors: true
  ProductGetProductPackagePrices:
    handler: src/controllers/product.getProductPackagePrices
    name: ${self:custom.config.namespace}-product-get-product-package-prices
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: product/prices/package
          method: post
          private: true
          cors: true
  ProductGetPricesPendingChanges:
    handler: src/controllers/product.getPricesPendingChanges
    name: ${self:custom.config.namespace}-product-get-prices-pending-changes
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: product/prices/pending-changes/{accountId}
          method: get
          private: true
          cors: true
          request:
            parameters:
              paths:
                accountId: true
  ProductOnPriceElementChange:
    handler: src/controllers/product.onPriceElementChange
    name: ${self:custom.config.namespace}-product-on-price-element-change
    environment: ${self:custom.config}
    memorySize: 2048
    reservedConcurrency: 10
    timeout: 120
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - PriceElementChangeJobQueue
              - Arn
          batchSize: 1
  ProductOnProductBrandChange:
    handler: src/controllers/product.onProductBrandChange
    name: ${self:custom.config.namespace}-product-on-product-brand-change
    environment: ${self:custom.config}
    timeout: 900
    events:
      - sns:
          arn:
            Fn::Join:
              - ':'
              - - 'arn:aws:sns'
                - Ref: 'AWS::Region'
                - Ref: 'AWS::AccountId'
                - ${self:custom.config.productBranchChangeTopic}
          topicName: ${self:custom.config.productBranchChangeTopic}
  ProductPriceRuleMatch:
    handler: src/controllers/product.priceRuleMatch
    name: ${self:custom.config.namespace}-product-price-rule-match
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: product/price-rule-match
          method: post
          private: true
          cors: true
  ProductPricesSearch:
    handler: src/controllers/product.pricesSearch
    name: ${self:custom.config.namespace}-product-prices-search
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: product/prices/search
          method: post
          private: true
          cors: true
  ProductUpdatePrices:
    handler: src/controllers/product.updatePrices
    name: ${self:custom.config.namespace}-product-update-prices
    environment: ${self:custom.config}
    reservedConcurrency: 20
    timeout: 30
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - UpdatePricesForProductsQueue
              - Arn
          batchSize: 1
  RentalTermsCreateOne:
    handler: src/controllers/rental-terms.createOne
    name: ${self:custom.config.namespace}-rental-terms-create-one
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: rental-terms
          method: post
          private: true
          cors: true
  RentalTermsDeleteOne:
    handler: src/controllers/rental-terms.deleteOne
    name: ${self:custom.config.namespace}-rental-terms-delete-one
    environment: ${self:custom.config}
    events:
      - http:
          path: rental-terms/{rentalTermId}
          method: delete
          private: true
          cors: true
          request:
            parameters:
              paths:
                rentalTermId: true
  RentalTermsGetOne:
    handler: src/controllers/rental-terms.getOne
    name: ${self:custom.config.namespace}-rental-terms-get-one
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: rental-terms/{rentalTermId}
          method: get
          private: true
          cors: true
          request:
            parameters:
              paths:
                rentalTermId: true
  RentalTermsReplaceOne:
    handler: src/controllers/rental-terms.replaceOne
    name: ${self:custom.config.namespace}-rental-terms-replace-one
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: rental-terms/{rentalTermId}
          method: put
          private: true
          cors: true
          request:
            parameters:
              paths:
                rentalTermId: true
  RentalTermsSearch:
    handler: src/controllers/rental-terms.search
    name: ${self:custom.config.namespace}-rental-terms-search
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: rental-terms/search
          method: post
          private: true
          cors: true
  RentalTermsSetOrder:
    handler: src/controllers/rental-terms.setOrder
    name: ${self:custom.config.namespace}-rental-terms-set-order
    environment: ${self:custom.config}
    provisionedConcurrency: 1
    events:
      - http:
          path: rental-terms/order
          method: put
          private: true
          cors: true

resources:
  Resources:
    AccountProductPriceChangeTopic:
      Type: AWS::SNS::Topic
      Properties:
        DisplayName: ${self:custom.config.accountProductPriceChangeTopic}
        TopicName: ${self:custom.config.accountProductPriceChangeTopic}
    PriceChangesBucket:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: ${self:custom.config.dataBucketName}
        LifecycleConfiguration:
          Rules:
            - Id: 'price-changes-1day'
              Prefix: 'price-changes/'
              Status: 'Enabled'
              ExpirationInDays: 1
        WebsiteConfiguration:
          IndexDocument: 'index.html'
    PriceElementChangeJobQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.config.priceElementChangeJobQueueName}
        VisibilityTimeout: 120
    PriceElementChangeJobTopic:
      Type: AWS::SNS::Topic
      Properties:
        DisplayName: ${self:custom.config.priceElementChangeJobTopic}
        TopicName: ${self:custom.config.priceElementChangeJobTopic}
    PromoPriceListActionTopic:
      Type: AWS::SNS::Topic
      Properties:
        DisplayName: ${self:custom.config.promoPriceListActionTopic}
        TopicName: ${self:custom.config.promoPriceListActionTopic}
    UpdatePricesForProductsQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:custom.config.updatePricesForProductsQueueName}
        VisibilityTimeout: 30
