module.exports = {
    async up(db) {
        // priceElementChangeQueueLock

        // Add unique index for accountId
        await db.collection('priceElementChangeQueueLock').createIndex({
            accountId: 1,
        }, {
            unique: true,
        });

        // Add TTL for the lock date to safe guard against stuck locks
        await db.collection('priceElementChangeQueueLock').createIndex({
            lockedAt: 1,
        }, {
            expireAfterSeconds: 60 * 60 * 12, // 12 hours
        });
    },
};
