// eslint-disable-next-line @typescript-eslint/no-var-requires
const _ = require('lodash');
module.exports = {
    async up(db) {

        // Update all existing documents to have action: 'update'
        await db.collection('priceRuleArchive').updateMany({}, {
            $set: {
                action: 'update',
            },
        });

        // Get the first archive document _id for each price rule as this will be the 'create' _id
        const createPriceRuleArchiveEvents = await db.collection('priceRuleArchive').aggregate([
            {$sort: {timestamp: 1}},
            {$group: {
                _id: '$priceRuleId',
                archiveOid: {$first: '$_id'},
            }},
        ]).toArray();

        // Update these documents to set action to 'create'
        await db.collection('priceRuleArchive').updateMany({
            _id: {
                $in: _.map(createPriceRuleArchiveEvents, 'archiveOid'),
            },
        }, {
            $set: {
                action: 'create',
            },
        });
    },
};
