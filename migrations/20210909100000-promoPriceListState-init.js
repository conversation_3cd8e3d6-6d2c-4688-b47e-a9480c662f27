module.exports = {
    async up(db) {

        // promoPriceListState

        // Query by priceListId
        await db.collection('promoPriceListState').createIndex({
            priceListId: 1,
        });

        // Expire 15 hours after pricesExpireAt time (to account for max timezone offset)
        await db.collection('promoPriceListState').createIndex({
            pricesExpireAt: 1,
        }, {
            expireAfterSeconds: 60 * 60 * 15,
        });

        // priceList

        // Query by schedule.endDate (newest first)
        await db.collection('priceList').createIndex({
            'schedule.endDate': -1,
        });

        // Query by schedule.startDate (newest first)
        await db.collection('priceList').createIndex({
            'schedule.startDate': -1,
        });

        // timezone

        // Drop timezones that are not requested after 30 days
        await db.collection('timezone').createIndex({
            lastRequestedAt: 1,
        }, {
            expireAfterSeconds: 60 * 60 * 24 * 30,
        });

        // Unique key for accountId + timezone (plus ability to query by accountId)
        await db.collection('timezone').createIndex({
            accountId: 1,
            timezone: 1,
        }, {
            unique: true,
        });


    },
};
