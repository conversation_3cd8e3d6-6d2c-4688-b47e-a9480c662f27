// eslint-disable-next-line @typescript-eslint/no-var-requires
const _ = require('lodash');

module.exports = {
    async up(db) {
        // productPrice
        const productPriceCollection = db.collection('productPrice');

        // Expire 15 hours after pricesExpireAt time (to account for max timezone offset)
        await productPriceCollection.createIndex({
            expirationDate: 1,
        }, {
            expireAfterSeconds: 60 * 60 * 15,
        });

        const promoPriceListIds = await db.collection('priceList').find({
            type: 'promotional',
        }, {
            projection: {
                _id: 1,
            },
        }).toArray();

        // Update all promo price lists to expire in an hour
        const defaultExpireDate = new Date(Date.now() + 1000 * 3600);
        await productPriceCollection.updateMany({
            priceListId: {
                $in: _.map(promoPriceListIds, (promoPriceListId) => promoPriceListId._id.toString()),
            },
        }, {
            $set: {
                expirationDate: defaultExpireDate,
            },
        });

        // Update current promo price lists to expire based on their existing expiration date
        const activePriceLists = await db.collection('promoPriceListState').find({
        }, {
            projection: {
                priceListId: 1,
                pricesExpireAt: 1,
            },
        }).toArray();

        for (const priceList of activePriceLists) {
            await productPriceCollection.updateMany({
                priceListId: priceList.priceListId,
            }, {
                $set: {
                    expirationDate: priceList.pricesExpireAt,
                },
            });
        }
    },
};
