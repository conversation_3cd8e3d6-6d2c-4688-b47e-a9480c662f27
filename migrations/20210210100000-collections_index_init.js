module.exports = {
    async down() {
        // Write the statements to rollback your migration (if possible)
    },

    async up(db) {
        await db.collection('priceList').createIndex({
            accountId: 1,
        });

        await db.collection('priceRule').createIndex({
            priceListId: 1,
        });

        await db.collection('productPrice').createIndex({
            priceListId: 1,
        });

        await db.collection('productPrice').createIndex({
            sku: 1,
            // eslint-disable-next-line sort-keys
            brand: 1,
            // eslint-disable-next-line sort-keys
            accountId: 1,
        });

        await db.collection('productPrice').createIndex({
            priceListId: 1,
            sku: 1,
            // eslint-disable-next-line sort-keys
            brand: 1,
        }, {
            unique: true,
        });

        await db.collection('rentalTerm').createIndex({
            accountId: 1,
            installments: 1,
            interval: 1,
            name: 1,
        }, {
            unique: true,
        });

        await db.collection('rentalTerm').createIndex({
            accountId: 1,
        });
    },
};
