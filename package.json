{"name": "price-rules-service", "version": "1.11.1", "description": "A Wondersign creation", "repository": {"type": "git", "url": "git+ssh://*****************/apxdev/price-rules-service.git"}, "author": "Wondersign", "license": "ISC", "homepage": "https://bitbucket.org/apxdev/price-rules-service", "devDependencies": {"@aws-sdk/client-lambda": "^3.645.0", "@aws-sdk/client-s3": "^3.645.0", "@aws-sdk/client-sns": "^3.645.0", "@aws-sdk/client-sqs": "^3.645.0", "@aws-sdk/s3-request-presigner": "^3.645.0", "@manifoldco/swagger-to-ts": "^2.1.0", "@types/aws-lambda": "^8.10.145", "@types/jasmine": "5.1.4", "@types/lodash": "^4.17.7", "@types/node": "^22.5.4", "@typescript-eslint/eslint-plugin": "~5.62.0", "@typescript-eslint/parser": "~5.62.0", "@typescript-eslint/types": "~5.62.0", "aws-sdk": "^2.1691.0", "aws-sdk-client-mock": "^4.0.1", "aws-sdk-mock": "~6.1.1", "copy-webpack-plugin": "~12.0.2", "eslint": "~8.57.0", "eslint-plugin-import": "^2.30.0", "eslint-plugin-jsdoc": "^50.2.2", "eslint-plugin-prefer-arrow": "^1.2.3", "jasmine": "5.3.0", "jasmine-core": "^5.3.0", "jasmine-ts": "0.4.0", "js-yaml": "^4.1.0", "mongo-mock": "^4.2.0", "moxios": "^0.4.0", "nyc": "17.0.0", "serverless": "~3.38.0", "serverless-domain-manager": "~7.4.0", "serverless-plugin-datadog": "~5.70.0", "serverless-vpc-discovery": "^5.0.2", "serverless-webpack": "5.14.2", "ts-loader": "9.5.1", "ts-node": "^10.9.2", "typescript": "~5.1.6", "webpack": "5.94.0", "webpack-node-externals": "3.0.0"}, "dependencies": {"@wondersign/serverless-services": "~3.0.2", "axios": "~1.7.7", "datadog-lambda-js": "~9.115.0", "dd-trace": "~5.22.0", "lodash": "^4.17.21", "migrate-mongo": "^11.0.0", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "mongodb": "6.7.0", "uuid": "^10.0.0"}, "scripts": {"deploy": "npx sls deploy", "generate:contracts": "npx @manifoldco/swagger-to-ts ./src/openapi/spec.json --output ./src/models/contracts.ts && sed -i '' -e s/components/Components/ ./src/models/contracts.ts && npm run lint:fix", "lint": "node_modules/eslint/bin/eslint.js . --ext .ts", "lint:fix": "node_modules/eslint/bin/eslint.js . --ext .ts --fix", "test": "ts-node node_modules/jasmine/bin/jasmine", "test:coverage": "nyc --reporter=lcov -e .ts -x \"spec/**\" npm run test"}}