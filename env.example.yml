#awsAccessKeyId: "" # Your AWS access key
awsAccountId: "************" # Local Dev
#awsSecretAccessKey: "" # Your AWS secret access key

dbConnectionUri: mongodb+srv://username:<EMAIL>/?retryWrites=true
dbName: dbName
pssApiKey: apikey
pssUrl: https://
stage: localdev # Environment (Please change to your own. Example: "localdev-dt")

# datadog monitoring settings
# all configuration options available here: https://github.com/DataDog/serverless-plugin-datadog
# Enable tracing on Lambda function using dd-trace
datadogEnableDDTracing: false
