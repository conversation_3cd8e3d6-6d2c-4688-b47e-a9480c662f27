import {ObjectId} from 'mongodb';
import {MongoService} from '@wondersign/serverless-services';
import * as _ from 'lodash';
import {BatchJob} from '../models/batch-job';
import {MongoHelperService} from './mongo-helper.service';

const config = process.env;

export class BatchJobService {

    private completionHandler: (batchJob: BatchJob) => Promise<void>;

    public static async receiveRequest(batchJob: BatchJob): Promise<BatchJob> {
        const logPrefix = 'BatchJobService: save:';
        console.log(`${logPrefix} Saving batchJob`, batchJob);
        const batchJobCollection = await MongoService.getCollection<BatchJob>(config.batchJobCollection);
        const currentDate = new Date();
        const findOneAndUpdateResponse = await batchJobCollection.findOneAndUpdate(
            _.pick(batchJob, ['consumerId', 'jobId']),
            {
                $inc: {
                    requestReceivedCount: 1,
                },
                $set: {
                    updatedAt: currentDate,
                },
                // Insert only batchJob (Ref: https://docs.mongodb.com/manual/reference/operator/update/setOnInsert/)
                $setOnInsert: {
                    ...batchJob,
                    createdAt: currentDate,
                },
            },
            {
                includeResultMetadata: true,
                returnDocument: 'after',
                upsert: true,
            }
        );

        if (!findOneAndUpdateResponse.ok) {
            console.error(`${logPrefix} BatchJob cannot be saved. Result: `, findOneAndUpdateResponse);
            throw new Error('Error saving batch job');
        }

        return _.first(MongoHelperService.convertOidToId<BatchJob & {_id: ObjectId}>([
            findOneAndUpdateResponse.value,
        ]));
    }

    private static async delete(batchJobId: string): Promise<void> {
        const batchJobCollection = await MongoService.getCollection(config.batchJobCollection);
        await batchJobCollection.deleteOne({_id: new ObjectId(batchJobId)});
    }

    public setCompletionHandler(completionHandler: (batchJob: BatchJob) => Promise<void>): void {
        this.completionHandler = completionHandler;
    }

    public async updateProcessedRequest(batchJobId: string): Promise<void> {
        const logPrefix = 'BatchJobService: updateProcessedRequest:';
        console.log(`${logPrefix} Updating`, batchJobId);
        const batchJobCollection = await MongoService.getCollection<BatchJob>(config.batchJobCollection);
        const currentDate = new Date();
        const findOneAndUpdateResponse = await batchJobCollection.findOneAndUpdate(
            {_id: new ObjectId(batchJobId)},
            {
                $inc: {
                    requestProcessedCount: 1,
                },
                $set: {
                    updatedAt: currentDate,
                },
            },
            {
                includeResultMetadata: true,
                returnDocument: 'after',
            }
        );

        if (!findOneAndUpdateResponse.ok) {
            console.error(`${logPrefix} Batch job cannot be updated. Result: `, findOneAndUpdateResponse);
            throw new Error('Error updating batch job');
        }

        const processedCount = _.get(findOneAndUpdateResponse.value, 'requestProcessedCount');
        const totalCount = _.get(findOneAndUpdateResponse.value, 'requestTotalCount');
        if (processedCount === totalCount) {
            console.log(`${logPrefix} Batch job is complete for batchJobId: `, batchJobId);
            if (this.completionHandler) {
                const batchJob = _.first(MongoHelperService.convertOidToId<BatchJob & {_id: ObjectId}>([
                    findOneAndUpdateResponse.value,
                ]));
                console.log(`${logPrefix} Calling completion handler for batchJob: `, batchJob);
                await this.completionHandler(batchJob);
            }
            console.log(`${logPrefix} Deleting completed batchJob with batchJobId: `, batchJobId);
            await BatchJobService.delete(batchJobId);
        }
    }

}
