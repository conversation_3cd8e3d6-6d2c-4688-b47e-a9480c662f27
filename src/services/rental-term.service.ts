import {HttpError, MongoService, ValidationService} from '@wondersign/serverless-services';
import * as _ from 'lodash';
import {Filter, ObjectId} from 'mongodb';
import {components} from '../openapi/spec.json';
import {RentalTerm} from '../models/rental-term';
import {MongoHelperService} from './mongo-helper.service';
import {ProductPriceService} from './product-price.service';
import {PriceRuleService} from './price-rule.service';
import {PriceElementChangeService} from './price-element-change.service';

export class RentalTermService {
    private static readonly collectionName: string = 'rentalTerm';

    private readonly logPrefix: string;
    private validationService: ValidationService;

    public constructor() {
        this.logPrefix = this.constructor.name;
        this.validationService = new ValidationService();
        _.forEach(components.schemas, (schema, title) => {
            this.validationService.registerSchemaWithTitle(schema, title);
        });
    }

    public static async deleteRentalTermsWithAccountId(accountId: string): Promise<void> {
        const rentalTermCollection = await MongoService.getCollection(RentalTermService.collectionName);
        await rentalTermCollection.deleteMany({accountId});
    }

    public async deleteRentalTermWithId(rentalTermId: string): Promise<void> {
        const rentalTermCollection = await MongoService.getCollection(RentalTermService.collectionName);
        const findOneAndDeleteResult = await rentalTermCollection.findOneAndDelete({
            _id: new ObjectId(rentalTermId),
        }, {
            includeResultMetadata: true,
        });
        const accountId = _.get(findOneAndDeleteResult.value, 'accountId');
        if (_.isEmpty(accountId)) {
            throw new Error('accountId not found on deleted rentalTerm: ' +
                JSON.stringify(findOneAndDeleteResult.value));
        }

        // Remove references to this rental term in product prices and price rules
        const productPriceService = new ProductPriceService();
        const priceRuleService = new PriceRuleService();
        await Promise.all([
            productPriceService.unsetRentalTermIdForAccountId(rentalTermId, accountId),
            priceRuleService.unsetRentalTermIdForAccountId(rentalTermId, accountId),
        ]);
    }

    public async queryRentalTerm(
        query: { accountId?: string; ids?: string[] },
    ): Promise<RentalTerm[]> {
        const filterQuery: Filter<RentalTerm> = _.pick(query, ['accountId']);

        if (!_.isEmpty(query.ids)) {
            // eslint-disable-next-line no-underscore-dangle
            filterQuery._id = {
                $in: _.map(query.ids, (id) => new ObjectId(id)),
            };
        }

        if (_.isEmpty(filterQuery)) {
            throw new HttpError('You must specify accountId and/or ids when querying Rental Terms', 422);
        }

        const rentalTermCollection = await MongoService.getCollection(RentalTermService.collectionName);
        return MongoHelperService.convertOidToId<RentalTerm & { _id: ObjectId }>(
            await rentalTermCollection.find<RentalTerm & { _id: ObjectId }>(filterQuery, {
                ...MongoHelperService.getDefaultReadPreference(),
                projection: {
                    order: 0,
                },
                sort: {
                    order: 1,
                },
            }).toArray(),
        );
    }

    public async saveRentalTerm(rentalTerm: RentalTerm): Promise<{ id: string; priceElementChangeJobId?: string }> {
        await this.validateRentalTerm(rentalTerm);
        const rentalTermCollection = await MongoService.getCollection(RentalTermService.collectionName);
        let id = rentalTerm.id;
        let priceElementChangeJobId: string;
        if (id) {

            // Check existing Rental Term values
            const elementBeforeChange = _.first(await this.queryRentalTerm({ids: [id]}));

            // Return not found error if there is not an existing element found
            if (_.isEmpty(elementBeforeChange)) {
                throw new HttpError(`Rental Term with ID: ${id} is not found`, 404);
            }

            // Update
            delete rentalTerm.id;
            const findOneAndUpdateResult = await rentalTermCollection.findOneAndUpdate({
                _id: new ObjectId(id),
            }, {
                $set: rentalTerm,
            }, {
                ...MongoHelperService.getDefaultWriteConcern(),
                includeResultMetadata: true,
                projection: {
                    _id: false,
                },
                returnDocument: 'after',
            });
            const elementAfterChange = findOneAndUpdateResult.value;
            elementAfterChange.id = id;

            // If there is a change in the installments - we must re-calculate rental prices
            if (_.get(elementBeforeChange, 'installments') !== _.get(elementAfterChange, 'installments')) {
                priceElementChangeJobId = await PriceElementChangeService.queuePriceElementChangeJob({
                    accountId: elementAfterChange.accountId,
                    elementAfterChange,
                    elementBeforeChange,
                    elementId: id,
                    elementType: 'rentalTerm',
                });
            }
        } else {

            // Increment existing rental term order
            await rentalTermCollection.updateMany({
                accountId: rentalTerm.accountId,
            }, {
                $inc: {
                    order: 1,
                },
            }, MongoHelperService.getDefaultWriteConcern());

            // Insert new rental term at the top of the list
            rentalTerm.order = 0;
            const insertResult = await rentalTermCollection.insertOne(
                rentalTerm,
                MongoHelperService.getDefaultWriteConcern(),
            );
            id = insertResult.insertedId.toString();
        }

        // Append the priceElementChangeJobId if set
        const response: { id: string; priceElementChangeJobId?: string } = {id};
        if (priceElementChangeJobId) {
            response.priceElementChangeJobId = priceElementChangeJobId;
        }

        return response;
    }

    public async saveRentalTermIdsOrder(orderedRentalTermIds: string[]): Promise<void> {
        if (_.isEmpty(orderedRentalTermIds)) {
            throw new HttpError('Ordered rental terms cannot be empty', 422);
        }

        const rentalTermCollection = await MongoService.getCollection(RentalTermService.collectionName);
        const bulkUpdates = _.map(orderedRentalTermIds, (rentalTermId, index) => ({
            updateOne: {
                filter: {_id: new ObjectId(rentalTermId)},
                update: {
                    $set: {
                        order: index,
                    },
                },
            },
        }));

        if (!_.isEmpty(bulkUpdates)) {
            await rentalTermCollection.bulkWrite(bulkUpdates, {
                ...MongoHelperService.getDefaultWriteConcern(),
                ordered: false,
            });
        }
    }

    private async validateRentalTerm(rentalTerm: RentalTerm): Promise<void> {
        const logPrefix = `${this.logPrefix}: validateRentalTerm:`;
        const schemaTitle = 'RentalTermWithoutId';
        const validationResult = await this.validationService.validate(schemaTitle, rentalTerm);
        if (!validationResult.isValid) {
            console.error(`${logPrefix} Invalid rentalTerm: `, rentalTerm, validationResult.errors);
            throw new HttpError(_.join(_.map(validationResult.errors, 'message'), '; '), 422);
        }
    }
}
