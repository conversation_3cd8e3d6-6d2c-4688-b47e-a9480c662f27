import {PriceElementChangeService} from './price-element-change.service';
import {PriceListService} from './price-list.service';
import {PriceRuleService} from './price-rule.service';
import {ProductPriceService} from './product-price.service';
import {RentalTermService} from './rental-term.service';
import {AccountSettingsService} from './account-settings.service';

export class AccountService {
    public static async deleteAllPricingDataWithAccountId(accountId: string): Promise<void> {
        const logPrefix = 'AccountService: deleteAllPricingDataWithAccountId:';

        console.log(`${logPrefix} About to delete pricing data for account: ${accountId}`);

        await Promise.all([
            AccountSettingsService.deleteWithAccountId(accountId),
            PriceElementChangeService.deletePriceElementChangeJobsWithAccountId(accountId),
            PriceListService.deletePriceListsWithAccountId(accountId),
            PriceRuleService.deletePriceRulesWithAccountId(accountId),
            ProductPriceService.deleteProductPricesWithAccountId(accountId),
            RentalTermService.deleteRentalTermsWithAccountId(accountId),
        ]);

        console.log(`${logPrefix} Successfully deleted all pricing data for account: ${accountId}`);
    }
}
