import * as _ from 'lodash';
import Axios, {AxiosRequestConfig, Method} from 'axios';
import {ErrorService} from '@wondersign/serverless-services';
import {ProductRef} from '../models/product-ref';
import {Product} from '../models/product';
import {Components} from '../models/contracts';
import {ProductPackageData} from '../models/product-package-data';
import {ProductService} from './product.service';

const config = process.env;

export class ProductSelectionService {
    private static readonly apiKey: string = config.pssApiKey;
    private static readonly logPrefix: string = 'ProductSelectionService: ';
    private static readonly url: string = config.pssUrl;

    public static async fetch(accountId: string, products: ProductRef[], fields: string[]): Promise<Product[]> {
        const maxProductFetchSize = 10000;
        let fetchedProducts: Product[] = [];
        const productBatches = _.chunk(products, maxProductFetchSize);
        for (const productBatch of productBatches) {
            fetchedProducts = fetchedProducts.concat(await this.execRequest('post', 'fetch', null, {
                accountId,
                fields,
                products: productBatch,
            }));
        }
        return fetchedProducts;
    }

    public static async fetchPackageData(accountId: string, products: ProductRef[]): Promise<ProductPackageData[]> {
        const maxPackageDataFetchSize = 5000;
        const packageDataMap: {[id: string]: ProductPackageData} = {};
        const productBatches = _.chunk(products, maxPackageDataFetchSize);
        for (const productBatch of productBatches) {
            const productRefs = _.map(productBatch, (product) => _.pick(product, ['brand', 'sku']));
            const packageData = await this.execRequest('post', 'fetch/package-data', null, {
                accountId,
                products: productRefs,
            });
            _.forEach(packageData, (productPackageData) => {
                const productKey = ProductService.getProductKey(productPackageData);
                packageDataMap[productKey] = productPackageData;
            });
        }
        return _.values(packageDataMap);
    }

    public static async match(
        accountId: string,
        products: ProductRef[],
        selectionGroupsOptions: Array<{
            id: string;
            isSelectAll?: boolean;
            selectionGroups: Components['schemas']['PriceRuleWithId']['productSelectionCriteria'];
            selectionGroupsOperator: Components['schemas']['PriceRuleWithId']['productSelectionCriteriaOperator'];
        }>,
        useProductsAsProvided = false,
    ): Promise<Array<ProductRef & { selectionId: string }>> {
        const maxProductMatchSize = 10000;
        let matches: Array<ProductRef & { selectionId: string }> = [];
        const productBatches = _.chunk(products, maxProductMatchSize);
        for (const productBatch of productBatches) {
            matches = matches.concat(await this.execRequest('post', 'match', null, {
                accountId,
                products: productBatch,
                selectionGroupsOptions,
                useProductsAsProvided,
            }));
        }
        return matches;
    }

    private static async execRequest(
        method: Method,
        urlPath: string,
        queryParams?: { [id: string]: any },
        body?: any,
    ): Promise<any> {
        const logPrefix = this.logPrefix + 'execRequest: ';
        const requestOptions: AxiosRequestConfig = {
            headers: {
                // eslint-disable-next-line @typescript-eslint/naming-convention
                'x-api-key': this.apiKey,
            },
            method,
            timeout: 1000 * 30, // Wait no more than 30 seconds for the response
            url: `${this.url}/${urlPath}`,
        };

        if (queryParams) {
            _.extend(requestOptions, {
                params: queryParams,
            });
        }

        if (body) {
            _.extend(requestOptions, {
                data: body,
            });
        }

        console.debug(`${logPrefix} Fetching with request`, requestOptions);
        try {
            const response = await Axios(requestOptions);
            console.debug(`${logPrefix} Response for request`, requestOptions, response.data);
            return response.data;
        } catch (error) {
            if (error?.response) {
                console.error(`${logPrefix} Request failed with response`, requestOptions, error.response);
                throw ErrorService.getHttpErrorForResponse({
                    body: error.response.data,
                    statusCode: error.response.status,
                });
            } else {
                console.error(`${logPrefix} Request failed without response`, requestOptions, error);
                throw (error || new Error());
            }
        }
    }
}
