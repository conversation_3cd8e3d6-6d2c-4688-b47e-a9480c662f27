import {ObjectId} from 'mongodb';
import * as _ from 'lodash';

// TODO: CK-6569 - Migrate this to MongoService from Serverless Services
export class MongoHelperService {
    public static convertOidToId<T extends { _id: ObjectId }>(
        mongoObjects: T[],
    ): Array<Omit<T, '_id'> & { id: string }> {
        return _.map(mongoObjects, (mongoObject) => _.extend(
            {
                // eslint-disable-next-line no-underscore-dangle
                id: mongoObject._id.toString(),
            },
            _.omit(mongoObject, '_id'),
        ));
    }

    public static getDefaultReadPreference(): { readPreference: 'secondaryPreferred' } {
        return {
            readPreference: 'secondaryPreferred',
        };
    }

    public static getDefaultWriteConcern(): {writeConcern: {j: boolean; w: 'majority'}} {
        return {
            writeConcern: {
                j: true,
                w: 'majority',
            },
        };
    }
}

