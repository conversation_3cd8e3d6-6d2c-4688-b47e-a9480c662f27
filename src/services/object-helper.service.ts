import * as _ from 'lodash';

export class ObjectHelperService {
    public static removeNil<T>(obj: T): T {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        return Object.fromEntries(
            Object.entries(obj)
                // eslint-disable-next-line @typescript-eslint/no-unused-vars
                .filter(([_k, v]) => !_.isNil(v))
                .map(([k, v]) => [k, v === Object(v) && !Array.isArray(v) ? this.removeNil(v) : v])
        );
    }
}
