import * as _ from 'lodash';
import {ProductPackageData} from '../models/product-package-data';
import {ProductRef} from '../models/product-ref';
import {ProductService} from './product.service';

export class ProductPackageService {

    public static getUniqueComponentRefsFromPackageProducts(packageProductData: ProductPackageData[]): ProductRef[] {
        const componentRefsMap: {[id: string]: ProductRef} = {};

        _.forEach(packageProductData, (packageProduct) => {
            const productRefProperties = ['brand', 'sku'];

            _.forEach(packageProduct.components, (component) => {
                componentRefsMap[ProductService.getProductKey(component)] = _.pick(
                    component,
                    productRefProperties,
                ) as ProductRef;
            });
        });

        return _.values(componentRefsMap);
    }

    public extractPackageProducts(productRefs: ProductRef[], packageData: ProductPackageData[]): ProductPackageData[] {
        return _.values(this.getPackageProductRefMap(productRefs, packageData));
    }

    public filterPackageProducts(productRefs: ProductRef[], packageData: ProductPackageData[]): ProductPackageData[] {
        const packageProductRefMap = this.getPackageProductRefMap(productRefs, packageData);

        // Filter package products out of the product refs
        _.remove(productRefs, (productRef) => !!packageProductRefMap[ProductService.getProductKey(productRef)]);

        return _.values(packageProductRefMap);
    }

    public getPackageProductsWithComponents(
        componentRefs: ProductRef[],
        packageData: ProductPackageData[],
    ): ProductPackageData[] {
        const packageDataMap = this.getPackageDataMap(packageData);
        const packageProductMap: { [id: string]: ProductPackageData } = {};
        _.forEach(componentRefs, (componentRef) => {
            const componentKey = ProductService.getProductKey(componentRef);
            const componentPackageData = packageDataMap[componentKey];
            if (!_.get(componentPackageData, 'isComponent')) {
                return true;
            }

            _.forEach(componentPackageData.packages, (packageProduct) => {
                const packageProductKey = ProductService.getProductKey(packageProduct);
                const packageProductPackageData = packageDataMap[packageProductKey];
                if (!packageProductPackageData) {
                    return true;
                }
                packageProductMap[packageProductKey] = packageProductPackageData;
            });
        });

        return _.values(packageProductMap);
    }

    private getPackageDataMap(packageData: ProductPackageData[]): { [id: string]: ProductPackageData } {
        const packageDataMap: { [id: string]: ProductPackageData } = {};
        _.forEach(packageData, (productPackageData) => {
            const productKey = ProductService.getProductKey(productPackageData);
            packageDataMap[productKey] = productPackageData;
        });
        return packageDataMap;
    }

    private getPackageProductRefMap(
        productRefs: ProductRef[],
        packageData: ProductPackageData[]
    ): { [id: string]: ProductPackageData } {
        const packageDataMap = this.getPackageDataMap(packageData);
        const packageProductRefMap = {};

        // Build a map of product key => package data for package products
        _.forEach(productRefs, (productRef) => {
            const productKey = ProductService.getProductKey(productRef);
            const productPackageData = packageDataMap[productKey];
            if (_.get(productPackageData, 'isPackage')) {
                packageProductRefMap[productKey] = productPackageData;
            }
        });

        return packageProductRefMap;
    }
}
