import {HttpError, MongoService, ValidationService} from '@wondersign/serverless-services';
import * as _ from 'lodash';
import {components} from '../openapi/spec.json';
import {AccountSettings, PackageCalculationMethod} from '../models/account-settings';
import {MongoHelperService} from './mongo-helper.service';
import {PriceElementChangeService} from './price-element-change.service';

export class AccountSettingsService {
    private static collectionName = 'accountSettings';
    private static readonly schemaPath = '#/components/schemas/';
    private static validationService: ValidationService;

    public static async deleteWithAccountId(accountId: string): Promise<void> {
        const accountSettingsCollection = await MongoService.getCollection(this.collectionName);
        await accountSettingsCollection.deleteOne({
            accountId,
        });
    }

    public static async getForAccountId(accountId: string): Promise<AccountSettings> {
        const accountSettingsCollection = await MongoService.getCollection<AccountSettings>(this.collectionName);
        return accountSettingsCollection.findOne(
            {accountId},
            {
                ...MongoHelperService.getDefaultReadPreference(),
                projection: {
                    _id: 0,
                },
            },
        );
    }

    public static havePricesBeenDisabled(
        accountSettingsBefore: AccountSettings,
        accountSettingsAfter: AccountSettings,
    ): boolean {
        return (
            (
                (accountSettingsBefore?.listPricingEnabled ?? true) === true &&
                accountSettingsAfter?.listPricingEnabled === false
            ) ||
            (
                (accountSettingsBefore?.rentalPricingEnabled ?? true) === true &&
                accountSettingsAfter?.rentalPricingEnabled === false
            )
        );
    }

    public static havePricesBeenEnabled(
        accountSettingsBefore: AccountSettings,
        accountSettingsAfter: AccountSettings,
    ): boolean {
        return (
            (
                (accountSettingsBefore?.listPricingEnabled ?? true) === false &&
                accountSettingsAfter?.listPricingEnabled === true
            ) ||
            (
                (accountSettingsBefore?.rentalPricingEnabled ?? true) === false &&
                accountSettingsAfter?.rentalPricingEnabled === true
            )
        );
    }

    public static isPackageRepricingRequired(
        accountSettingsBefore: AccountSettings,
        accountSettingsAfter: AccountSettings,
    ): boolean {
        if (_.get(accountSettingsBefore, 'packagedProductPriceCalculationMethod') !==
            _.get(accountSettingsAfter, 'packagedProductPriceCalculationMethod')
        ) {
            return true;
        }

        const sumPackageProductPrices = _.get(accountSettingsAfter, 'packagedProductPriceCalculationMethod')
            === PackageCalculationMethod.mergedPrice;

        if (!sumPackageProductPrices) {
            return false;
        }

        return _.reduce(
            [
                'packagedProductPriceRound',
                'packagedProductShowPriceIfComponentZero',
            ],
            (acc, property) => acc || _.get(accountSettingsBefore, property) !== _.get(accountSettingsAfter, property),
            false
        );
    }

    public static async save(accountSettings: AccountSettings): Promise<{ priceElementChangeJobId?: string }> {
        if (!accountSettings) {
            throw new HttpError('accountSettings must be provided', 422);
        }
        await this.validateAccountSettings(accountSettings);
        const accountSettingsCollection = await MongoService.getCollection<AccountSettings>(this.collectionName);
        const findOneAndReplaceResult = await accountSettingsCollection.findOneAndReplace(
            {
                accountId: accountSettings.accountId,
            },
            accountSettings,
            {
                ...MongoHelperService.getDefaultWriteConcern(),
                includeResultMetadata: true,
                projection: {
                    _id: 0,
                },
                returnDocument: 'before',
                upsert: true,
            },
        );

        const elementBeforeChange = findOneAndReplaceResult.value;
        let priceElementChangeJobId;
        if (this.isPackageRepricingRequired(elementBeforeChange, accountSettings) ||
            this.havePricesBeenDisabled(elementBeforeChange, accountSettings) ||
            this.havePricesBeenEnabled(elementBeforeChange, accountSettings)
        ) {
            priceElementChangeJobId = await PriceElementChangeService.queuePriceElementChangeJob({
                accountId: accountSettings.accountId,
                elementAfterChange: accountSettings,
                elementBeforeChange,
                elementType: 'accountSettings',
            });
            return {priceElementChangeJobId};
        } else {
            return {};
        }
    }

    private static initValidationService(): void {
        if (this.validationService) {
            return;
        }

        this.validationService = new ValidationService();
        _.forEach(components.schemas, (schema, title) => {
            this.validationService.registerSchemaWithTitle(schema, this.schemaPath + title);
        });
    }

    private static async validateAccountSettings(accountSettings: AccountSettings): Promise<void> {
        const logPrefix = 'AccountSettingsService: validateAccountSettings:';
        this.initValidationService();
        const validationResult = await this.validationService.validate(
            this.schemaPath + 'AccountSettings',
            accountSettings,
        );
        if (!validationResult.isValid) {
            console.error(`${logPrefix} Invalid accountSettings: `, accountSettings, validationResult.errors);
            throw new HttpError(_.join(
                _.map(
                    validationResult.errors,
                    (error) => `accountSettings ${_.get(error, 'dataPath')} ${_.get(error, 'message')}`,
                ),
                '; ',
            ), 422);
        }
    }
}
