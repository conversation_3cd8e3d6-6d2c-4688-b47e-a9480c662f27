import {AwsSnsService, HttpError, MongoService, ValidationService} from '@wondersign/serverless-services';
import * as _ from 'lodash';
import {Filter, ObjectId} from 'mongodb';
import * as moment from 'moment';
import {components} from '../openapi/spec.json';
import {PriceList} from '../models/price-list';
import {PriceRule} from '../models/price-rule';
import {Components} from '../models/contracts';
import {PromotionalPriceListEvent} from '../models/promotional-price-list-event';
import {PromotionalPriceListStatus} from '../models/promotional-price-list-status';
import {PromotionalPriceListScheduleEdgeTypes} from '../models/promotional-price-list-schedule-edge-types';
import {PriceElementChangeService} from './price-element-change.service';
import {ObjectHelperService} from './object-helper.service';
import {PriceRuleService} from './price-rule.service';
import {MongoHelperService} from './mongo-helper.service';
import {NameCloneService} from './name-clone.service';
import {DateTimeService} from './date-time.service';
import {ProductPriceService} from './product-price.service';

const config = process.env;

export class PriceListService {
    private static readonly collectionName: string = 'priceList';
    private static readonly promoStateCollectionName: string = 'promoPriceListState';

    private readonly logPrefix: string;
    private validationService: ValidationService;

    public constructor() {
        this.logPrefix = this.constructor.name;
        this.validationService = new ValidationService();
        _.forEach(components.schemas, (schema, title) => {
            this.validationService.registerSchemaWithTitle(schema, title);
        });
    }

    public static async checkPromoPriceListSchedules(): Promise<void> {

        // Get Price Lists about to start
        const priceListsWithRecentActions = await this.getPromoPriceListsStartingOrEndingForAnyTimezone();

        // Get supported timezones
        const timezonesGroupedByOffset = await DateTimeService.getTimezonesGroupedByOffset();

        const currentTime = moment.utc().format();
        const snsEvents: PromotionalPriceListEvent[] = [];

        // Check the promo states to determine which price lists will need to be queued for price calculations
        const promoStateCollection = await MongoService.getCollection(PriceListService.promoStateCollectionName);
        const promoStates = await promoStateCollection.find({
            priceListId: {
                $in: _.map(priceListsWithRecentActions, (priceList) => priceList._id.toString()),
            },
        }).toArray();
        const promoStateMap = _.keyBy(promoStates, 'priceListId');
        const priceListsToQueueForCalculating: PriceList[] = [];

        for (const priceList of priceListsWithRecentActions) {
            const priceListId = priceList._id.toString();
            priceList.id = priceListId;
            delete priceList._id;

            // If the price list does not have valid schedule, ignore it
            if (!this.isValidSchedule(priceList.schedule)) {
                continue;
            }

            _.forEach(timezonesGroupedByOffset, (timezonesAndOffset) => {
                const snsEvent = this.getPromoPriceListSnsEvent(priceList, timezonesAndOffset, currentTime);

                if (snsEvent) {
                    snsEvents.push(snsEvent);
                }
            });

            // Check for price lists that do not have prices and are not yet queued
            if (!_.get(promoStateMap, `${priceListId}.arePricesCalculated`) &&
                !_.get(promoStateMap, `${priceListId}.isQueued`)
            ) {
                priceListsToQueueForCalculating.push(priceList);
            }
        }

        // Send SNS events for start and end
        if (!_.isEmpty(snsEvents)) {
            await AwsSnsService.sendSnsMessage(
                snsEvents,
                config.promoPriceListActionTopic,
                'Promo Price List start / end',
                config.awsAccountId
            );
        }

        // Queue the priceLists that are not yet queued
        if (!_.isEmpty(priceListsToQueueForCalculating)) {
            await promoStateCollection.insertMany(_.map(priceListsToQueueForCalculating, (priceList) => ({
                arePricesCalculated: false,
                isQueued: true,
                priceListId: priceList.id,
                pricesExpireAt: PriceListService.getExpirationDateForPromoPriceList(priceList),
            })));

            for (const priceList of priceListsToQueueForCalculating) {
                await PriceElementChangeService.queuePriceElementChangeJob({
                    accountId: priceList.accountId,
                    elementAfterChange: priceList,
                    elementBeforeChange: priceList,
                    elementId: priceList.id,
                    elementType: 'promoPriceListCalculation',
                });
            }
        }
    }

    public static async deletePriceListsWithAccountId(accountId: string): Promise<void> {
        const priceListCollection = await MongoService.getCollection(PriceListService.collectionName);
        await priceListCollection.deleteMany({accountId});
    }

    public static getExpirationDateForPromoPriceList(priceList: PriceList): Date {
        if (!this.isValidScheduleEdge(_.get(priceList, 'schedule'), PromotionalPriceListScheduleEdgeTypes.end)) {
            console.warn('PriceListService: getExpirationDateForPromoPriceList: invalid pricelist schedule', priceList);
            return new Date();
        }

        const utcString = priceList.schedule.endDate +
            'T' +
            DateTimeService.get24HourWithMeridiem(
                priceList.schedule.endTimeHour,
                priceList.schedule.endTimeMeridiem,
            ) +
            ':' +
            priceList.schedule.endTimeMinute +
            ':00Z';
        return moment.utc(utcString).toDate();
    }

    public static getPromoPriceListStatus(priceList: PriceList, timezone?: string): PromotionalPriceListStatus {
        if (priceList.type !== 'promotional') {
            return null;
        }

        if (!timezone) {
            timezone = 'utc';
        }

        if (!this.isValidSchedule(priceList.schedule)) {
            return PromotionalPriceListStatus.inactive;
        }

        // Get current time in UTC
        const currentMoment = moment.utc();

        // Get start date in UTC
        const schedule = priceList.schedule;
        const startTimestamp = schedule.startDate + ' ' +
            DateTimeService.get24HourWithMeridiem(schedule.startTimeHour, schedule.startTimeMeridiem) + ':' +
            schedule.startTimeMinute;
        const startMoment = moment.utc(DateTimeService.convertRelativeTimestampToDate(startTimestamp, timezone));

        // Get end date in UTC
        const endTimestamp = schedule.endDate + ' ' +
            DateTimeService.get24HourWithMeridiem(schedule.endTimeHour, schedule.endTimeMeridiem) + ':' +
            schedule.endTimeMinute;
        const endMoment = moment.utc(DateTimeService.convertRelativeTimestampToDate(endTimestamp, timezone));

        let status: PromotionalPriceListStatus = PromotionalPriceListStatus.inactive;

        if (startMoment.isBefore(currentMoment) && endMoment.isAfter(currentMoment)) {
            status = PromotionalPriceListStatus.active;
        } else if (startMoment.isAfter(currentMoment) && endMoment.isAfter(currentMoment)) {
            status = PromotionalPriceListStatus.planned;
        }

        return status;
    }

    private static getPromoPriceListSnsEvent(
        priceList: PriceList,
        timezonesAndOffset: { accountIds: string[]; offset: string; timezones: string[] },
        currentTime: string,
    ): PromotionalPriceListEvent {
        const accountIds = timezonesAndOffset.accountIds;
        const timezones = timezonesAndOffset.timezones;

        // If the Account does not request pricing for this timezone, skip the check
        if (!_.includes(accountIds, priceList.accountId)) {
            return;
        }

        // If we don't have a valid schedule, ignore the price list
        if (!this.isValidSchedule(priceList.schedule)) {
            return;
        }

        // Check for price list start for this timezone
        if (DateTimeService.isRelativeTimeMatchForUtcTimestamp(
            priceList.schedule.startDate,
            DateTimeService.get24HourWithMeridiem(
                priceList.schedule.startTimeHour,
                priceList.schedule.startTimeMeridiem,
            ),
            priceList.schedule.startTimeMinute,
            _.first(timezones),
            currentTime,
        )) {
            console.log('Promo Price List start for timezone', priceList, timezonesAndOffset);
            return {
                accountId: priceList.accountId,
                action: 'start',
                priceListId: priceList.id,
                timezones,
            };
        }

        // Check for price list end for this timezone
        if (DateTimeService.isRelativeTimeMatchForUtcTimestamp(
            priceList.schedule.endDate,
            DateTimeService.get24HourWithMeridiem(
                priceList.schedule.endTimeHour,
                priceList.schedule.endTimeMeridiem,
            ),
            priceList.schedule.endTimeMinute,
            _.first(timezones),
            currentTime,
        )) {
            console.log('Promo Price List end for timezone', priceList, timezonesAndOffset);
            return {
                accountId: priceList.accountId,
                action: 'end',
                priceListId: priceList.id,
                timezones,
            };
        }
    }

    private static async getPromoPriceListsStartingOrEndingForAnyTimezone(): Promise<PriceList[]> {
        const priceListCollection = await MongoService.getCollection(PriceListService.collectionName);
        const currentRelativeDates = [];

        // Time zone ranges between UTC-12 to UTC+14
        const yearMonthDayFormat = 'Y-MM-DD';
        const currentTimestamp = moment.utc().format(yearMonthDayFormat);
        const earliestTimestamp = moment.utc().subtract(12, 'hour').format(yearMonthDayFormat);
        const latestTimestamp = moment.utc().add(14, 'hour').format(yearMonthDayFormat);

        _.forEach([currentTimestamp, earliestTimestamp, latestTimestamp], (timestamp) => {
            if (!_.includes(currentRelativeDates, timestamp)) {
                currentRelativeDates.push(timestamp);
            }
        });

        return priceListCollection.find({
            $or: [
                {
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    'schedule.startDate': {
                        $in: currentRelativeDates,
                    },
                },
                {
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    'schedule.endDate': {
                        $in: currentRelativeDates,
                    },
                },
            ],
        }, {
            ...MongoHelperService.getDefaultReadPreference(),
            projection: {},
        }).toArray();
    }

    private static async getUniqueName(
        providedName: string,
        accountId: string,
        excludePriceListId?: string,
    ): Promise<string> {
        const query: any = {
            accountId,
        };

        if (excludePriceListId) {
            // eslint-disable-next-line no-underscore-dangle
            query._id = {
                $ne: new ObjectId(excludePriceListId),
            };
        }

        const priceListCollection = await MongoService.getCollection(PriceListService.collectionName);
        const existingPriceLists = await priceListCollection.find(query, {
            ...MongoHelperService.getDefaultReadPreference(),
            projection: {
                _id: 0,
                name: 1,
            },
        }).toArray();

        return NameCloneService.getUniqueNameOptionallyCloned(providedName, _.map(existingPriceLists, 'name'));
    }

    private static isValidSchedule(schedule: PriceList['schedule']): boolean {
        if (_.isEmpty(schedule)) {
            return false;
        }

        for (const edge in PromotionalPriceListScheduleEdgeTypes) {
            if (!this.isValidScheduleEdge(schedule, PromotionalPriceListScheduleEdgeTypes[edge])) {
                return false;
            }
        }

        return true;
    }

    private static isValidScheduleEdge(
        schedule: PriceList['schedule'],
        edge: PromotionalPriceListScheduleEdgeTypes,
    ): boolean {
        if (_.isEmpty(schedule)) {
            return false;
        }

        const edgeDate: string = _.get(schedule, edge + 'Date');
        const edgeTimeHour: string = _.get(schedule, edge + 'TimeHour', '');
        const edgeTimeMinute: string = _.get(schedule, edge + 'TimeMinute');
        const edgeTimeMeridiem: 'am' | 'pm' = _.get(schedule, edge + 'TimeMeridiem');

        if (_.isEmpty(edgeTimeMeridiem)) {
            return false;
        }

        const edgeUtcDateString = edgeDate +
            'T' +
            DateTimeService.get24HourWithMeridiem(
                edgeTimeHour,
                edgeTimeMeridiem,
            ) +
            ':' +
            edgeTimeMinute +
            ':00Z';

        try {
            const edgeUtcDate = moment.utc(edgeUtcDateString).toDate();
            return (edgeUtcDate instanceof Date && !isNaN(edgeUtcDate.getTime()));
        } catch (err) {
            console.warn('Invalid date provided: ', edgeUtcDateString, err);
            return false;
        }
    }

    private static async setOrderForNewPriceList(priceList: PriceList): Promise<void> {
        if (priceList.type !== 'promotional') {
            return;
        }

        // Increment existing promo price list order
        const priceListCollection = await MongoService.getCollection(PriceListService.collectionName);
        await priceListCollection.updateMany({
            accountId: priceList.accountId,
            type: 'promotional',
        }, {
            $inc: {
                order: 1,
            },
        }, MongoHelperService.getDefaultWriteConcern());

        // Insert new promo price list at the top of the list
        priceList.order = 0;
    }

    private static async updatePromoStateExpirationDateIfChanged(
        priceListAfter: PriceList,
        priceListBefore: PriceList,
    ) {
        const endTimestampFields = [
            'schedule.endDate',
            'schedule.endTimeHour',
            'schedule.endTimeMeridiem',
            'schedule.endTimeMinute',
        ];
        if (priceListAfter.type === 'promotional' &&
            !_.isEqual(
                _.pick(priceListBefore, endTimestampFields),
                _.pick(priceListAfter, endTimestampFields),
            )
        ) {
            const promoStateCollection = await MongoService.getCollection(
                PriceListService.promoStateCollectionName,
            );

            if (this.isValidScheduleEdge(
                _.get(priceListAfter, 'schedule'),
                PromotionalPriceListScheduleEdgeTypes.end)
            ) {
                const pricesExpireAt = PriceListService.getExpirationDateForPromoPriceList(priceListAfter);

                // Update the Price List Promo State
                await promoStateCollection.updateOne({priceListId: priceListAfter.id}, {
                    $set: {
                        pricesExpireAt,
                    },
                }, MongoHelperService.getDefaultWriteConcern());

                // Update the product prices themselves
                await ProductPriceService.updateExpirationDateForPriceListId(priceListAfter.id, pricesExpireAt);
            } else {
                await promoStateCollection.deleteOne(
                    {priceListId: priceListAfter.id},
                    MongoHelperService.getDefaultWriteConcern()
                );
            }
        }
    }

    public async clonePriceList(
        cloneConfig: Components['schemas']['PriceListCloneConfig'],
    ): Promise<{ id: string; priceElementChangeJobId?: string }> {
        const accountId = _.get(cloneConfig, 'accountId');
        if (!accountId) {
            throw new HttpError('Missing required property: accountId', 422);
        }

        const clonePropertyMap = _.get(cloneConfig, 'clonePropertyMap', {});
        if (_.isEmpty(clonePropertyMap)) {
            throw new HttpError('clonePropertyMap must have at least 1 property defined', 422);
        }

        // Fetch price lists referenced by map
        let priceRuleSourcePriceListId: string;
        const priceListIds = _.uniq(_.filter(_.map(clonePropertyMap, (priceListId, field) => {
            if (field === 'priceRuleIds') {
                priceRuleSourcePriceListId = priceListId;
            }

            return priceListId;
        })));

        const priceListMap: { [id: string]: PriceList } = {};
        if (!_.isEmpty(priceListIds)) {
            const priceLists = await this.queryPriceList({ids: priceListIds});
            _.forEach(priceLists, (existingPriceList) => {
                priceListMap[existingPriceList.id] = existingPriceList;
            });
        }

        // Assign base properties
        const priceList: PriceList = {
            accountId,
        };

        // Assign values from clonePropertyMap
        _.forOwn(clonePropertyMap, (priceListId, field) => {
            if (field === 'priceRuleIds') {
                return;
            }

            priceList[field] = _.get(priceListMap[priceListId], field);
        });

        // Apply overrides
        _.extend(priceList, cloneConfig.newPriceListOverride);

        // If price rule Ids are specified, they will have to be cloned
        const sourcePriceRuleMap: { [id: string]: PriceRule } = {};
        const priceRuleService = new PriceRuleService();
        const sourcePriceRuleIds = _.get(priceListMap[priceRuleSourcePriceListId], 'priceRuleIds', []);
        if (!_.isEmpty(sourcePriceRuleIds)) {
            const sourcePriceRules = await priceRuleService.queryPriceRule({ids: sourcePriceRuleIds});
            _.forEach(sourcePriceRules, (sourcePriceRule) => {
                sourcePriceRuleMap[sourcePriceRule.id] = sourcePriceRule;
            });
        }

        // Save this price list
        const response = await this.savePriceList(priceList);

        // Save each price rule (in reverse since they are continually added to the top of the Price List)
        for (const sourcePriceRuleId of sourcePriceRuleIds.reverse()) {
            const sourcePriceRule = sourcePriceRuleMap[sourcePriceRuleId];
            if (_.isEmpty(sourcePriceRule)) {
                return;
            }

            const newPriceRule = _.cloneDeep(sourcePriceRule);
            delete newPriceRule.id;
            newPriceRule.priceListId = response.id;
            await priceRuleService.savePriceRule(newPriceRule);
        }

        return response;
    }

    public async deletePriceListWithId(priceListId: string): Promise<void> {
        const priceListCollection = await MongoService.getCollection(PriceListService.collectionName);
        await Promise.all([
            priceListCollection.deleteOne({
                _id: new ObjectId(priceListId),
            }),
            PriceRuleService.deletePriceRulesWithPriceListId(priceListId),
            ProductPriceService.deleteProductPricesWithPriceListId(priceListId),
        ]);
    }

    public async hasPromoPricesCalculated(priceListId: string): Promise<boolean> {
        const promoStateCollection = await MongoService.getCollection(PriceListService.promoStateCollectionName);
        const promoState = await promoStateCollection.findOne({priceListId}, {
            ...MongoHelperService.getDefaultReadPreference(),
            projection: {
                arePricesCalculated: 1,
            },
        });

        return _.get(promoState, 'arePricesCalculated', false);
    }

    public isActivePromotionalPriceList(priceList: PriceList, timezone: string): boolean {
        if (priceList.type !== 'promotional') {
            return false;
        }

        return PriceListService.getPromoPriceListStatus(priceList, timezone) === PromotionalPriceListStatus.active;
    }

    public isRematchingRequired(priceListBefore: PriceList, priceListAfter: PriceList): boolean {
        return !_.isEqual(
            _.get(priceListAfter, 'priceRuleIds'),
            _.get(priceListBefore, 'priceRuleIds'),
        ) && !_.isEmpty(priceListAfter);
    }

    public async markPromoPricesCalculated(priceListId: string): Promise<void> {
        const promoStateCollection = await MongoService.getCollection(PriceListService.promoStateCollectionName);
        await promoStateCollection.updateOne({
            priceListId,
        }, {
            $set: {
                arePricesCalculated: true,
                isQueued: false,
            },
        });
    }

    public async prependPriceRuleIdForPriceList(priceRuleId: string, priceListId: string): Promise<void> {
        const priceListCollection = await MongoService.getCollection<PriceList>(PriceListService.collectionName);
        await priceListCollection.updateOne({
            _id: new ObjectId(priceListId),
        }, {
            $push: {
                priceRuleIds: {
                    $each: [priceRuleId],
                    $position: 0,
                },
            },
        });
    }

    public async pullPriceRuleIdForPriceList(priceRuleId: string, priceListId: string): Promise<void> {
        const priceListCollection = await MongoService.getCollection<PriceList>(PriceListService.collectionName);
        await priceListCollection.updateOne({
            _id: new ObjectId(priceListId),
        }, {
            $pull: {
                priceRuleIds: priceRuleId,
            },
        });
    }

    public async queryPriceList(
        query: { accountId?: string; ids?: string[]; type?: PriceList['type'] },
        includeAllProperties = false,
    ): Promise<PriceList[]> {
        if (_.isEmpty(query) || (!query.accountId && !query.ids)) {
            throw new HttpError('You must specify either ids or accountId when querying Price Lists', 422);
        }
        const filterQuery: Filter<PriceList> = _.pick(query, ['accountId', 'type']);
        if (query.ids) {
            // eslint-disable-next-line no-underscore-dangle
            filterQuery._id = {
                $in: _.map(query.ids, (id) => new ObjectId(id)),
            };
        }

        // Exclude order unless all properties are requested
        let projection = {};
        if (!includeAllProperties) {
            projection = {
                order: 0,
            };
        }

        const priceListCollection = await MongoService.getCollection(PriceListService.collectionName);
        return MongoHelperService.convertOidToId<PriceList & { _id: ObjectId }>(
            await priceListCollection.find<PriceList & { _id: ObjectId }>(filterQuery, {
                ...MongoHelperService.getDefaultReadPreference(),
                projection,
                sort: {
                    order: 1,
                },
            }).toArray(),
        );
    }

    public async savePriceList(
        priceList: PriceList | Partial<PriceList>,
        isPartial = false,
    ): Promise<{ id: string; name: string; priceElementChangeJobId?: string }> {

        // If this is a replace (non-partial) - validate the priceRule as provided
        if (!isPartial) {
            priceList = ObjectHelperService.removeNil(priceList);
            await this.validatePriceList(priceList);
        }

        const priceListCollection = await MongoService.getCollection(PriceListService.collectionName);
        let id = priceList.id;
        let priceElementChangeJobId;
        if (id) {

            // Track the element before the change
            const elementBeforeChange = _.first(await this.queryPriceList({ids: [id]}, true));

            if (_.isEmpty(elementBeforeChange)) {
                throw new HttpError(`Price List with ID: ${id} not found`, 404);
            }

            // Ensure name uniqueness
            if (priceList.name) {
                priceList.name = await PriceListService.getUniqueName(
                    priceList.name,
                    elementBeforeChange.accountId,
                    id,
                );
            }

            if (isPartial) {

                // Apply the partial update
                priceList = _.extend({}, elementBeforeChange, priceList);

                // Strip null values
                priceList = ObjectHelperService.removeNil(priceList);

                // Validate
                await this.validatePriceList(priceList);
            } else {

                // Preserve order (only applicable for promo price lists)
                priceList = _.extend({}, priceList, _.pick(elementBeforeChange, ['order']));
            }

            // Disallow changing of accountId
            if (elementBeforeChange.accountId !== priceList.accountId) {
                throw new HttpError('Changing Price List accountId is not permitted', 403);
            }

            // Replace
            delete priceList.id;
            const findOneAndReplaceResult = await priceListCollection.findOneAndReplace(
                {
                    _id: new ObjectId(id),
                },
                priceList,
                {
                    includeResultMetadata: true,
                    projection: {
                        _id: 0,
                    },
                    returnDocument: 'after',
                },
            );
            const elementAfterChange = findOneAndReplaceResult.value;
            elementAfterChange.id = id;

            // Queue the price change update
            if (this.isRematchingRequired(elementBeforeChange, elementAfterChange)) {
                priceElementChangeJobId = await PriceElementChangeService.queuePriceElementChangeJob({
                    accountId: elementBeforeChange.accountId,
                    elementAfterChange,
                    elementBeforeChange,
                    elementId: id,
                    elementType: 'priceList',
                });
            }

            // If this is a promo price list and the end timestamp has changed, update the expiration date
            await PriceListService.updatePromoStateExpirationDateIfChanged(elementAfterChange, elementBeforeChange);
        } else {

            // Ensure name uniqueness
            priceList.name = await PriceListService.getUniqueName(priceList.name, priceList.accountId);

            // Increment existing order for promo price list and set the order for this price list to 0
            await PriceListService.setOrderForNewPriceList(priceList);

            // Insert
            const insertResult = await priceListCollection.insertOne(priceList);
            id = insertResult.insertedId.toString();
        }

        const response: { id: string; name: string; priceElementChangeJobId?: string } = {id, name: priceList.name};
        if (priceElementChangeJobId) {
            response.priceElementChangeJobId = priceElementChangeJobId;
        }

        return response;
    }

    public async savePriceListIdsOrder(orderedPriceListIds: string[]): Promise<void> {
        if (_.isEmpty(orderedPriceListIds)) {
            throw new HttpError('Ordered price list IDs cannot be empty', 422);
        }

        const priceListCollection = await MongoService.getCollection(PriceListService.collectionName);
        const bulkUpdates = _.map(orderedPriceListIds, (priceListId, index) => ({
            updateOne: {
                filter: {_id: new ObjectId(priceListId)},
                update: {
                    $set: {
                        order: index,
                    },
                },
            },
        }));

        if (!_.isEmpty(bulkUpdates)) {
            await priceListCollection.bulkWrite(bulkUpdates, {
                ...MongoHelperService.getDefaultWriteConcern(),
                ordered: false,
            });
        }
    }

    private async validatePriceList(priceList: PriceList): Promise<void> {
        const logPrefix = `${this.logPrefix}: validatePriceList:`;
        const schemaTitle = 'PriceListWithoutId';
        const validationResult = await this.validationService.validate(schemaTitle, priceList);
        if (!validationResult.isValid) {
            console.error(`${logPrefix} Invalid priceList: `, priceList, validationResult.errors);
            throw new HttpError(_.join(_.map(validationResult.errors, 'message'), '; '), 422);
        }
    }
}
