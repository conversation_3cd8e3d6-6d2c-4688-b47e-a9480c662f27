import {HttpError, MongoService, ValidationService} from '@wondersign/serverless-services';
import * as _ from 'lodash';
import {Filter, ObjectId} from 'mongodb';
import {components} from '../openapi/spec.json';
import {PriceRule, PriceRuleArchive} from '../models/price-rule';
import {PriceListService} from './price-list.service';
import {PriceElementChangeService} from './price-element-change.service';
import {ObjectHelperService} from './object-helper.service';
import {NameCloneService} from './name-clone.service';
import {MongoHelperService} from './mongo-helper.service';

export class PriceRuleService {
    private static readonly collectionArchiveName: string = 'priceRuleArchive';
    private static readonly collectionName: string = 'priceRule';

    private readonly logPrefix: string;
    private readonly schemaPath = '#/components/schemas/';
    private validationService: ValidationService;

    public constructor() {
        this.logPrefix = this.constructor.name;
        this.validationService = new ValidationService({
        });
        _.forEach(components.schemas, (schema, title) => {
            this.validationService.registerSchemaWithTitle(schema, this.schemaPath + title);
        });
    }

    public static async deletePriceRulesWithAccountId(accountId: string): Promise<void> {
        const priceRuleCollection = await MongoService.getCollection(PriceRuleService.collectionName);
        await priceRuleCollection.deleteMany({accountId});

        const priceRuleArchiveCollection = await MongoService.getCollection(PriceRuleService.collectionArchiveName);
        await priceRuleArchiveCollection.deleteMany({accountId});
    }

    public static async deletePriceRulesWithPriceListId(priceListId: string): Promise<void> {
        const priceRuleCollection = await MongoService.getCollection(PriceRuleService.collectionName);
        const priceRuleArchiveCollection = await MongoService.getCollection(PriceRuleService.collectionArchiveName);
        await Promise.all([
            priceRuleCollection.deleteMany({priceListId}),
            priceRuleArchiveCollection.deleteMany({priceListId}),
        ]);
    }

    private static async archivePriceRule(
        action: string,
        accountId: string,
        priceListId: string,
        priceRule: PriceRule,
        priceRuleId: string,
        userEmail?: string,
    ): Promise<void> {
        const priceRuleArchive: PriceRuleArchive = {
            accountId,
            action,
            priceListId,
            priceRule,
            priceRuleId,
            timestamp: new Date(),
            userEmail,
        };
        const priceRuleArchiveCollection = await MongoService.getCollection(PriceRuleService.collectionArchiveName);
        await priceRuleArchiveCollection.insertOne(priceRuleArchive);
    }

    private static async getUniqueName(
        providedName: string,
        priceListId: string,
        excludePriceRuleId?: string,
    ): Promise<string> {
        const query: any = {
            priceListId,
        };

        if (excludePriceRuleId) {
            // eslint-disable-next-line no-underscore-dangle
            query._id = {
                $ne: new ObjectId(excludePriceRuleId),
            };
        }

        const priceRuleCollection = await MongoService.getCollection(PriceRuleService.collectionName);
        const existingPriceRules = await priceRuleCollection.find(query, {
            ...MongoHelperService.getDefaultReadPreference(),
            projection: {
                _id: 0,
                name: 1,
            },
        }).toArray();

        return NameCloneService.getUniqueNameOptionallyCloned(providedName, _.map(existingPriceRules, 'name'));
    }

    public async deletePriceRuleWithId(
        id: string,
        userEmail?: string,
    ): Promise<{ id: string; priceElementChangeJobId: string }> {
        const priceRuleCollection = await MongoService.getCollection(PriceRuleService.collectionName);

        // Track the element before the change
        const elementBeforeChange = _.first(await this.queryPriceRule({ids: [id]}));

        // Delete the PriceRule and pull the ID from the PriceList
        const priceListService = new PriceListService();
        await Promise.all([
            priceRuleCollection.deleteOne({_id: new ObjectId(id)}),
            priceListService.pullPriceRuleIdForPriceList(id, elementBeforeChange.priceListId),
            await PriceRuleService.archivePriceRule(
                'delete',
                elementBeforeChange.accountId,
                elementBeforeChange.priceListId,
                null,
                id,
                userEmail,
            ),
        ]);

        // Queue the price change update
        const priceElementChangeJobId = await PriceElementChangeService.queuePriceElementChangeJob({
            accountId: elementBeforeChange.accountId,
            elementAfterChange: null,
            elementBeforeChange,
            elementId: id,
            elementType: 'priceRule',
        });

        return {id, priceElementChangeJobId};
    }

    public isRecalculatingRequired(priceRuleBefore: PriceRule, priceRuleAfter: PriceRule): boolean {
        return !_.isEqual(
            _.get(priceRuleAfter, 'calculation'),
            _.get(priceRuleBefore, 'calculation'),
        ) && !_.isEmpty(priceRuleAfter);
    }

    public isRematchingRequired(priceRuleBefore: PriceRule, priceRuleAfter: PriceRule): boolean {
        const matchingFields = [
            'enabled',
            'productSelectionCriteria',
            'productSelectionType',
        ];
        return !_.isEqual(
            _.pick(priceRuleAfter, matchingFields),
            _.pick(priceRuleBefore, matchingFields),
        );
    }

    public async queryPriceRule(
        query: { accountId?: string; ids?: string[]; priceListIds?: string[] },
    ): Promise<PriceRule[]> {
        const priceRuleCollection = await MongoService.getCollection(PriceRuleService.collectionName);
        const filterQuery: Filter<PriceRule> = _.pick(query, ['accountId']);
        if (!_.isEmpty(query.ids)) {
            // eslint-disable-next-line no-underscore-dangle
            filterQuery._id = {
                $in: _.map(query.ids, (id) => new ObjectId(id)),
            };
        }
        if (!_.isEmpty(query.priceListIds)) {
            filterQuery.priceListId = {
                $in: query.priceListIds,
            };
        }

        return (await priceRuleCollection.find(filterQuery).toArray()).map((priceRule) => {
            // eslint-disable-next-line no-underscore-dangle
            priceRule.id = priceRule._id.toString();
            // eslint-disable-next-line no-underscore-dangle
            delete priceRule._id;
            return priceRule;
        });
    }

    public async queryPriceRuleArchive(
        query: { accountId?: string; priceRuleIds?: string[]; priceListId?: string },
        dataHandling?: {limit?: number; skip?: number}
    ): Promise<{data: PriceRule[]; totalCount: number}> {
        const priceRuleArchiveCollection = await MongoService.getCollection(PriceRuleService.collectionArchiveName);
        const filterQuery: Filter<PriceRuleArchive> = _.pick(query, ['accountId', 'priceListId']);

        if (!_.isEmpty(query.priceRuleIds)) {
            filterQuery.priceRuleId = {
                $in: query.priceRuleIds,
            };
        }

        if (_.isEmpty(filterQuery)) {
            return {data: [], totalCount: 0};
        }

        const dataHandlingOptions: {limit?: number; skip?: number} = _.pick(dataHandling, ['skip', 'limit']);
        const priceRuleArchiveCursor = priceRuleArchiveCollection.find(filterQuery, {
            ...dataHandlingOptions,
            projection: {
                _id: 0,
            },
            sort: {
                timestamp: -1,
            },
        });

        const data = await priceRuleArchiveCursor.toArray();
        let totalCount = _.size(data);

        if (_.get(dataHandlingOptions, 'limit')) {
            totalCount = await priceRuleArchiveCollection.countDocuments(filterQuery);
        }

        return {
            data,
            totalCount,
        };
    }

    public async unsetRentalTermIdForAccountId(rentalTermId: string, accountId: string): Promise<void> {
        if (_.isEmpty(rentalTermId)) {
            throw new HttpError('rentalTermId is required in order to remove a rental price rule', 422);
        }

        if (_.isEmpty(accountId)) {
            throw new HttpError('accountId is required in order to remove a rental price rule', 422);
        }

        const priceRuleCollection = await MongoService.getCollection(PriceRuleService.collectionName);
        await priceRuleCollection.updateMany({
            accountId,
        }, {
            $unset: {
                [`calculation.rental.${rentalTermId}`]: 1,
            },
        });
    }

    public async updateBrandFromProductSelectionCriteria(currentBrand: string, newBrand: string, logPrefix: string) {
        const internalLogPrefix = `${logPrefix}: priceRuleService: updateBrandFromProductSelectionCriteria`;
        const priceRuleCollection = await MongoService.getCollection(PriceRuleService.collectionName);
        const filterQuery = {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            'productSelectionCriteria.conditions': {
                $elemMatch: { field: 'brand', value: { $in: [currentBrand] } },
            },
        };

        const update = {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            $set: { 'productSelectionCriteria.$[critElem].conditions.$[condElem].value.$[valueElem]': newBrand },
        };

        // Define array filters to update the nested field
        const arrayFilters = [
            // eslint-disable-next-line @typescript-eslint/naming-convention
            { 'critElem.conditions': {$exists: true, $type: 'array'} },
            // eslint-disable-next-line @typescript-eslint/naming-convention
            { 'condElem.value': {$exists: true, $type: 'array'} },
            { valueElem: currentBrand },
        ];

        const updateResult = await priceRuleCollection.updateMany(
            filterQuery,
            update,
            { arrayFilters },
        );

        if (updateResult.matchedCount === 0) {
            const errorMessage =
                `${internalLogPrefix} No priceRule document found for productSelectionCriteria brand ${currentBrand}`;
            console.log(errorMessage);
            return;
        }

        const message = `${internalLogPrefix} updated ${updateResult.modifiedCount} with new ${newBrand} brand value`;
        console.log(message);
    }

    public async savePriceRule(
        priceRule: PriceRule | Partial<PriceRule>,
        isPartial = false,
        userEmail?: string,
    ): Promise<{ id: string; name: string; priceElementChangeJobId?: string }> {

        // If this is a replacement (non-partial) - validate the priceRule as provided
        if (!isPartial) {
            priceRule = ObjectHelperService.removeNil(priceRule);
            await this.validatePriceRule(priceRule);
        }

        const priceRuleCollection = await MongoService.getCollection(PriceRuleService.collectionName);
        let id = priceRule.id;
        let elementBeforeChange = null;
        let elementAfterChange;
        let archiveAction;
        const priceListService = new PriceListService();
        if (id) {

            // Track the element before the change
            elementBeforeChange = _.first(await this.queryPriceRule({ids: [id]}));

            if (_.isEmpty(elementBeforeChange)) {
                throw new HttpError(`Price Rule with ID: ${id} not found`, 404);
            }

            // Ensure name uniqueness
            if (priceRule.name) {
                priceRule.name = await PriceRuleService.getUniqueName(
                    priceRule.name,
                    elementBeforeChange.priceListId,
                    id,
                );
            }

            if (isPartial) {

                // Apply the partial update
                priceRule = _.extend({}, elementBeforeChange, priceRule);

                // Strip null values
                priceRule = ObjectHelperService.removeNil(priceRule);

                // Validate
                await this.validatePriceRule(priceRule);
            }

            // Disallow changing of accountId
            if (elementBeforeChange.accountId !== priceRule.accountId) {
                throw new HttpError('Changing Price Rule accountId is not permitted', 403);
            }

            // Replace
            delete priceRule.id;
            const findOneAndReplaceResult = await priceRuleCollection.findOneAndReplace(
                {
                    _id: new ObjectId(id),
                },
                priceRule,
                {
                    includeResultMetadata: true,
                    projection: {
                        _id: 0,
                    },
                    returnDocument: 'after',
                },
            );
            elementAfterChange = findOneAndReplaceResult.value;
            elementAfterChange.id = id;
            archiveAction = 'update';
        } else {

            if (isPartial) {
                throw new HttpError('Supplying a partial value for a new Price Rule is not valid', 422);
            }

            // Ensure name uniqueness
            priceRule.name = await PriceRuleService.getUniqueName(priceRule.name, priceRule.priceListId);

            // Insert
            const insertResult = await priceRuleCollection.insertOne(priceRule);
            id = insertResult.insertedId.toString();
            elementAfterChange = _.omit({
                id,
                ...priceRule,
            }, '_id');

            // Update the Price List to include this new Price Rule
            await priceListService.prependPriceRuleIdForPriceList(id, priceRule.priceListId);

            archiveAction = 'create';
        }

        const response: { id: string; name: string; priceElementChangeJobId?: string } = {
            id,
            name: elementAfterChange.name,
        };

        // Queue the price change update
        if (this.isRematchingRequired(elementBeforeChange, elementAfterChange) ||
            this.isRecalculatingRequired(elementBeforeChange, elementAfterChange)
        ) {
            response.priceElementChangeJobId = await PriceElementChangeService.queuePriceElementChangeJob({
                accountId: priceRule.accountId,
                elementAfterChange,
                elementBeforeChange,
                elementId: id,
                elementType: 'priceRule',
            });
        }

        // Add the changed price rule to the archive
        const accountId = _.get(elementAfterChange, 'accountId');
        const priceListId = _.get(elementAfterChange, 'priceListId');
        await PriceRuleService.archivePriceRule(
            archiveAction,
            accountId,
            priceListId,
            elementAfterChange,
            id,
            userEmail,
        );

        return response;
    }

    private async validatePriceRule(priceRule: PriceRule): Promise<void> {
        const logPrefix = `${this.logPrefix}: validatePriceRule:`;
        const schemaTitle = this.schemaPath + 'PriceRuleWithoutId';

        const validationResult = await this.validationService.validate(schemaTitle, priceRule);
        if (!validationResult.isValid) {
            console.error(`${logPrefix} Invalid priceRule: `, priceRule, validationResult.errors);
            throw new HttpError(_.join(
                _.map(
                    validationResult.errors,
                    (error) => `priceRule ${_.get(error, 'dataPath')} ${_.get(error, 'message')}`
                ),
                '; '
            ), 422);
        }
    }
}
