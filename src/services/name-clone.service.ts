import * as _ from 'lodash';

export class NameCloneService {

    private static readonly cloneStringForRegex = '\\- Clone (\\d+)';

    public static getUniqueNameOptionallyCloned(providedName: string, existingNames: string[]): string {
        if (!_.includes(existingNames, providedName)) {
            return providedName;
        } else {
            return this.generateCloneName(providedName, existingNames);
        }
    }

    public static generateCloneName(providedName: string, existingNames: string[]): string {
        const baseName: string = this.getBaseName(providedName);
        const cloneIndex = this.getCloneIndex(providedName, existingNames);

        // Increment by 1 and add to name
        return this.appendCloneIndexToBaseName(baseName, cloneIndex + 1);
    }

    private static appendCloneIndexToBaseName(baseName: string, cloneIndex: number): string {
        return baseName + ' - Clone ' + cloneIndex.toString();
    }

    private static getBaseName(providedName: string): string {
        const baseNameRegex = new RegExp('^(.+)( ' + this.cloneStringForRegex + ')?$', 'i');
        const matches = baseNameRegex.exec(providedName);
        return matches[1];
    }

    private static getCloneIndex(providedName: string, existingNames: string[]): number {
        const baseName: string = this.getBaseName(providedName);
        let cloneIndex = 0;

        // Loop through existing rules to find names that match base name + the clone string format
        const cloneNameRegex = new RegExp(this.cloneStringForRegex + '$', 'i');
        _.forEach(existingNames, (existingName) => {
            if (_.startsWith(existingName, baseName) && cloneNameRegex.test(existingName)) {
                const cloneMatches = cloneNameRegex.exec(existingName);
                const cloneValue = Number(cloneMatches[1]);
                if (cloneValue > cloneIndex &&
                    existingName === this.appendCloneIndexToBaseName(baseName, cloneValue)
                ) {
                    cloneIndex = cloneValue;
                }
            }
        });

        return cloneIndex;
    }
}
