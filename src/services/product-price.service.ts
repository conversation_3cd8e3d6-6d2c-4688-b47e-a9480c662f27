import {AwsS3Service, AwsSnsService, HttpError, MongoService} from '@wondersign/serverless-services';
import * as _ from 'lodash';
import {Filter} from 'mongodb';
import {v4 as uuidV4} from 'uuid';
import {ProductPrice} from '../models/product-price';
import {ProductRef} from '../models/product-ref';
import {Product} from '../models/product';
import {
    ExpressionTypeFreightPrice,
    ExpressionTypeMath,
    ExpressionTypeRound,
    PriceFieldCalculationRule,
    PriceRule,
} from '../models/price-rule';
import {PriceElementChangeJob, ProductFixedPricesSet} from '../models/price-element-change-job';
import {PriceList} from '../models/price-list';
import {RentalTerm} from '../models/rental-term';
import {Components} from '../models/contracts';
import {AccountSettings, PackageCalculationMethod} from '../models/account-settings';
import {ProductPackageData} from '../models/product-package-data';
import {BatchJob} from '../models/batch-job';
import {FixedPriceChangeType} from '../models/fixed-price-change-type';
import {PriceListService} from './price-list.service';
import {PriceRuleService} from './price-rule.service';
import {ProductService} from './product.service';
import {ProductSelectionService} from './product-selection.service';
import {PriceElementChangeService} from './price-element-change.service';
import {MongoHelperService} from './mongo-helper.service';
import {RentalTermService} from './rental-term.service';
import {ObjectHelperService} from './object-helper.service';
import {AccountSettingsService} from './account-settings.service';
import {ProductPackageService} from './product-package.service';
import {BatchJobService} from './batch-job.service';

const config = process.env;

export class ProductPriceService {

    private static readonly collectionName: string = 'productPrice';
    private static readonly productRefFields = ['brand', 'sku'];
    private static readonly propertiesImpactingPriceCalculation: string[] = [
        'basePrice',
        'mapPrice',
        'msrpPrice',
        'rates',
    ];

    private accountSettings: AccountSettings;
    private queryTotalCount: number;
    private rentalTermMap: { [id: string]: RentalTerm };

    public static async countProductPrices(filter: {
        isFixed?: boolean;
        isMixed?: boolean;
        priceListId?: string;
        priceRuleId?: string;
    }): Promise<number> {
        // Ensure at least 1 key is present
        if (!_.get(filter, 'priceListId') && !_.get(filter, 'priceRuleId')) {
            throw new HttpError('Getting count of product prices requires at least priceListId or priceRuleId', 400);
        }

        const filterQuery: Filter<ProductPrice> = _.pick(filter, [
            'isFixed',
            'priceListId',
            'priceRuleId',
        ]);

        // Support mixed prices which should filter based on the values of `fixedPriceKeys`
        if (!_.isNil(filter.isMixed)) {
            if (filter.isMixed) {
                // Include mixed prices
                filterQuery.fixedPriceKeys = {
                    $exists: true,
                    $ne: [],
                };
            } else {
                // Exclude mixed prices
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                filterQuery.$or = [
                    {
                        fixedPriceKeys: {
                            $exists: false,
                        },
                    },
                    {
                        fixedPriceKeys: [],
                    },
                ];
            }
        }

        const productPriceCollection = await MongoService.getCollection(ProductPriceService.collectionName);
        return await productPriceCollection.countDocuments(
            filterQuery,
            MongoHelperService.getDefaultReadPreference()
        ) || 0;
    }

    public static async deleteProductPricesWithAccountId(accountId: string): Promise<void> {
        const productPriceCollection = await MongoService.getCollection(ProductPriceService.collectionName);
        await productPriceCollection.deleteMany({accountId});
    }

    public static async deleteProductPricesWithPriceListId(priceListId: string): Promise<void> {
        const productPriceCollection = await MongoService.getCollection(ProductPriceService.collectionName);
        await productPriceCollection.deleteMany({priceListId});
    }

    public static async updateExpirationDateForPriceListId(priceListId: string, expirationDate: Date): Promise<void> {
        const productPriceCollection = await MongoService.getCollection(ProductPriceService.collectionName);
        await productPriceCollection.updateMany({
            priceListId,
        }, {
            $set: {
                expirationDate,
            },
        }, MongoHelperService.getDefaultWriteConcern());
    }

    private static *iterateCalculationPricesStructure(
        calculation: PriceRule['calculation'],
    ): Generator<[string, PriceFieldCalculationRule, string?]> {
        for (const [priceKey, fieldDef] of Object.entries(calculation ?? {})) {
            if (priceKey === 'rental') {
                for (const [rentalId, rentalDef] of Object.entries(fieldDef ?? {})) {
                    const rentalKey = `${priceKey}.${rentalId}`;
                    yield [rentalKey, rentalDef, rentalId];
                }
            } else {
                yield [priceKey, fieldDef];
            }
        }
    }

    private static *iteratePricesStructure(prices: ProductPrice['prices']): Generator<[string, number]> {
        for (const [priceKey, priceStruct] of Object.entries(prices ?? {})) {
            if (priceKey === 'rental') {
                for (const [rentalId, rentalPrice] of Object.entries(priceStruct ?? {})) {
                    const rentalKey = `${priceKey}.${rentalId}`;
                    yield [rentalKey, rentalPrice];
                }
            } else {
                yield [priceKey, priceStruct as number];
            }
        }
    }

    public calculateProductPricesWithPriceRule(
        accountId: string,
        product: Product,
        priceRule: PriceRule,
        fixedPriceKeys?: string[],
        existingPrices?: ProductPrice['prices'],
    ): ProductPrice {
        const fixedPriceKeysSet = new Set<string>();
        let hasCalculatedPrice = true;
        const productPrice: ProductPrice = {
            accountId,
            brand: product.brand,
            isFixed: false,
            priceListId: priceRule.priceListId,
            priceRuleId: priceRule.id,
            prices: {
                list: null,
                rental: {},
                retail: null,
            },
            sku: product.sku,
        };

        // Set fixed prices if they are provided
        if (!_.isEmpty(fixedPriceKeys)) {
            hasCalculatedPrice = false;
            productPrice.fixedPriceKeys = fixedPriceKeys;

            for (const fixedPriceKey of fixedPriceKeys) {
                fixedPriceKeysSet.add(fixedPriceKey);
                _.set(productPrice, `prices.${fixedPriceKey}`, _.get(existingPrices, fixedPriceKey));
            }
        }
        // For each defined field in the rule
        for (const [field, , rentalId] of
            ProductPriceService.iterateCalculationPricesStructure(priceRule.calculation)) {
            // Skip setting fixed prices
            if (fixedPriceKeysSet.has(field)) {
                continue;
            }

            let rentalInstallments = null;
            if (rentalId) {
                rentalInstallments = _.get(this.getRentalTermById(rentalId), 'installments');

                if (!_.isFinite(rentalInstallments)) {
                    continue;
                }
            }
            hasCalculatedPrice = true;
            _.set(
                productPrice.prices,
                field,
                this.calculateProductPriceForField(product, priceRule, field, rentalInstallments)
            );
        }

        if (!hasCalculatedPrice) {
            productPrice.isFixed = true;
            delete productPrice.priceRuleId;
            delete productPrice.fixedPriceKeys;
        }

        const freightExpression = priceRule?.calculation?.retail?.expressions?.find(
            (expression) => expression.expressionType === 'freight'
        );
        if (freightExpression) {
            const metadata = { isFreightPriceApplied: false };
            this.addFreightPrice(
                product,
                productPrice.prices.retail,
                false,
                freightExpression as ExpressionTypeFreightPrice,
                metadata,
            );
            if (metadata.isFreightPriceApplied) {
                productPrice.isFreightPriceApplied = true;
            }
        }

        // Unset list or rental prices if the Account Settings for these are not enabled
        this.removeDisabledPriceTypes(productPrice);

        return ObjectHelperService.removeNil(productPrice);
    }

    public calculateProductPriceWithFieldRule(
        product: Product,
        calculation: PriceFieldCalculationRule,
        rentalInstallments?: number,
        metadata?: { isFreightPriceApplied: boolean },
    ): number {
        if (_.isEmpty(calculation)) {
            return null;
        }

        // Extract Base Price
        let price = this.extractBasePriceFromProduct(product, calculation.basePriceDef, rentalInstallments);
        if (_.isNil(price)) {
            return null;
        }
        // Perform calculation
        _.forEach(calculation.expressions, (expression) => {
            switch (expression.expressionType) {
                case 'freight':
                    price = this.addFreightPrice(product, price, calculation.minIsMAP, expression, metadata);
                    break;
                case 'math':
                    price = this.applyMathOperation(price, expression);
                    break;
                case 'round':
                    price = this.applyRoundOperation(price, expression);
                    break;
            }

            // First round to a precision of 6 decimal places. This helps with edge cases due to float point precision
            price = _.round(price, 6);

            // Round to 2 decimal places after each operation to ensure what's shown on the step-by-step preview matches
            price = _.round(price, 2);
        });

        // Enforce minIsMAP
        const mapPrice = product.mapPrice;
        if (calculation.minIsMAP && _.isFinite(mapPrice) && price < mapPrice) {
            price = mapPrice;
        }

        return _.round(price, 2);
    }

    public async deleteFixedPricesForPriceListIdAndProducts(
        priceListId: string,
        productRefs: ProductRef[],
    ): Promise<void> {
        const logPrefix = `${this.constructor.name}: deleteFixedPricesForPriceListIdAndProducts:`;
        const productPriceCollection = await MongoService.getCollection(ProductPriceService.collectionName);
        const brands = new Set(_.map(productRefs, 'brand'));
        const skus = new Set(_.map(productRefs, 'sku'));

        await productPriceCollection.deleteMany({
            $or: [
                {
                    isFixed: true,
                },
                {
                    fixedPriceKeys: {
                        $exists: true,
                        $ne: [],
                    },
                },
            ],
            brand: {
                $in: Array.from(brands),
            },
            priceListId,
            sku: {
                $in: Array.from(skus),
            },
        }, MongoHelperService.getDefaultWriteConcern());

        // Re-calculate prices for this product
        const priceListService = new PriceListService();
        const priceLists = await priceListService.queryPriceList({ids: [priceListId]});
        try {
            if (!_.isEmpty(priceLists)) {
                await this.updatePricesForPriceList(priceLists[0], false, productRefs);
            } else {
                console.error(`${logPrefix} Could not find Price List with ID: ${priceListId}`);
            }
        } catch (err) {
            console.error(`${logPrefix} Failed to update price for products: `, err, productRefs);
        }
    }

    public async deletePricesForDeletedProducts(accountId: string, productRefs: ProductRef[]): Promise<void> {
        const productPriceCollection = await MongoService.getCollection(ProductPriceService.collectionName);
        const bulkUpdates = [];
        _.forEach(productRefs, (productRef) => {
            bulkUpdates.push({
                deleteMany: {
                    filter: _.extend({accountId}, productRef),
                },
            });
        });
        await productPriceCollection.bulkWrite(bulkUpdates, MongoHelperService.getDefaultWriteConcern());
    }

    public extractBasePriceFromProduct(
        product: Product,
        basePriceDef: PriceFieldCalculationRule['basePriceDef'],
        rentalInstallments?: number,
    ): number {
        const cost = _.get(product, 'basePrice', null);
        const msrp = _.get(product, 'msrpPrice', null);
        let basePrice: number = null;
        switch (basePriceDef) {
            case 'cost':
                basePrice = cost;
                break;

            case 'costMaxMsrp':
                if (!_.isNil(msrp) && cost > msrp) {
                    basePrice = msrp;
                } else {
                    basePrice = cost;
                }
                break;

            case 'msrp':
                basePrice = msrp;
                break;

            case 'msrpPreferred':
                basePrice = _.isNil(msrp) ? cost : msrp;
                break;
        }

        if (_.isFinite(basePrice)) {

            // Rental Price must start by dividing the base price by the installments
            if (_.isFinite(rentalInstallments) && rentalInstallments > 0) {
                basePrice = basePrice / rentalInstallments;
            }

            // Always make sure base price is rounded to 2 decimal places
            basePrice = _.round(basePrice, 2);
        }

        return basePrice;
    }

    public async getActiveProductPrices(
        accountId: string,
        productRefs: ProductRef[],
        timezone?: string,
    ): Promise<ProductPrice[]> {

        // Init Price Lists and Package product calculation setting for account
        const priceListService = new PriceListService();
        const priceLists = await priceListService.queryPriceList({accountId});
        const accountSumsPackageProductPrices = await this.doesAccountSumPackageProductPrices(accountId);
        let productRefsToPrice = productRefs;

        // Layer product prices by applying prices in priority sorted order, keeping the first found price
        const activePromoPriceLists: PriceList[] = [];
        let defaultPriceList: PriceList;
        for (const priceList of priceLists) {
            if (priceList.type === 'promotional') {
                if (timezone && priceListService.isActivePromotionalPriceList(priceList, timezone)) {
                    activePromoPriceLists.push(priceList);
                }
            } else {
                defaultPriceList = priceList;
            }
        }

        // Find package products in the requested list and add in dependent component prices for calculation
        let packageProducts = [];
        if (!_.isEmpty(activePromoPriceLists) && accountSumsPackageProductPrices) {

            // Find package products in the requested list
            const packageData = await ProductSelectionService.fetchPackageData(accountId, productRefs);
            const productPackageService = new ProductPackageService();
            packageProducts = productPackageService.extractPackageProducts(productRefs, packageData);

            if (!_.isEmpty(packageProducts)) {

                // Find component refs for the package products
                const componentRefs = ProductPackageService.getUniqueComponentRefsFromPackageProducts(packageProducts);

                // Add the component refs to the list of product refs to price
                productRefsToPrice = _.union(productRefsToPrice, componentRefs);
            }
        }

        // Fetch and apply pre-calculated pricing per price list in preferred order (first set value wins)
        const productPriceMap = await this.getMergedProductPriceMapForPriceLists([
            ...activePromoPriceLists,
            defaultPriceList,
        ], productRefsToPrice);

        // Get updated combined package pricing from components
        this.updateProductPriceMapWithPromoPackageProducts(productPriceMap, packageProducts, productRefs);

        return _.values(productPriceMap);
    }

    public async getProductPackagePrices(
        accountId: string,
        priceListId: string,
        productRefs: ProductRef,
    ): Promise<ProductPrice> {
        let productRefsToPrice = [productRefs];

        const accountSumsPackageProductPrices = await this.doesAccountSumPackageProductPrices(accountId);
        if (!accountSumsPackageProductPrices) {
            return;
        }

        const priceListService = new PriceListService();
        const priceLists = await priceListService.queryPriceList({ accountId, ids: [priceListId] });

        if (_.isEmpty(priceLists)) {
            return;
        }

        const packageData = await ProductSelectionService.fetchPackageData(accountId, [productRefs]);

        if (_.isEmpty(packageData)) {
            return;
        }

        const productPackageService = new ProductPackageService();
        const packageProducts = productPackageService.extractPackageProducts([productRefs], packageData);
        if (!_.isEmpty(packageProducts)) {
            const componentsRef = ProductPackageService.getUniqueComponentRefsFromPackageProducts(packageProducts);
            productRefsToPrice = _.union(productRefsToPrice, componentsRef);
        }

        const productPriceMap = await this.getMergedProductPriceMapForPriceLists([
            ...priceLists,
        ], productRefsToPrice);

        return this.calculatePackagePrices(packageProducts, productPriceMap)[0];
    }

    public getQueryTotalCount(): number {
        return this.queryTotalCount;
    }

    public async initAccountSettings(accountId: string): Promise<void> {
        if (this.accountSettings) {
            return;
        }

        this.accountSettings = await AccountSettingsService.getForAccountId(accountId);
    }

    public async initRentalTermMap(accountId: string): Promise<void> {
        if (this.rentalTermMap) {
            return;
        }

        const rentalTermService = new RentalTermService();
        this.rentalTermMap = {};
        _.forEach(await rentalTermService.queryRentalTerm({accountId}), (rentalTerm) => {
            this.rentalTermMap[rentalTerm.id] = rentalTerm;
        });
    }

    public async matchPriceRulesForPriceListAndProducts(
        priceList: PriceList,
        products: ProductRef[],
        treatProductsAsComplete = false,
    ): Promise<Array<{ priceRule: PriceRule; product: ProductRef }>> {
        const logPrefix = `${this.constructor.name}: matchPriceRulesForPriceListAndProducts:`;

        // If empty products, no need for any further processing
        if (_.isEmpty(products)) {
            return [];
        }

        // Build map of Price Rules
        const priceRuleService = new PriceRuleService();
        const priceRules = await priceRuleService.queryPriceRule({priceListIds: [priceList.id]});
        const priceRuleMap = _.zipObject(
            _.map(priceRules, 'id'),
            priceRules,
        );

        // Get the matched rules for these products (order is important - use order from priceList)
        const selectionGroupsOptions: Array<{
            id: string;
            isSelectAll?: boolean;
            selectionGroups: Components['schemas']['PriceRuleWithId']['productSelectionCriteria'];
            selectionGroupsOperator: Components['schemas']['PriceRuleWithId']['productSelectionCriteriaOperator'];
        }> = [];
        _.forEach(priceList.priceRuleIds, (priceRuleId) => {
            const priceRule = priceRuleMap[priceRuleId];
            if (_.isEmpty(priceRule)) {
                console.error(`${logPrefix} Could not find Price Rule with ID: `, priceRuleId);
                return;
            }

            // Ignore disabled Price Rules
            if (!priceRule.enabled) {
                return;
            }

            selectionGroupsOptions.push({
                id: priceRule.id,
                isSelectAll: priceRule.productSelectionType === 'all',
                selectionGroups: priceRule.productSelectionCriteria,
                selectionGroupsOperator: priceRule.productSelectionCriteriaOperator ?? null,
            });
        });

        // If there are no priceRules / selectionGroupOptions, no need for further processing
        if (_.isEmpty(selectionGroupsOptions)) {
            console.warn(`${logPrefix} No enabled Price Rules were found for priceList: `, priceList.id);
            return _.map(products, (product) => ({priceRule: null, product}));
        }

        // PSS - Match
        const matchedProductsToPriceRules = await ProductSelectionService.match(
            priceList.accountId,
            products,
            selectionGroupsOptions,
            treatProductsAsComplete,
        );

        // Map the provided products to the output from PSS match
        const productsMap = _.zipObject(
            _.map(products, (product) => ProductService.getProductKey(product)),
            products,
        );
        return _.map(matchedProductsToPriceRules, (matchedProductToPriceRule) => {
            const priceRuleId = _.get(matchedProductToPriceRule, 'selectionId');
            const priceRule = priceRuleMap[priceRuleId] ?? null;
            return {
                priceRule,
                product: productsMap[ProductService.getProductKey(matchedProductToPriceRule)],
            };
        });
    }

    public async queryProductPrices(
        query: {
            accountId?: string;
            isFixed?: boolean;
            isMixed?: boolean;
            priceListId?: string;
            priceRuleId?: string;
            productRefs?: ProductRef[];
        },
        options?: {
            limit?: number;
            skip?: number;
        }
    ): Promise<ProductPrice[]> {
        const productPriceCollection = await MongoService.getCollection(ProductPriceService.collectionName);
        const filterQuery: Filter<ProductPrice> = _.pick(query, [
            'accountId',
            'isFixed',
            'priceListId',
            'priceRuleId',
        ]);

        // Disallow an empty query filter for one of the primary account-defining keys
        if (_.isEmpty(_.pick(filterQuery, ['accountId', 'priceListId', 'priceRuleId']))) {
            throw new HttpError('You must include a valid indexed field when querying for product prices', 400);
        }

        // Support mixed prices which should filter based on the values of `fixedPriceKeys`
        if (!_.isNil(query.isMixed)) {
            if (query.isMixed) {
                // Include mixed prices
                filterQuery.fixedPriceKeys = {
                    $exists: true,
                    $ne: [],
                };
            } else {
                // Exclude mixed prices
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                filterQuery.$or = [
                    {
                        fixedPriceKeys: {
                            $exists: false,
                        },
                    },
                    {
                        fixedPriceKeys: [],
                    },
                ];
            }
        }

        const productKeys = new Set<string>();

        // Mutate Product refs to a friendly query syntax
        if (!_.isEmpty(query.productRefs)) {
            const brands = new Set<string>();
            const skus = new Set<string>();
            _.forEach(query.productRefs, (productRef) => {
                brands.add(productRef.brand);
                skus.add(productRef.sku);
                productKeys.add(ProductService.getProductKey(productRef));
            });
            filterQuery.brand = {$in: Array.from(brands)};
            filterQuery.sku = {$in: Array.from(skus)};
        }

        const productPricesCursor = productPriceCollection.find<ProductPrice>(filterQuery, {
            ...MongoHelperService.getDefaultReadPreference(),
            projection: {_id: 0},
            ...options,
        });

        // Get the total count of products
        if (_.get(options, 'limit')) {
            this.queryTotalCount = await productPriceCollection.countDocuments(filterQuery);
        }

        const productPrices = await productPricesCursor.toArray();

        // Get the total count of products
        if (!_.get(options, 'limit')) {
            this.queryTotalCount = _.size(productPrices);
        }

        if (productKeys.size > 0) {
            return _.filter(
                productPrices,
                (productPrice) => productKeys.has(ProductService.getProductKey(productPrice)),
            );
        } else {
            return productPrices;
        }
    }

    public async queueProductEventBatch(
        accountId: string,
        productEvents: Array<Components['schemas']['ProductEvent']>,
        batchJobFromConsumer?: BatchJob,
    ): Promise<{priceElementChangeJobId: string}> {
        let batchJobId: string;

        if (!_.isEmpty(batchJobFromConsumer)) {
            const batchJob = await BatchJobService.receiveRequest(batchJobFromConsumer);
            batchJobId = _.get(batchJob, 'id');
        }

        const priceElementChangeJobId = await PriceElementChangeService.queuePriceElementChangeJob({
            accountId,
            batchJobId,
            elementAfterChange: productEvents,
            elementType: 'productEventBatch',
        });

        return {priceElementChangeJobId};
    }

    public async queueProductFixedPricesBatch(
        accountId: string,
        priceListId: string,
        productsAndPrices: Array<{prices: ProductPrice['prices']; product: Product}>,
        existingNotIncluded: FixedPriceChangeType = FixedPriceChangeType.remove,
        batchJobFromConsumer?: BatchJob,
    ): Promise<{priceElementChangeJobId: string}> {
        let batchJobId: string;

        if (!_.isEmpty(batchJobFromConsumer)) {
            const batchJob = await BatchJobService.receiveRequest(batchJobFromConsumer);
            batchJobId = _.get(batchJob, 'id');
        }

        const priceElementChangeJobId = await PriceElementChangeService.queuePriceElementChangeJob({
            accountId,
            batchJobId,
            elementAfterChange: {
                existingNotIncluded,
                priceListId,
                productsAndPrices,
            },
            elementType: 'productFixedPricesSetBatch',
        });
        return {priceElementChangeJobId};
    }

    public async saveProductPrices(productPrices: ProductPrice[]): Promise<void> {
        if (_.isEmpty(productPrices)) {
            return;
        }

        const productPriceCollection = await MongoService.getCollection(ProductPriceService.collectionName);
        const bulkUpdates = [];

        const accountIds = new Set<string>();
        const priceListIds = new Set<string>();
        _.forEach(productPrices, (productPrice: ProductPrice) => {
            accountIds.add(productPrice.accountId);
            priceListIds.add(productPrice.priceListId);
        });

        const priceListService = new PriceListService();
        const priceLists = await priceListService.queryPriceList({ids: Array.from(priceListIds)});
        const priceListExpirationDateMap = new Map<string, Date>();

        // Set expiration dates for promo price lists
        _.forEach(priceLists, (priceList) => {
            if (priceList.type === 'promotional') {
                priceListExpirationDateMap.set(
                    priceList.id,
                    PriceListService.getExpirationDateForPromoPriceList(priceList)
                );
            }
        });

        _.forEach(productPrices, (productPrice) => {
            const replacement: ProductPrice = _.extend({}, productPrice);
            const priceListId = productPrice.priceListId;
            const expirationDate = priceListExpirationDateMap.get(priceListId);

            if (expirationDate) {
                replacement.expirationDate = expirationDate;
            }

            bulkUpdates.push({
                replaceOne: {
                    filter: _.pick(productPrice, ['priceListId', 'brand', 'sku']),
                    replacement,
                    upsert: true,
                },
            });
        });
        await productPriceCollection.bulkWrite(bulkUpdates, MongoHelperService.getDefaultWriteConcern());

        // Save product price changes to S3
        const hash = uuidV4();
        const s3Key = `price-changes/prices-update-${hash}.json`;

        try {
            const awsS3Service = new AwsS3Service(config.dataBucketName);
            await awsS3Service.writeToS3(s3Key, JSON.stringify(productPrices));
        } catch (err) {
            console.error('ProductPriceService: saveProductPrices: Failed to write price changes to S3: ', err);
        }

        // Announce productPriceCollection changes for the accountId
        await AwsSnsService.sendSnsMessage(
            _.map(Array.from(accountIds), (accountId) => ({
                accountId,
                s3Key,
            })),
            config.accountProductPriceChangeTopic,
            'Account Product Price Changes',
            config.awsAccountId
        );
    }

    public async unsetRentalTermIdForAccountId(rentalTermId: string, accountId: string): Promise<void> {
        if (_.isEmpty(rentalTermId)) {
            throw new HttpError('rentalTermId is required in order to remove a rental price', 422);
        }

        if (_.isEmpty(accountId)) {
            throw new HttpError('accountId is required in order to remove a rental price', 422);
        }

        const productPriceCollection = await MongoService.getCollection<ProductPrice>(
            ProductPriceService.collectionName,
        );
        await productPriceCollection.updateMany({
            accountId,
        }, {
            $pull: {
                fixedPriceKeys: `rental.${rentalTermId}`,
            },
            $unset: {
                [`prices.rental.${rentalTermId}`]: 1,
            },
        }, MongoHelperService.getDefaultWriteConcern());
    }

    public async updateBrandFromProduct(currentBrand: string, newBrand: string, logPrefix: string): Promise<void> {
        const internalLogPrefix = `${logPrefix}: productPriceService: updateBrandFromProductSelectionCriteria`;
        const productPriceCollection = await MongoService.getCollection(ProductPriceService.collectionName);
        const filterQuery: Partial<ProductPrice> = { brand: currentBrand };
        const setQuery: Partial<ProductPrice> = { brand: newBrand };
        const updateResult = await productPriceCollection.updateMany(
            filterQuery,
            {
                $set: {
                    ...setQuery,
                },
            },
        );

        if (updateResult.matchedCount === 0) {
            console.warn(`${internalLogPrefix} No productPrice documents found for branch ${currentBrand}`);
            return;
        }

        console.log(`${internalLogPrefix} updated ${updateResult.modifiedCount} with new ${newBrand} brand value`);
    }

    public async updateFixedProductPrices(
        accountId: string,
        priceListId: string,
        productsAndPrices?: Array<{prices: ProductPrice['prices']; product: Product}>,
        existingNotIncluded?: FixedPriceChangeType,
        productPrices?: ProductPrice[],
    ): Promise<void> {
        if (!productPrices) {
            if (_.isEmpty(productsAndPrices)) {
                return;
            }

            existingNotIncluded = existingNotIncluded || FixedPriceChangeType.remove;
            const productRefs = _.map(productsAndPrices, 'product');

            switch (existingNotIncluded) {
                // Reset non-included prices to be calculated. This option is equal to delete fixed prices + keep
                // Make sure `reset` is above `keep`. Note there is no break in this case - it should continue to keep
                case FixedPriceChangeType.reset:
                    await this.deleteFixedPricesForPriceListIdAndProducts(
                        priceListId,
                        productRefs
                    );

                // Keep non-included prices as-is
                case FixedPriceChangeType.keep:
                    productPrices = await this.buildKeepFixedPrices(accountId, priceListId, productsAndPrices);
                    break;

                // Remove non-included prices (create a new record with only these fixed values)
                default:
                    // Generate new prices with isFixed: true
                    productPrices = _.map(productsAndPrices, ({product, prices}) => ({
                        accountId,
                        ...product, // brand, sku
                        isFixed: true,
                        priceListId,
                        prices,
                    }));
                    break;
            }
        }

        if (_.isEmpty(productPrices)) {
            return;
        }

        await this.initAccountSettings(accountId);

        // Strip list and/or rental price if associated pricing is disabled
        for (const productPrice of productPrices) {
            this.removeDisabledPriceTypes(productPrice);
        }

        await this.saveProductPrices(productPrices);
        await this.updatePackagePricesForComponents(
            _.map(productPrices, (productPrice) => _.pick(productPrice, ['brand', 'sku'])),
            accountId,
            priceListId,
        );
    }

    public async updatePackagePricesForComponents(
        componentRefs: ProductRef[],
        accountId: string,
        priceListId: string,
    ): Promise<void> {

        // Only calculate package prices for default price lists - promo lists are calculated on the fly
        const priceListService = new PriceListService();
        const priceList = _.first(await priceListService.queryPriceList({ids: [priceListId]}));
        if (_.get(priceList, 'type') !== 'default') {
            return;
        }

        // Check if Account is using Package Product pricing method price
        if (!await this.doesAccountSumPackageProductPrices(accountId)) {
            return;
        }

        // Find packages impacted by these possible components
        const packageData = await ProductSelectionService.fetchPackageData(accountId, componentRefs);

        // Get package products from these components
        const productPackageService = new ProductPackageService();
        const packageProductsToReprice = productPackageService.getPackageProductsWithComponents(
            componentRefs,
            packageData,
        );

        if (_.isEmpty(packageProductsToReprice)) {
            return;
        }

        // Get existing prices for package and components and build map
        const packageAndComponentRefsMap: {[id: string]: ProductRef} = {};
        _.forEach(packageProductsToReprice, (packageProduct) => {
            const productRefProperties = ['brand', 'sku'];
            packageAndComponentRefsMap[ProductService.getProductKey(packageProduct)] = _.pick(
                packageProduct,
                productRefProperties,
            ) as ProductRef;

            _.forEach(packageProduct.components, (component) => {
                packageAndComponentRefsMap[ProductService.getProductKey(component)] = _.pick(
                    component,
                    productRefProperties,
                ) as ProductRef;
            });
        });
        const existingPackagePrices = await this.queryProductPrices({
            priceListId,
            productRefs: _.values(packageAndComponentRefsMap),
        });
        const productPricesMap: {[id: string]: ProductPrice} = {};
        _.forEach(existingPackagePrices, (existingPackagePrice) => {
            const packageKey = ProductService.getProductKey(existingPackagePrice);
            productPricesMap[packageKey] = existingPackagePrice;
        });

        return this.calculateFilterSavePackagePrices(packageProductsToReprice, productPricesMap);
    }

    public async updatePricesForMutatedProducts(
        accountId: string,
        mutatedProductRefs: Array<Components['schemas']['ProductEvent']>,
    ): Promise<void> {
        const logPrefix = `${this.constructor.name}: updatePricesForMutatedProducts:`;

        // Fetch all Price Lists for the Account
        const priceListService = new PriceListService();
        const priceLists = await priceListService.queryPriceList({accountId});

        const productsToDelete = [];
        const productsToPrice = [];
        const productsToRuleMatch = [];

        // Breakout mutated products between deleted / added / changed
        _.forEach(mutatedProductRefs, (mutatedProductRef) => {
            if (mutatedProductRef.isDeleted) {
                productsToDelete.push(_.pick(mutatedProductRef, ProductPriceService.productRefFields));
                return;
            }

            if (mutatedProductRef.isNew) {
                productsToRuleMatch.push(_.pick(mutatedProductRef, ProductPriceService.productRefFields));
                return;
            }

            if (!_.isEmpty(_.intersection(
                ProductPriceService.propertiesImpactingPriceCalculation,
                mutatedProductRef.changedProperties,
            ))) {
                productsToPrice.push(_.pick(mutatedProductRef, ProductPriceService.productRefFields));
            }

            if (!_.isEmpty(mutatedProductRef.changedProperties)) {
                productsToRuleMatch.push(_.pick(mutatedProductRef, ProductPriceService.productRefFields));
            }
        });

        // Delete prices for delete products (for all Price Lists)
        if (!_.isEmpty(productsToDelete)) {
            await this.deletePricesForDeletedProducts(accountId, productsToDelete);
        }

        // Iterate over price lists
        for (const priceList of priceLists ?? []) {
            if (priceList.type === 'promotional' && !await priceListService.hasPromoPricesCalculated(priceList.id)) {
                console.log(`${logPrefix} Ignoring inactive promotional priceList: `, priceList.id);
                continue;
            }
            await this.updatePricesForPriceList(priceList, false, productsToRuleMatch, productsToPrice);
        }
    }

    public async updatePricesForPriceElementChange(priceElementChangeJob: PriceElementChangeJob): Promise<void> {
        const accountId = priceElementChangeJob.accountId;
        const priceListService = new PriceListService();
        const priceRuleService = new PriceRuleService();
        const batchJobService = new BatchJobService();

        // If there is a batch job, only publish the completion if the batch job is completed
        if (priceElementChangeJob.batchJobId) {
            batchJobService.setCompletionHandler(async (batchJob) => {
                priceElementChangeJob.context = batchJob.context;
                await PriceElementChangeService.publishPriceElementChangeJobCompletion(priceElementChangeJob);
            });
        }

        let priceLists: PriceList[];
        switch (priceElementChangeJob.elementType) {
            case 'accountSettings': {
                await this.processAccountSettingsChange(priceElementChangeJob);
                break;
            }

            case 'priceList': {
                // If rematching is required for the price list, update prices for this Price List
                if (priceListService.isRematchingRequired(
                    _.get(priceElementChangeJob, 'elementBeforeChange') as PriceList,
                    _.get(priceElementChangeJob, 'elementAfterChange') as PriceList,
                )) {
                    priceLists = await priceListService.queryPriceList({ids: [priceElementChangeJob.elementId]});
                    await this.updatePricesForPriceList(_.first(priceLists), true);
                }
                break;
            }

            case 'priceRule': {
                // Determine if re-matching is required
                if (priceRuleService.isRematchingRequired(
                    _.get(priceElementChangeJob, 'elementBeforeChange') as PriceRule,
                    _.get(priceElementChangeJob, 'elementAfterChange') as PriceRule,
                )) {
                    const priceListId = _.get(priceElementChangeJob, 'elementAfterChange.priceListId') ??
                        _.get(priceElementChangeJob, 'elementBeforeChange.priceListId');
                    priceLists = await priceListService.queryPriceList({ids: [priceListId]});
                    await this.updatePricesForPriceList(_.first(priceLists), true);
                }

                // Determine if recalculating is required
                if (priceRuleService.isRecalculatingRequired(
                    _.get(priceElementChangeJob, 'elementBeforeChange') as PriceRule,
                    _.get(priceElementChangeJob, 'elementAfterChange') as PriceRule,
                )) {
                    await this.updatePricesForPriceRuleId(priceElementChangeJob.elementId);
                }
                break;
            }

            case 'productEventBatch': {
                await this.updatePricesForMutatedProducts(
                    accountId,
                    priceElementChangeJob.elementAfterChange as Array<Components['schemas']['ProductEvent']>,
                );
                break;
            }

            case 'productFixedPricesSetBatch': {
                const productFixedPricesSetBatch = priceElementChangeJob.elementAfterChange as ProductFixedPricesSet;
                await this.updateFixedProductPrices(
                    accountId,
                    productFixedPricesSetBatch.priceListId,
                    productFixedPricesSetBatch.productsAndPrices,
                    productFixedPricesSetBatch.existingNotIncluded,

                    // This argument can be removed after the 1.5.0 deployment, this is only retained for handling
                    // any existing queued elements at the time of deployment
                    productFixedPricesSetBatch.productPrices,
                );
                break;
            }

            case 'promoPriceListCalculation': {
                await priceListService.markPromoPricesCalculated(priceElementChangeJob.elementId);
                priceLists = await priceListService.queryPriceList({ids: [priceElementChangeJob.elementId]});
                await this.updatePricesForPriceList(_.first(priceLists), true);
                break;
            }

            case 'rentalTerm': {
                // Reprice products that have Price Rules impacted by this Rental Term
                const priceRuleIds = (await priceRuleService.queryPriceRule({accountId}))
                    .filter((priceRule) =>
                        !_.isNil(_.get(priceRule, 'calculation.rental.' + priceElementChangeJob.elementId)))
                    .map((priceRule) => priceRule.id);
                for (const priceRuleId of priceRuleIds) {
                    await this.updatePricesForPriceRuleId(priceRuleId);
                }
                break;
            }
        }

        if (!priceElementChangeJob.batchJobId) {

            // If this is not part of a batch job, proceed with notifying of completion
            await PriceElementChangeService.publishPriceElementChangeJobCompletion(priceElementChangeJob);
        } else {

            // If this is part of a batch, update the request as processed. This will trigger the completion handler
            // if it's the final request to be processed
            await batchJobService.updateProcessedRequest(priceElementChangeJob.batchJobId);
        }
    }

    private applyMathOperation(input: number, mathOperation: ExpressionTypeMath): number {
        const operand = mathOperation.value;
        let result;
        let multiplier;
        switch (mathOperation.operation) {
            case 'add':
                result = input + operand;
                break;

            case 'divide':
                result = input / operand;
                break;

            case 'margin':
                if (operand < 100) {
                    multiplier = 100 / (100 - operand);
                } else {
                    multiplier = 0;
                }
                result = input * multiplier;
                break;

            case 'markup':
                multiplier = 1 + (operand / 100);
                result = input * multiplier;
                break;

            case 'turns':
            case 'multiply':
                result = input * operand;
                break;

            case 'subtract':
                result = input - operand;
                break;

        }

        // Prevent a negative result
        if (_.isFinite(result) && result < 0) {
            result = 0;
        }

        return result;
    }

    private applyRoundOperation(input: number, roundOperation: ExpressionTypeRound): number {
        // Don't round a 0 price
        if (input === 0.0 || input === 0) {
            return input;
        }

        const roundDirection = roundOperation.direction;
        const roundDecimalValue = roundOperation.decimalValue;
        const roundIntegerValue = roundOperation.integerValue;

        // Start with a basic rounded number
        let price = String(Math.round(input * 100) / 100);

        const priceParts = price.split('.');
        const dollars = Number(priceParts[0]);
        let cents = '00';
        if (priceParts[1]) {
            cents = priceParts[1];
        }

        let roundOrderOfMag = String(roundIntegerValue).length;
        if (_.isNil(roundIntegerValue)) {
            roundOrderOfMag = 0;
        }
        const roundDividend = Math.pow(10, roundOrderOfMag);

        const roundResult = dollars / roundDividend;
        const dollarsRounded = (Math.floor(roundResult) * roundDividend) +
            (roundIntegerValue ? _.toNumber(roundIntegerValue) : 0);
        if (roundDirection.toLowerCase() === 'up') {
            price = this.roundUp({
                cents,
                dollars,
                dollarsRounded,
                price,
                roundDecimalValue,
                roundDividend,
            });
        } else {
            price = this.roundDown({
                cents,
                dollars,
                dollarsRounded,
                price,
                roundDecimalValue,
                roundDividend,
            });
        }

        // Check max adjustment value
        const roundedPrice = parseFloat(price);
        if (_.get(roundOperation, 'maxAdjustment.enabled')) {
            const adjustmentDifferenceCurrency = Math.abs(input - roundedPrice);
            const adjustmentDifferencePercent = (adjustmentDifferenceCurrency / input) * 100;
            let roundedOutOfBounds;
            if (roundOperation.maxAdjustment.unit === 'finite') {
                roundedOutOfBounds = adjustmentDifferenceCurrency > roundOperation.maxAdjustment.value;
            } else {
                roundedOutOfBounds = adjustmentDifferencePercent > roundOperation.maxAdjustment.value;
            }
            return roundedOutOfBounds ? input : roundedPrice;
        } else {
            return roundedPrice;
        }
    }

    private addFreightPrice(
        product: Product,
        price: number,
        minIsMAP: boolean,
        expression: ExpressionTypeFreightPrice,
        metadata?: { isFreightPriceApplied: boolean },
    ): number {
        const logPrefix = 'ProductPriceService - addFreightPrice: ';

        const shouldApplyFreightCost = (!product.msrpPrice || price !== product.msrpPrice) && !minIsMAP;
        if (!shouldApplyFreightCost) {
            console.log(logPrefix,
                `Product with brand ${product.brand} and sku ${product.sku} is using MSRP Price or minIsMAP is set true`
            );
            return price;
        }

        if (!product.rates || product.rates.length === 0) {
            console.log(logPrefix, `Rates is empty for product with brand ${product.brand} and sku ${product.sku}`);
            return price;
        }

        const freightService = product.rates.find((rate) => rate.service === expression.operation);

        if (!freightService) {
            console.log(logPrefix, `Freight service ${expression.operation} not found into rates array`);
            return price;
        }

        let result = price + freightService.rate;
        if (_.isFinite(result) && result < 0) {
            result = 0;
        }

        if (metadata && 'isFreightPriceApplied' in metadata) {
            metadata.isFreightPriceApplied = true;
        }
        return result;
    }

    private async buildKeepFixedPrices(
        accountId: string,
        priceListId: string,
        productsAndPrices: Array<{prices: ProductPrice['prices']; product: Product}>
    ): Promise<ProductPrice[]> {
        const productPrices = [];

        // Lookup existing prices
        const productRefs = _.map(productsAndPrices, 'product');
        const existingPrices = await this.queryProductPrices({
            priceListId,
            productRefs,
        });

        const existingPriceMap = new Map<string, ProductPrice>();

        for (const existingPrice of existingPrices) {
            existingPriceMap.set(ProductService.getProductKey(existingPrice), existingPrice);
        }

        // Set fixedPriceKeys and/or adjust isFixed as necessary
        for (const {prices, product} of productsAndPrices) {
            const productPrice: ProductPrice = existingPriceMap.get(
                ProductService.getProductKey(product)
            ) || {
                accountId,
                ...product, // brand, sku
                isFixed: true,
                priceListId,
                prices,
            };

            const fixedPriceKeys = new Set<string>();

            // Override prices and capture fixedPriceKeys
            for (const [priceKey, value] of ProductPriceService.iteratePricesStructure(prices)) {
                fixedPriceKeys.add(priceKey);
                _.set(productPrice, `prices.${priceKey}`, value);
            }

            // If the product price, only has fixed prices, just push it and continue
            if (productPrice.isFixed) {
                productPrices.push(productPrice);
                continue;
            }

            // Determine if there are other prices that are non-fixed
            let hasCalculatedPrices = false;
            for (const [priceKey] of ProductPriceService.iteratePricesStructure(productPrice.prices)) {
                if (!fixedPriceKeys.has(priceKey)) {
                    hasCalculatedPrices = true;
                    break;
                }
            }

            // Set isFixed to true if all prices are now fixed or set fixedPriceKeys if not
            if (hasCalculatedPrices) {
                _.forEach(productPrice.fixedPriceKeys, (fixedPriceKey) => {
                    fixedPriceKeys.add(fixedPriceKey);
                });

                productPrice.fixedPriceKeys = Array.from(fixedPriceKeys);
            } else {
                productPrice.isFixed = true;
                delete productPrice.priceRuleId;
            }

            productPrices.push(productPrice);
        }

        return productPrices;
    }

    private async calculateFilterSavePackagePrices(
        packageProducts: ProductPackageData[],
        productPricesMap: {[id: string]: ProductPrice},
    ): Promise<void> {

        // Calculate package product prices
        const packagePrices = this.calculatePackagePrices(packageProducts, productPricesMap);

        // Filter out product prices that are currently set as `isFixed` or have not changed
        const packagePricesToSave = _.filter(packagePrices, (packagePrice) => {
            const packageKey = ProductService.getProductKey(packagePrice);
            const existingPackagePrice = productPricesMap[packageKey];
            const compareKeys = ['prices', 'isFixed', 'priceRuleId'];
            return !existingPackagePrice ||
                !(existingPackagePrice.isFixed || _.isEqual(
                    _.pick(existingPackagePrice, compareKeys),
                    _.pick(packagePrice, compareKeys),
                ));
        });

        // Save package product prices
        if (!_.isEmpty(packagePricesToSave)) {
            await this.saveProductPrices(packagePricesToSave);
        }
    }

    private calculatePackagePricesForProduct(
        packageProduct: ProductPackageData,
        pricesMap: { [id: string]: ProductPrice },
        ignoreEmptyComponentPrices: boolean,
    ): ProductPrice {
        const existingProductPrices: ProductPrice = pricesMap[ProductService.getProductKey(packageProduct)];
        const packagedProductPriceRound = _.get(
            this.accountSettings,
            'packagedProductPriceRound',
            false,
        );
        const fixedPriceKeys = _.get(existingProductPrices, 'fixedPriceKeys');
        const existingPrices = _.get(existingProductPrices, 'prices');
        const fixedPriceKeysSet = new Set<string>();
        let hasCalculatedPrice = true;
        const productPrice: ProductPrice = {
            accountId: null,
            brand: packageProduct.brand,
            isFixed: false,
            priceListId: null,
            prices: {
                list: null,
                rental: {},
                retail: null,
            },
            sku: packageProduct.sku,
        };

        const priceKeyValuesMap: { [id: string]: number[] } = {};

        // Set fixed prices if they are provided
        if (!_.isEmpty(fixedPriceKeys) && existingPrices) {
            hasCalculatedPrice = false;
            productPrice.fixedPriceKeys = fixedPriceKeys;

            for (const fixedPriceKey of fixedPriceKeys) {
                fixedPriceKeysSet.add(fixedPriceKey);
                _.set(productPrice, `prices.${fixedPriceKey}`, _.get(existingPrices, fixedPriceKey));
            }
        }

        try {
            this.initComponentPrices({
                packageProduct,
                priceKeyValuesMap,
                pricesMap,
                productPrice,
            });
        } catch (err) {
            return null;
        }

        _.forOwn(priceKeyValuesMap, (prices, priceKey) => {
            hasCalculatedPrice = this.applyPackagePrices({
                fixedPriceKeysSet,
                hasCalculatedPrice,
                ignoreEmptyComponentPrices,
                packageProduct,
                packagedProductPriceRound,
                priceKey,
                prices,
                productPrice,
            });
        });

        if (!hasCalculatedPrice) {
            productPrice.isFixed = true;
        }

        ObjectHelperService.removeNil(productPrice.prices);
        if (_.isEmpty(productPrice.prices)) {
            return null;
        }

        return ObjectHelperService.removeNil(productPrice);
    }

    private initComponentPrices({
        packageProduct,
        priceKeyValuesMap,
        pricesMap,
        productPrice,
    }: {
        packageProduct: ProductPackageData;
        priceKeyValuesMap: { [priceKey: string]: number[] };
        pricesMap: { [productKey: string]: ProductPrice };
        productPrice: ProductPrice;
    }): void {
        const logPrefix = `${this.constructor.name}: calculatePackagePricesForProduct:`;

        // Convenience method to add a price to the priceKeyValuesMap
        const setPriceValuesForKey = (priceKey, price, quantity) => {

            // Treat null / undefined same as 0
            if (!_.isFinite(price)) {
                price = 0.0;
            }

            priceKeyValuesMap[priceKey] = priceKeyValuesMap[priceKey] || [];
            priceKeyValuesMap[priceKey].push((price as number) * quantity);
        };

        let missingComponentPrice = false;
        _.forEach(packageProduct.components, (component) => {
            const componentKey = ProductService.getProductKey(component);
            const componentPrices = pricesMap[componentKey];

            if (!componentPrices) {
                console.warn(`${logPrefix} Missing component price: `, component);
                missingComponentPrice = true;
                return false;
            }

            // Inherit common properties from component
            productPrice.accountId = componentPrices.accountId;
            productPrice.priceListId = componentPrices.priceListId;

            _.forOwn(componentPrices.prices, (priceValue, priceType) => {
                if (priceType === 'rental') {
                    _.forOwn(priceValue, (price, rentalTermId) => {
                        setPriceValuesForKey('rental.' + rentalTermId, price as number, component.quantity);
                    });
                } else {
                    setPriceValuesForKey(priceType, priceValue as number, component.quantity);
                }
            });
        });

        if (missingComponentPrice || !productPrice.priceListId || !productPrice.accountId) {
            throw new Error('Missing a required value to generate package product price: ' + JSON.stringify({
                accountId: productPrice.accountId,
                missingComponentPrice,
                priceListId: productPrice.priceListId,
            }));
        }
    }

    private applyPackagePrices({
        fixedPriceKeysSet,
        hasCalculatedPrice,
        ignoreEmptyComponentPrices,
        packageProduct,
        packagedProductPriceRound,
        priceKey,
        prices,
        productPrice,
    }: {
        fixedPriceKeysSet: Set<string>;
        hasCalculatedPrice: boolean;
        ignoreEmptyComponentPrices: boolean;
        packageProduct: ProductPackageData;
        packagedProductPriceRound: boolean;
        priceKey: string;
        prices: number[];
        productPrice: ProductPrice;
    }): boolean {
        const componentCount = _.size(packageProduct.components);

        if (fixedPriceKeysSet.has(priceKey)) {
            return;
        }

        if (_.size(prices) !== componentCount) {
            // We are missing a component value for this field - append a 0.0 to force empty price handling
            prices.push(0.0);
        }

        let summedPrice = 0.0;
        const centsValues = new Set<number>();

        _.forEach(prices, (price) => {
            if (!ignoreEmptyComponentPrices && (price === 0 || price === 0.0)) {
                summedPrice = null;
                return false;
            }

            // Track rounding values of components for use on summed total
            centsValues.add(Math.floor(price * 100) - (Math.floor(price) * 100));

            summedPrice += price;
        });

        if (!_.isNull(summedPrice)) {
            hasCalculatedPrice = true;

            // Compute rounded price if this flag is enabled, and we have consistent rounded components
            if (packagedProductPriceRound && centsValues.size === 1) {
                const roundedPrice = ((Math.floor(summedPrice) * 100) + _.first(Array.from(centsValues))) / 100;

                // Only apply the rounded price if it's greater than or equal to the raw summed value to avoid
                // unwanted price loss
                if (roundedPrice >= summedPrice) {
                    summedPrice = roundedPrice;
                }
            }

            _.set(productPrice.prices, priceKey, _.round(summedPrice, 2));
        }

        return hasCalculatedPrice;
    }

    private calculatePackagePrices(
        packageProductPackageData: ProductPackageData[],
        pricesMap: { [id: string]: ProductPrice },
    ): ProductPrice[] {
        const ignoreEmptyComponentPrices = _.get(
            this.accountSettings,
            'packagedProductShowPriceIfComponentZero',
            false,
        );
        return _.filter(_.map(
            packageProductPackageData,
            (packageProduct) => this.calculatePackagePricesForProduct(
                packageProduct,
                pricesMap,
                ignoreEmptyComponentPrices,
            ),
        ));
    }

    private calculateProductPriceForField(
        product: Product,
        priceRule: PriceRule,
        field: string,
        rentalInstallments?: number,
    ): number {
        const calculation = _.get(
            priceRule,
            'calculation.' + field,
        ) as PriceFieldCalculationRule;
        return this.calculateProductPriceWithFieldRule(product, calculation, rentalInstallments);
    }

    private async deleteProductPrices(query: {
        accountId?: string;
        isFixed?: boolean;
        priceListId?: string;
        priceRuleId?: string;
        productRefs?: ProductRef[];
    }): Promise<void> {
        const filterQuery: Filter<ProductPrice> = _.pick(query, [
            'accountId',
            'isFixed',
            'priceListId',
            'priceRuleId',
        ]);

        // Disallow an empty query filter for one of the primary account-defining keys
        if (_.isEmpty(_.pick(filterQuery, ['accountId', 'priceListId', 'priceRuleId']))) {
            throw new HttpError('You must include a valid indexed field when querying for product prices', 400);
        }

        let productBulkDelete = [];

        if (!_.isEmpty(query.productRefs)) {
            productBulkDelete = _.map(query.productRefs, (productRef) => ({
                deleteOne: {
                    filter: {
                        ...filterQuery,
                        ..._.pick(productRef, ['brand', 'sku']),
                    },
                },
            }));
        }

        const productPriceCollection = await MongoService.getCollection(ProductPriceService.collectionName);

        if (!_.isEmpty(productBulkDelete)) {
            await productPriceCollection.bulkWrite(productBulkDelete, {
                ordered: false,
            });
        } else {
            await productPriceCollection.deleteMany(filterQuery, MongoHelperService.getDefaultWriteConcern());
        }
    }

    private async doesAccountSumPackageProductPrices(accountId: string): Promise<boolean> {
        await this.initAccountSettings(accountId);
        const packageProductCalcMethod = _.get(
            this.accountSettings,
            'packagedProductPriceCalculationMethod',
            PackageCalculationMethod.mergedCost,
        );
        return packageProductCalcMethod === PackageCalculationMethod.mergedPrice;
    }

    private getChangedCalculatedPrices(
        products: Product[],
        accountId: string,
        productPricesMap: {[id: string]: ProductPrice},
        originalProductPricesMap: {[id: string]: ProductPrice},
        priceRuleMap: {[id: string]: PriceRule},
    ): ProductPrice[] {
        const logPrefix = `${this.constructor.name}: getChangedCalculatedPrices:`;
        const productPricesToSave: ProductPrice[] = [];
        _.forEach(products, (product) => {
            try {
                const productKey = ProductService.getProductKey(product);

                // Get the Price Rule ID for this Product
                const priceRuleId = productPricesMap[productKey]?.priceRuleId;

                if (_.isEmpty(priceRuleId)) {
                    throw new Error(`Price Rule ID not set for product: ${ProductService.getProductKey(product)}`);
                }

                // Get the Price Rule by ID
                const priceRule = _.get(priceRuleMap, priceRuleId);
                if (_.isEmpty(priceRule)) {
                    throw new Error(`Price Rule not found for ID: ${priceRuleId}`);
                }

                const existingProductPrices: ProductPrice = originalProductPricesMap[productKey];

                // Get the calculated product price
                const calculatedProductPrices = this.calculateProductPricesWithPriceRule(
                    accountId,
                    product,
                    priceRule,
                    _.get(existingProductPrices, 'fixedPriceKeys'),
                    _.get(existingProductPrices, 'prices'),
                );

                // Compare the calculated price to the existing price
                if (!_.isEqual(calculatedProductPrices, existingProductPrices)) {
                    productPricesToSave.push(calculatedProductPrices);
                    productPricesMap[ProductService.getProductKey(product)] = calculatedProductPrices;
                }

            } catch (err) {
                console.error(`${logPrefix} Unable to price product: `, err);
            }
        });
        return productPricesToSave;
    }

    private async getMergedProductPriceMapForPriceLists(
        priceLists: PriceList[],
        productRefsToPrice: ProductRef[],
    ): Promise<{[id: string]: ProductPrice}> {
        const productPriceMap = {};
        for (const priceList of priceLists) {
            const productPrices = await this.queryProductPrices({
                priceListId: priceList.id,
                productRefs: productRefsToPrice,
            });

            _.forEach(productPrices, (productPrice) => {
                const productKey = ProductService.getProductKey(productPrice);

                // Only respect the first encountered price for this product (to disambiguate promo price lists)
                if (_.isEmpty(productPriceMap[productKey])) {
                    if (priceList.type === 'promotional') {
                        productPrice.isPromoPrice = true;
                    }

                    productPriceMap[productKey] = productPrice;
                }
            });
        }

        return productPriceMap;
    }

    private getRentalTermById(rentalId: string): RentalTerm {
        const logPrefix = 'ProductPrice: getRentalTermById:';

        if (_.isNil(this.rentalTermMap)) {
            throw new Error('Rental Term Map must be initialized before calculating prices');
        }

        const rentalTerm = this.rentalTermMap[rentalId];

        if (!rentalTerm) {
            console.error(`${logPrefix} Cannot find rental term with ID: `, rentalId);
            return;
        }

        return rentalTerm;
    }

    private hasNumberValue(possibleNumberValue) {
        return (
            _.isNumber(possibleNumberValue) ||
            !_.isEmpty(possibleNumberValue)
        );
    }

    private async matchAndProcessProductsWithPriceRules(
        priceList: PriceList,
        priceListProductsToRuleMatch: ProductRef[],
        productPricesMap: {[id: string]: ProductPrice},
        priceListProductsToPrice: ProductRef[],
        productPricesToDelete: ProductRef[]
    ): Promise<void> {
        const logPrefix = 'ProductPriceService: matchProductsAndPriceRules:';
        const matchedProductsToPriceRules = await this.matchPriceRulesForPriceListAndProducts(
            priceList,
            priceListProductsToRuleMatch,
        );

        _.forEach(matchedProductsToPriceRules, (matchedProductToPriceRule) => {
            const matchedPriceRuleId = _.get(matchedProductToPriceRule.priceRule, 'id');
            const productKey = ProductService.getProductKey(matchedProductToPriceRule.product);
            const existingPriceRuleId = productPricesMap[productKey]?.priceRuleId;

            // If we failed to match the product to a price rule...
            if (!matchedPriceRuleId) {
                if (priceList.type === 'promotional') {

                    // Delete unmatched products for existing promotional price rules
                    if (existingPriceRuleId) {
                        productPricesToDelete.push(matchedProductToPriceRule.product);
                    }
                } else {
                    console.error(`${logPrefix} Error matching price list and product: `,
                        priceList.id, matchedProductToPriceRule.product);
                }
                return;
            }

            if (existingPriceRuleId !== matchedPriceRuleId) {

                // Add this product to be priced
                priceListProductsToPrice.push(
                    _.pick(matchedProductToPriceRule.product, ProductPriceService.productRefFields) as ProductRef,
                );

                // Update the priceRuleId
                if (!productPricesMap[productKey]) {
                    productPricesMap[productKey] = {
                        accountId: priceList.accountId,
                        brand: matchedProductToPriceRule.product.brand,
                        isFixed: false,
                        priceListId: priceList.id,
                        prices: {},
                        sku: matchedProductToPriceRule.product.sku,
                    };
                }

                productPricesMap[productKey].priceRuleId = matchedPriceRuleId;
            }
        });
    }

    private async processAccountSettingsChange(priceElementChangeJob: PriceElementChangeJob): Promise<void> {
        // Ensure we use the latest version of account settings for all computations
        await this.initAccountSettings(priceElementChangeJob.accountId);

        // Abort early if no change is required
        const packageRepricingRequired = AccountSettingsService.isPackageRepricingRequired(
            priceElementChangeJob.elementBeforeChange as AccountSettings,
            this.accountSettings,
        );

        // Check to see if list or rental pricing has been disabled
        const pricesHaveBeenDisabled = AccountSettingsService.havePricesBeenDisabled(
            priceElementChangeJob.elementBeforeChange as AccountSettings,
            this.accountSettings,
        );

        // Check to see if list or rental pricing has been enabled
        const pricesHaveBeenEnabled = AccountSettingsService.havePricesBeenEnabled(
            priceElementChangeJob.elementBeforeChange as AccountSettings,
            this.accountSettings,
        );

        // Nothing to do
        if (!packageRepricingRequired && !pricesHaveBeenDisabled && !pricesHaveBeenEnabled) {
            return;
        }

        // Reprice all products in the Account
        const priceListService = new PriceListService();
        const priceLists = await priceListService.queryPriceList({
            accountId: priceElementChangeJob.accountId,
        });

        // Iterate over price lists
        for (const priceList of priceLists ?? []) {
            await this.processAccountSettingsChangeForPriceList(
                priceList,
                pricesHaveBeenDisabled,
                pricesHaveBeenEnabled,
                packageRepricingRequired,
            );
        }
    }

    private async processAccountSettingsChangeForPriceList(
        priceList: PriceList,
        pricesHaveBeenDisabled: boolean,
        pricesHaveBeenEnabled: boolean,
        packageRepricingRequired: boolean,
    ): Promise<void> {
        const priceListService = new PriceListService();

        if (priceList.type === 'promotional' && !await priceListService.hasPromoPricesCalculated(priceList.id)) {
            return;
        }

        // Grab all existing product prices
        const existingProductPrices = await this.queryProductPrices({priceListId: priceList.id}) ?? [];

        // Get package data for the existing prices
        const packageData = await ProductSelectionService.fetchPackageData(
            priceList.accountId,
            existingProductPrices,
        );

        const packageProducts = _.filter(packageData, (productPackageData) => productPackageData.isPackage);

        const productsToRuleMatch = [];
        const productsToPriceMap = new Map<string, ProductRef>();

        // If disabling prices, remove the impacted values from existing prices
        if (pricesHaveBeenDisabled) {
            const productPricesToSave = [];

            for (const existingProductPrice of existingProductPrices) {
                // Remove list and/or rental prices for existing price products
                const newProductPrice = _.cloneDeep(existingProductPrice);
                this.removeDisabledPriceTypes(newProductPrice);

                if (!_.isEqual(newProductPrice, existingProductPrice)) {
                    productPricesToSave.push(newProductPrice);
                }
            }

            // Save prices
            await this.saveProductPrices(productPricesToSave);
        } else if (pricesHaveBeenEnabled) {
            // If prices have been switched from disabled to enabled, recalculate prices in case there are existing
            // price rules with list or rental pricing configured
            for (const existingProductPrice of existingProductPrices) {
                const {brand, sku} = existingProductPrice;
                productsToPriceMap.set(ProductService.getProductKey(existingProductPrice), {brand, sku});
            }
        }

        // If package product calculations changed, re-match and re-price all packaged products
        if (packageRepricingRequired) {
            for (const packageProduct of packageProducts) {
                const {brand, sku} = packageProduct;
                productsToPriceMap.set(ProductService.getProductKey(packageProduct), {brand, sku});
                productsToRuleMatch.push({brand, sku});
            }
        }

        if (productsToRuleMatch.length === 0 && productsToPriceMap.size === 0) {
            return;
        }

        await this.updatePricesForPriceList(
            priceList,
            false,
            productsToRuleMatch,
            Array.from(productsToPriceMap.values())
        );
    }

    private removeDisabledPriceTypes(productPrice: ProductPrice): void {
        if (!this.accountSettings) {
            throw new Error('Account Settings not initialized. Cannot remove disabled price types');
        }

        if (this.accountSettings?.listPricingEnabled === false) {
            delete productPrice.prices.list;
            _.pull(productPrice.fixedPriceKeys ?? [], 'list');
        }

        if (this.accountSettings?.rentalPricingEnabled === false) {
            delete productPrice.prices.rental;
            _.remove(productPrice.fixedPriceKeys ?? [], (fixedPriceKey) => _.startsWith(fixedPriceKey, 'rental'));
        }
    }

    private roundDown({
        cents,
        dollars,
        dollarsRounded,
        price,
        roundDecimalValue,
        roundDividend,
    }: {
        cents: string;
        dollars: number;
        dollarsRounded: number;
        price: string;
        roundDecimalValue: string;
        roundDividend: number;
    }): string {
        if (dollarsRounded > dollars) {
            dollarsRounded -= roundDividend;
        }

        if (this.hasNumberValue(roundDecimalValue)) {
            if (parseFloat(String(dollarsRounded) + '.' + roundDecimalValue) > parseFloat(price)) {
                dollarsRounded -= roundDividend;
            }
            price = String(dollarsRounded) + '.' + _.padEnd(roundDecimalValue.toString(), 2, '0');
        } else {
            price = String(dollarsRounded) + '.' + cents;
        }

        // If the resulting price is less than 0, revert to the original price
        if (parseFloat(price) < 0) {
            price = String(dollars) + '.' + cents;
        }

        return price;
    }

    private roundUp({
        cents,
        dollars,
        dollarsRounded,
        price,
        roundDecimalValue,
        roundDividend,
    }: {
        cents: string;
        dollars: number;
        dollarsRounded: number;
        price: string;
        roundDecimalValue: string;
        roundDividend: number;
    }): string {
        if (dollarsRounded < dollars) {
            dollarsRounded += roundDividend;
        }

        if (this.hasNumberValue(roundDecimalValue)) {
            if (parseFloat(String(dollarsRounded) + '.' + roundDecimalValue) < parseFloat(price)) {
                dollarsRounded += roundDividend;
            }
            price = String(dollarsRounded) + '.' + _.padEnd(roundDecimalValue.toString(), 2, '0');
        } else {
            price = String(dollarsRounded) + '.' + cents;
        }
        return price;
    }

    private async updatePricesForPriceList(
        priceList: PriceList,
        reMatchAll: boolean,
        productsToRuleMatch?: ProductRef[],
        productsToPrice?: ProductRef[],
    ): Promise<void> {
        const logPrefix = `${this.constructor.name}: updatePricesForPriceList:`;

        // Ignore price updates for promo price lists unless they already have prices
        const priceListService = new PriceListService();
        if (priceList.type === 'promotional' && !await priceListService.hasPromoPricesCalculated(priceList.id)) {
            return;
        }

        console.log(`${logPrefix} Updating prices for priceList: `, priceList.id);
        const priceRuleService = new PriceRuleService();

        // Fetch Price Rules, Product Prices for this Price List and init Account Settings
        const [priceRules, productPrices] = await Promise.all([
            priceRuleService.queryPriceRule({priceListIds: [priceList.id]}),
            this.queryProductPrices({priceListId: priceList.id}),
            this.initAccountSettings(priceList.accountId),
        ]);

        // Build map of current Product Prices
        const productPricesMap = _.zipObject(
            _.map(productPrices, (productPrice) => ProductService.getProductKey(productPrice)),
            productPrices,
        );
        const originalProductPricesMap = _.cloneDeep(productPricesMap);

        // If rematch all - use all the existing product price references as the source
        if (reMatchAll) {
            let existingProducts = productPrices;

            // For promotional price lists, use the products from the default price list for the "all" rematch
            if (priceList.type === 'promotional') {
                const defaultPriceList = _.first(await priceListService.queryPriceList({
                    accountId: priceList.accountId,
                    type: 'default',
                }));
                existingProducts = await this.queryProductPrices({
                    priceListId: _.get(defaultPriceList, 'id'),
                });
            }

            productsToRuleMatch = _.map(existingProducts, (productPrice) => _.pick(productPrice, ['brand', 'sku']));
        }

        // Filter out fixed prices from products to price and rule match
        const priceListProductsToPrice = _.filter(
            productsToPrice,
            (productToPrice) => !(productPricesMap[ProductService.getProductKey(productToPrice)]?.isFixed ?? false)
        );
        const priceListProductsToRuleMatch = _.filter(
            productsToRuleMatch,
            (productToRuleMatch) => !(
                productPricesMap[ProductService.getProductKey(productToRuleMatch)]?.isFixed ?? false
            )
        );

        const productPricesToDelete: ProductRef[] = [];

        // Check which package calculation method we should use
        const productPackageService = new ProductPackageService();
        let packageData: ProductPackageData[];
        let packageProductsToReprice: ProductPackageData[];
        const packageProductCalcMethod = _.get(
            this.accountSettings,
            'packagedProductPriceCalculationMethod',
            PackageCalculationMethod.mergedCost,
        );
        const sumPackageProductPrices = packageProductCalcMethod === PackageCalculationMethod.mergedPrice;

        if (sumPackageProductPrices) {

            // Fetch package data for all products we'll be working with
            packageData = await ProductSelectionService.fetchPackageData(priceList.accountId, _.unionBy(
                priceListProductsToRuleMatch,
                priceListProductsToPrice,
                ProductService.getProductKey.bind(ProductService),
            ));

            // Filter out packaged products since they are not rule-matched
            packageProductsToReprice = productPackageService.filterPackageProducts(
                priceListProductsToRuleMatch,
                packageData,
            );
        }

        if (!_.isEmpty(priceListProductsToRuleMatch)) {
            await this.matchAndProcessProductsWithPriceRules(
                priceList,
                priceListProductsToRuleMatch,
                productPricesMap,
                priceListProductsToPrice,
                productPricesToDelete,
            );
        }

        // If we're summing package products, extract package products to price
        if (sumPackageProductPrices) {
            packageProductsToReprice = _.unionBy(
                packageProductsToReprice,
                productPackageService.filterPackageProducts(priceListProductsToPrice, packageData),
                ProductService.getProductKey.bind(ProductService),
            );
        }

        // Fetch matching products for priceListProductsToPrice
        const [products] = await Promise.all([
            ProductSelectionService.fetch(
                priceList.accountId,
                priceListProductsToPrice,
                ProductPriceService.propertiesImpactingPriceCalculation,
            ),
            this.initRentalTermMap(priceList.accountId),
        ]);

        // Build map of Price Rules
        const priceRuleMap = _.zipObject(
            _.map(priceRules, 'id'),
            priceRules,
        );

        const productPricesToSave = this.getChangedCalculatedPrices(
            products,
            priceList.accountId,
            productPricesMap,
            originalProductPricesMap,
            priceRuleMap,
        );

        // Delete product prices
        if (!_.isEmpty(productPricesToDelete)) {
            await this.deleteProductPrices({
                isFixed: false,
                priceListId: priceList.id,
                productRefs: productPricesToDelete,
            });
        }

        if (!_.isEmpty(productPricesToSave)) {
            await this.saveProductPrices(productPricesToSave);

            if (sumPackageProductPrices) {

                // Get package products from these components
                packageProductsToReprice = _.unionBy(
                    packageProductsToReprice,
                    productPackageService.getPackageProductsWithComponents(productPricesToSave, packageData),
                    ProductService.getProductKey.bind(ProductService),
                );
            }
        }

        // Calculated summed packaged products if appropriate
        if (sumPackageProductPrices && !_.isEmpty(packageProductsToReprice) && priceList.type === 'default') {
            await this.calculateFilterSavePackagePrices(packageProductsToReprice, productPricesMap);
        }
    }

    private async updatePricesForPriceRuleId(priceRuleId: string): Promise<void> {
        const logPrefix = `${this.constructor.name}: updatePricesForPriceRuleId:`;
        console.log(`${logPrefix} Updating prices for priceRuleId: `, priceRuleId);
        const priceRuleService = new PriceRuleService();
        const [productPrices, priceRules] = await Promise.all([
            this.queryProductPrices({priceRuleId}),
            priceRuleService.queryPriceRule({ids: [priceRuleId]}),
        ]);
        const priceRule: PriceRule = _.first(priceRules);

        if (_.isEmpty(priceRule)) {
            throw new Error(`Price Rule not found for ID: ${priceRuleId}`);
        }

        // Fetch products from these price rules and init required data
        const [products] = await Promise.all([
            ProductSelectionService.fetch(
                priceRule.accountId,
                _.map(
                    productPrices,
                    (productPrice) => _.pick(productPrice, ProductPriceService.productRefFields),
                ) as ProductRef[],
                ProductPriceService.propertiesImpactingPriceCalculation,
            ),
            this.initRentalTermMap(priceRule.accountId),
            this.initAccountSettings(priceRule.accountId),
        ]);

        const productPricesMap = _.zipObject(
            _.map(productPrices, (productPrice) => ProductService.getProductKey(productPrice)),
            productPrices,
        );
        const productPricesToSave = [];
        for (const product of (products ?? [])) {
            try {
                const existingProductPrices = productPricesMap[ProductService.getProductKey(product)];

                // Get the calculated product price
                const calculatedProductPrices = this.calculateProductPricesWithPriceRule(
                    priceRule.accountId,
                    product,
                    priceRule,
                    _.get(existingProductPrices, 'fixedPriceKeys'),
                    _.get(existingProductPrices, 'prices'),
                );

                // Compare the calculated price to the existing price
                if (!_.isEqual(calculatedProductPrices, existingProductPrices)) {
                    productPricesToSave.push(calculatedProductPrices);

                    // Update product prices map
                    productPricesMap[ProductService.getProductKey(product)] = calculatedProductPrices;
                }
            } catch (err) {
                console.error(`${logPrefix} Unable to price product: `, err);
            }
        }

        if (!_.isEmpty(productPricesToSave)) {
            await this.saveProductPrices(productPricesToSave);

            // Update Package Product prices
            await this.updatePackagePricesForComponents(
                productPricesToSave,
                priceRule.accountId,
                priceRule.priceListId,
            );
        }
    }

    private updateProductPriceMapWithPromoPackageProducts(
        productPriceMap: {[id: string]: ProductPrice},
        packageProducts: ProductPackageData[],
        productRefs: ProductRef[],
    ): void {
        if (_.isEmpty(packageProducts)) {
            return;
        }

        const packagePrices = this.calculatePackagePrices(packageProducts, productPriceMap);

        _.forEach(packagePrices, (packagePrice) => {
            const productKey = ProductService.getProductKey(packagePrice);

            // Don't apply calculated price if there is a current fixed price set
            const isFixed = _.get(productPriceMap[productKey], 'isFixed');
            if (!isFixed) {

                // If the calculated price is different from the price from the default, it must be a promo price
                if (!_.isEqual(productPriceMap[productKey], packagePrice)) {
                    packagePrice.isPromoPrice = true;
                }

                productPriceMap[productKey] = packagePrice;
            }
        });

        // Filter out any extra components not in the original product refs list
        const productRefsMap = _.keyBy(productRefs, (productRef) => ProductService.getProductKey(productRef));
        _.forEach(_.keys(productPriceMap), (productKey) => {
            if (!productRefsMap[productKey]) {
                delete productPriceMap[productKey];
            }
        });
    }
}
