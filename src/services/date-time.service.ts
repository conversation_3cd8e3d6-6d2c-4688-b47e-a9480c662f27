import {MongoService} from '@wondersign/serverless-services';
import * as moment from 'moment-timezone';
import {TimezoneAccount} from '../models/timezone-account';
import {MongoHelperService} from './mongo-helper.service';

export class DateTimeService {
    private static readonly timezoneCollectionName: string = 'timezone';

    public static convertRelativeTimestampToDate(timestamp: string, timezone: string): Date {
        return moment.tz(timestamp, timezone).toDate();
    }

    public static get24HourWithMeridiem(baseHour: string, meridiem: 'am' | 'pm'): string {
        if (meridiem === 'am') {
            return baseHour === '12' ? '00' : baseHour;
        }

        // 12 pm is still 12 in the 24 hour clock
        if (baseHour === '12') {
            return '12';
        }

        return String(Number(baseHour) + 12);
    }

    public static async getTimezonesGroupedByOffset(): Promise<Array<{
        accountIds: string[];
        offset: string;
        timezones: string[];
    }>> {
        const timezoneCollection = await MongoService.getCollection<TimezoneAccount>(this.timezoneCollectionName);
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        return timezoneCollection.aggregate([
            {
                $group: {
                    _id: '$offset',
                    accountIds: {
                        $addToSet: '$accountId',
                    },
                    timezones: {
                        $addToSet: '$timezone',
                    },
                },
            },
            {
                $project: {
                    accountIds: 1,
                    offset: '$_id',
                    timezones: 1,
                },
            },
        ]).toArray();
    }

    public static isRelativeTimeMatchForUtcTimestamp(
        relativeDate: string,
        relativeHour: string,
        relativeMinute: string,
        timezone: string,
        utcTimestamp: string,
    ): boolean {
        const relativeTimestamp = `${relativeDate} ${relativeHour}:${relativeMinute}`;
        return moment.utc(utcTimestamp).tz(timezone).format('Y-MM-DD HH:mm') === relativeTimestamp;
    }

    public static async trackTimezoneForAccountId(timezone: string, accountId: string): Promise<void> {
        const timezoneCollection = await MongoService.getCollection<TimezoneAccount>(this.timezoneCollectionName);
        const offset = moment().tz(timezone).format('Z');
        await timezoneCollection.updateOne({
            accountId,
            timezone,
        }, {
            $set: {
                accountId,
                lastRequestedAt: new Date(),
                offset,
                timezone,
            },
        }, {
            ...MongoHelperService.getDefaultWriteConcern(),
            upsert: true,
        });
    }
}
