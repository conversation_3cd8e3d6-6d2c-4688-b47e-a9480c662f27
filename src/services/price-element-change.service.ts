import {AwsSnsService, AwsSqsService, MongoService} from '@wondersign/serverless-services';
import * as _ from 'lodash';
import {ObjectId} from 'mongodb';
import {PriceElementChangeJob} from '../models/price-element-change-job';
import {MongoHelperService} from './mongo-helper.service';

const config = process.env;

export class PriceElementChangeService {
    private static collectionName = 'priceElementChangeJob';
    private static queueLockCollectionName = 'priceElementChangeQueueLock';

    public static async deletePriceElementChangeJobsWithAccountId(accountId: string): Promise<void> {
        const priceElementChangeCollection = await MongoService.getCollection(PriceElementChangeService.collectionName);
        await priceElementChangeCollection.deleteMany({accountId});
    }

    public static async getPriceElementChangesForAccountId(accountId: string): Promise<PriceElementChangeJob[]> {
        const priceElementChangeJobCollection = await MongoService.getCollection(this.collectionName);
        const priceElementChanges = MongoHelperService.convertOidToId<PriceElementChangeJob & { _id: ObjectId }>(
            await priceElementChangeJobCollection.find(
                {accountId},
            ).toArray(),
        );

        // Override the ID with the batchJobId if set (for consistent batch job handling)
        return _.map(priceElementChanges, (priceElementChange) => {
            if (priceElementChange.batchJobId) {
                priceElementChange.id = priceElementChange.batchJobId;
            }
            return priceElementChange;
        });
    }

    public static async hasExistingJobForAccount(accountId: string): Promise<boolean> {
        const priceElementChangeJobCollection = await MongoService.getCollection(this.collectionName);

        // Check to see if there is already at least 1 job already for this account
        const existingJobForThisAccount = await priceElementChangeJobCollection.findOne({
            accountId,
        }, {
            projection: {
                _id: 1,
            },
        });

        return !!existingJobForThisAccount;
    }

    public static async publishPriceElementChangeJobCompletion(
        priceElementChangeJob: PriceElementChangeJob,
    ): Promise<void> {
        const notificationData = _.pick(priceElementChangeJob, [
            'accountId',
            'context',
            'elementId',
            'elementType',
            'id',
        ]);

        // Override ID to use batchJob ID if it's set (since this is what would be provided to the consumer)
        if (priceElementChangeJob.batchJobId) {
            notificationData.id = priceElementChangeJob.batchJobId;
        }

        await AwsSnsService.sendSnsMessage(
            notificationData,
            config.priceElementChangeJobTopic,
            'Price Element Change Job Completion',
            config.awsAccountId
        );
    }

    public static async queueAccountId(accountId: string): Promise<void> {
        const logPrefix = 'PriceElementChangeService: queueAccountId:';

        if (!await this.acquireQueueLockForAccount(accountId)) {
            console.log(`${logPrefix} Unable to acquire queue lock - Account must already be queued`);
            return;
        }

        const awsSqsService = new AwsSqsService();
        let priceElementChangeJobQueueName = config.priceElementChangeJobQueueName;

        // Currently, buildQueueUrlFromConfig prepends the namespace to the queue name, so we should strip it if set
        if (_.startsWith(priceElementChangeJobQueueName, config.namespace + '-')) {
            priceElementChangeJobQueueName = priceElementChangeJobQueueName.substring(_.size(config.namespace) + 1);
        }

        await awsSqsService.sendDataToQueue(
            awsSqsService.buildQueueUrlFromConfig(priceElementChangeJobQueueName, {
                awsAccountId: config.awsAccountId,
                awsRegion: config.awsRegion,
                namespace: config.namespace,
            }),
            [{accountId}],
            1,
        );
    }

    public static async queuePriceElementChangeJob(priceElementChangeJob: PriceElementChangeJob): Promise<string> {
        if (!_.get(priceElementChangeJob, 'accountId')) {
            throw new Error('priceElementChangeJob requires an accountId');
        }

        // Set the createdAt time. This allows us to grab the oldest job in shiftPriceElementChangesForAccountId()
        priceElementChangeJob.createdAt = (new Date()).toISOString();

        // Store the details of the record in the DB for future handling
        const priceElementChangeJobCollection = await MongoService.getCollection(this.collectionName);
        const insertResult = await priceElementChangeJobCollection.insertOne(priceElementChangeJob);
        priceElementChangeJob.id = insertResult.insertedId.toString();

        // Make sure the accountId is queued
        await this.queueAccountId(priceElementChangeJob.accountId);

        return priceElementChangeJob.batchJobId || priceElementChangeJob.id;
    }

    public static async releaseQueueLockForAccount(accountId: string): Promise<void> {
        const queueLockCollection = await MongoService.getCollection(this.queueLockCollectionName);
        await queueLockCollection.deleteOne({accountId});
    }

    public static async shiftPriceElementChangeForAccountId(accountId: string): Promise<PriceElementChangeJob> {
        const priceElementChangeJobCollection = await MongoService.getCollection(this.collectionName);
        const oldestPriceElementChangeJob = await priceElementChangeJobCollection.findOneAndDelete(
            {accountId},
            {includeResultMetadata: true, sort: {createdAt: 1}},
        );

        if (!_.get(oldestPriceElementChangeJob, 'value')) {
            return null;
        }

        return _.first(MongoHelperService.convertOidToId<PriceElementChangeJob & { _id: ObjectId }>(
            [oldestPriceElementChangeJob.value],
        ));
    }

    private static async acquireQueueLockForAccount(accountId: string): Promise<boolean> {
        const logPrefix = 'PriceElementChangeService: acquireQueueLockForAccount:';
        const queueLockCollection = await MongoService.getCollection(this.queueLockCollectionName);

        try {
            const result = await queueLockCollection.insertOne({
                accountId,
                lockedAt: new Date(),
            });

            return !!result.insertedId;
        } catch (err) {
            // Ignore insert errors due to duplicate key
            if (_.get(err, 'code') !== 11000) {
                console.error(`${logPrefix} Unable to insert lock`, err);
            }

            return false;
        }
    }
}
