{"openapi": "3.0.0", "info": {"version": "1.12.0", "title": "Price Rules Service", "description": "Rule-based calculation system for Product pricing"}, "servers": [{"description": "SwaggerHub API Auto Mocking", "url": "https://virtserver.swaggerhub.com/wondersign/prs/1.9.0"}], "tags": [{"name": "Account <PERSON><PERSON>", "description": "Settings applied Account-wide"}, {"name": "Price Lists", "description": "High level grouping of Price Rules supporting default (everyday) and promotional pricing"}, {"name": "Price Rules", "description": "Detailed rule-based pricing calculation logic"}, {"name": "Product", "description": "Requests to operate on Products"}, {"name": "Rental Terms", "description": "Requests to operate on rental terms"}], "paths": {"/account-settings": {"get": {"tags": ["Account <PERSON><PERSON>"], "description": "Get Account Settings for a specific Account", "parameters": [{"$ref": "#/components/parameters/QueryAccountId"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountSettings"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}, "put": {"tags": ["Account <PERSON><PERSON>"], "description": "Set Account Settings for a specific Account", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountSettings"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PriceElementChangeJobId"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/price-lists": {"post": {"tags": ["Price Lists"], "description": "Create a new Price List", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PriceListWithoutId"}}}}, "responses": {"201": {"description": "Success", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Id"}, {"$ref": "#/components/schemas/Name"}]}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/price-lists/clone": {"post": {"tags": ["Price Lists"], "description": "Clone a Price List. `clonePropertyMap` includes which fields should be cloned from the referenced Price List ID(s).  Optionally include a partial Price List as `newPriceListOverride` for the new Price List.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PriceListCloneConfig"}}}}, "responses": {"201": {"description": "Success", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Id"}, {"$ref": "#/components/schemas/Name"}]}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/price-lists/order": {"put": {"tags": ["Price Lists"], "description": "Set the preferred order of promotional price lists for a specific Account", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"accountId": {"type": "string"}, "priceListIds": {"type": "array", "items": {"type": "string"}}}, "required": ["accountId", "priceListIds"], "type": "object"}}}}, "responses": {"200": {"description": "Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/price-lists/promo-status/{accountId}": {"get": {"tags": ["Price Lists"], "description": "Get Promotional Price Lists with status for an Account with respect to a provided timezone. They will be returned in priority order.", "parameters": [{"$ref": "#/components/parameters/PathAccountId"}, {"$ref": "#/components/parameters/QueryTimezone"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"items": {"allOf": [{"$ref": "#/components/schemas/PriceListWithId"}], "properties": {"status": {"description": "The current status of the promotional price list with respect to the provided timezone", "enum": ["active", "inactive", "planned"], "type": "string"}}, "type": "object"}, "type": "array"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/price-lists/search": {"post": {"tags": ["Price Lists"], "description": "Search for Price Lists.  Common use cases would include finding all Price Lists for an Account or only one type of Price List for the Account like only `default` or only `promotional`. They will be returned in priority order.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"accountId": {"type": "string"}, "type": {"enum": ["default", "promotional"], "type": "string"}}, "required": ["accountId"], "type": "object"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PriceListWithId"}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/price-lists/{priceListId}": {"delete": {"tags": ["Price Lists"], "description": "Delete a Price List", "parameters": [{"$ref": "#/components/parameters/PathPriceListId"}], "responses": {"200": {"description": "Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}, "get": {"tags": ["Price Lists"], "description": "Get a specific Price List", "parameters": [{"$ref": "#/components/parameters/PathPriceListId"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PriceListWithId"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}, "patch": {"tags": ["Price Lists"], "description": "Perform a partial update of a Price List", "parameters": [{"$ref": "#/components/parameters/PathPriceListId"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PriceListWithoutId"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Id"}, {"$ref": "#/components/schemas/Name"}, {"$ref": "#/components/schemas/PriceElementChangeJobId"}]}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}, "put": {"tags": ["Price Lists"], "description": "Fully replace an existing Price List", "parameters": [{"$ref": "#/components/parameters/PathPriceListId"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PriceListWithoutId"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Id"}, {"$ref": "#/components/schemas/Name"}, {"$ref": "#/components/schemas/PriceElementChangeJobId"}]}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/price-lists/{priceListId}/history": {"get": {"tags": ["Price Lists"], "description": "Get Price Rule history for a specific Price List", "parameters": [{"$ref": "#/components/parameters/PathPriceListId"}, {"$ref": "#/components/parameters/QueryLimit"}, {"$ref": "#/components/parameters/QuerySkip"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"items": {"properties": {"accountId": {"type": "string"}, "priceListId": {"type": "string"}, "priceRule": {"$ref": "#/components/schemas/PriceRuleWithId"}, "priceRuleId": {"type": "string"}, "timestamp": {"type": "string"}, "userEmail": {"type": "string"}}, "type": "object"}, "type": "array"}}}, "headers": {"Link": {"description": "Provides \"prev\" and \"next\" link URLs for use with pagination. \nExample https://example.com?skip=0&limit=1000; rel=\"prev\", https://example.com?skip=2000&limit=1000; rel=\"next\"", "schema": {"type": "string"}}, "X-Total-Count": {"description": "Total quantity of history records for this Price List. This amount may be greater than the number of records returned in a single request.", "schema": {"type": "string"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/price-rules": {"post": {"tags": ["Price Rules"], "description": "Create a new price rule", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PriceRuleWithoutId"}}}}, "responses": {"201": {"description": "Success", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Id"}, {"$ref": "#/components/schemas/Name"}, {"$ref": "#/components/schemas/PriceElementChangeJobId"}]}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/price-rules/search": {"post": {"tags": ["Price Rules"], "description": "Search for Price Rules", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"ids": {"items": {"type": "string"}, "type": "array"}, "priceListId": {"type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/PriceRuleWithId"}, "type": "array"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/price-rules/{priceRuleId}": {"delete": {"tags": ["Price Rules"], "description": "Delete a Price Rule", "parameters": [{"$ref": "#/components/parameters/PathPriceRuleId"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Id"}, {"$ref": "#/components/schemas/PriceElementChangeJobId"}]}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}, "get": {"tags": ["Price Rules"], "description": "Get a specific Price Rule", "parameters": [{"$ref": "#/components/parameters/PathPriceRuleId"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PriceRuleWithId"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}, "patch": {"tags": ["Price Rules"], "description": "Perform a partial update of a Price Rule", "parameters": [{"$ref": "#/components/parameters/PathPriceRuleId"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PriceRuleWithoutId"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Id"}, {"$ref": "#/components/schemas/Name"}, {"$ref": "#/components/schemas/PriceElementChangeJobId"}]}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}, "put": {"tags": ["Price Rules"], "description": "Fully replace an existing Price Rule", "parameters": [{"$ref": "#/components/parameters/PathPriceRuleId"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PriceRuleWithoutId"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Id"}, {"$ref": "#/components/schemas/Name"}, {"$ref": "#/components/schemas/PriceElementChangeJobId"}]}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/product/calc/base-price": {"post": {"tags": ["Product"], "description": "Derive the base price for a Product based on a field-level calculation rule", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"basePriceDef": {"enum": ["cost", "costMaxMsrp", "msrp", "msrpPreferred"], "type": "string"}, "product": {"$ref": "#/components/schemas/Product"}}, "type": "object"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "number"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/product/calc/detail": {"post": {"tags": ["Product"], "description": "Perform a detailed calculation of price using a Product and field-level price calculation", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"priceFieldCalculationRule": {"$ref": "#/components/schemas/PriceFieldCalculationRule"}, "product": {"$ref": "#/components/schemas/Product"}, "rentalInstallments": {"type": "number"}}, "type": "object"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"properties": {"basePrice": {"type": "number"}, "calculatedPrice": {"type": "number"}, "expressions": {"items": {"type": "number"}, "type": "array"}, "isFreightPriceApplied": {"description": "Flag that indicates if the freight cost is applied to the retail price value", "type": "boolean"}, "minIsMAPEnforced": {"description": "Whether the minIsMAP setting had to be enforced due to the calculated price", "type": "boolean"}}, "type": "object"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/product/calc/prices-by-rule/{priceRuleId}": {"post": {"tags": ["Product"], "description": "Calculate prices for a provided Product using a referenced Price Rule", "parameters": [{"$ref": "#/components/parameters/PathPriceRuleId"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Product"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Prices"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/product/count-by-rule/{priceRuleId}": {"get": {"tags": ["Product"], "description": "Get a count of Products that are priced due to using this Price Rule", "parameters": [{"$ref": "#/components/parameters/PathPriceRuleId"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "number"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/product/event": {"post": {"tags": ["Product"], "description": "Submit an event for a single product", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["accountId", "productEvent"], "type": "object", "properties": {"accountId": {"type": "string"}, "productEvent": {"allOf": [{"$ref": "#/components/schemas/ProductEvent"}], "required": ["brand", "sku"]}}}}}}, "responses": {"200": {"description": "Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/product/event/batch": {"post": {"tags": ["Product"], "description": "Submit an event for many products", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"accountId": {"type": "string"}, "batchJob": {"$ref": "#/components/schemas/BatchJob"}, "productEvents": {"items": {"$ref": "#/components/schemas/ProductEvent"}, "type": "array"}}, "required": ["accountId", "productEvents"], "type": "object"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PriceElementChangeJobId"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/product/fixed-prices/{priceListId}": {"delete": {"tags": ["Product"], "description": "Delete all fixed prices for a specific price list", "parameters": [{"$ref": "#/components/parameters/PathPriceListId"}], "responses": {"200": {"description": "Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/product/fixed-prices/{priceListId}/batch": {"post": {"tags": ["Product"], "description": "Submit a batch of fixed prices for Products", "parameters": [{"$ref": "#/components/parameters/PathPriceListId"}, {"$ref": "#/components/parameters/QueryExistingNotIncluded"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"accountId": {"type": "string"}, "batchJob": {"$ref": "#/components/schemas/BatchJob"}, "priceListId": {"type": "string"}, "productPrices": {"items": {"properties": {"prices": {"$ref": "#/components/schemas/Prices"}, "product": {"$ref": "#/components/schemas/ProductRef"}}, "type": "object"}, "type": "array"}}, "required": ["accountId", "productPrices"], "type": "object"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PriceElementChangeJobId"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/product/fixed-prices/{priceListId}/{brand}/{sku}": {"delete": {"tags": ["Product"], "description": "Delete fixed prices for a specific Product", "parameters": [{"$ref": "#/components/parameters/PathPriceListId"}, {"$ref": "#/components/parameters/PathBrand"}, {"$ref": "#/components/parameters/PathSku"}], "responses": {"200": {"description": "Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}, "get": {"tags": ["Product"], "description": "Get fixed prices for a specific Product. Only fixed prices will be returned. It's possible the product may also have other prices which are calculated. Use the `/product/prices/active` endpoint to see all effective prices for the product.", "parameters": [{"$ref": "#/components/parameters/PathPriceListId"}, {"$ref": "#/components/parameters/PathBrand"}, {"$ref": "#/components/parameters/PathSku"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Prices"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}, "put": {"tags": ["Product"], "description": "Update fixed prices for a specific Product", "parameters": [{"$ref": "#/components/parameters/PathPriceListId"}, {"$ref": "#/components/parameters/PathBrand"}, {"$ref": "#/components/parameters/PathSku"}, {"$ref": "#/components/parameters/QueryExistingNotIncluded"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Prices"}}}}, "responses": {"200": {"description": "Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/product/price-rule-match": {"post": {"tags": ["Product"], "description": "Find a matching Price Rule within the referenced Price List (by Price List ID) using the provided Product", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"priceListId": {"type": "string"}, "product": {"$ref": "#/components/schemas/Product"}}, "type": "object"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PriceRuleWithId"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/product/prices/active": {"post": {"tags": ["Product"], "description": "Get current active prices.  This will factor in calculated vs fixed prices as well as promotional pricing if timezone is provided.  Timezone, if provided, should be a string in ISO8601 format.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"accountId": {"type": "string"}, "products": {"items": {"$ref": "#/components/schemas/ProductRef"}, "type": "array"}, "timezone": {"type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"items": {"allOf": [{"$ref": "#/components/schemas/ProductPricesRecord"}], "properties": {"isPromoPrice": {"description": "`true` if this price is set due to an active promotional price list", "type": "boolean"}}, "type": "object"}, "type": "array"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/product/prices/package": {"post": {"tags": ["Product"], "description": "Get product package prices.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"accountId": {"type": "string"}, "products": {"items": {"$ref": "#/components/schemas/ProductRef"}, "type": "array"}, "priceListIds": {"items": {"type": "string"}}}, "type": "object"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"items": {"allOf": [{"$ref": "#/components/schemas/ProductPricesRecord"}], "type": "object"}, "type": "array"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/product/prices/pending-changes/{accountId}": {"get": {"tags": ["Product"], "description": "Get a list of changed price elements for which product prices have not yet been updated.", "parameters": [{"$ref": "#/components/parameters/PathAccountId"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/PriceElementChangeJob"}, "type": "array"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/product/prices/search": {"post": {"tags": ["Product"], "description": "Search for product prices. `accountId` is required.  Other search parameters are optional.", "parameters": [{"$ref": "#/components/parameters/QueryLimit"}, {"$ref": "#/components/parameters/QuerySkip"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"accountId": {"type": "string"}, "isFixed": {"description": "The product only has fixed (non-calculated) prices", "type": "boolean"}, "isMixed": {"description": "The product has a mix of fixed and calculated prices", "type": "boolean"}, "priceListId": {"type": "string"}, "priceRuleId": {"type": "string"}, "products": {"items": {"$ref": "#/components/schemas/ProductRef"}, "type": "array"}}, "required": ["accountId"], "type": "object"}}}}, "responses": {"200": {"description": "Success", "headers": {"Link": {"description": "Provides \"prev\" and \"next\" link URLs for use with pagination. \nExample https://example.com?skip=0&limit=1000; rel=\"prev\", https://example.com?skip=2000&limit=1000; rel=\"next\"", "schema": {"type": "string"}}, "X-Total-Count": {"description": "Total quantity of product prices matching your search. This amount may be greater than the number of products returned if it exceeds the provided limit.", "schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/ProductPricesRecord"}, "type": "array"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/rental-terms": {"post": {"tags": ["Rental Terms"], "description": "Create a new Rental Term", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RentalTermWithoutId"}}}}, "responses": {"201": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Id"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/rental-terms/order": {"put": {"tags": ["Rental Terms"], "description": "Set the preferred order of rental terms for a specific Account", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"accountId": {"type": "string"}, "rentalTermIds": {"type": "array", "items": {"type": "string"}}}, "required": ["accountId", "rentalTermIds"], "type": "object"}}}}, "responses": {"200": {"description": "Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/rental-terms/search": {"post": {"tags": ["Rental Terms"], "description": "Get rental terms for a specific Account", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"accountId": {"type": "string"}}, "required": ["accountId"], "type": "object"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/RentalTermWithId"}, "type": "array"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}, "/rental-terms/{rentalTermId}": {"delete": {"tags": ["Rental Terms"], "description": "Delete a Rental Term", "parameters": [{"$ref": "#/components/parameters/PathRentalTermId"}], "responses": {"200": {"description": "Success"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}, "get": {"tags": ["Rental Terms"], "description": "Get a specific Rental Term", "parameters": [{"$ref": "#/components/parameters/PathRentalTermId"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RentalTermWithId"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}, "put": {"tags": ["Rental Terms"], "description": "Fully replace an existing Rental Term", "parameters": [{"$ref": "#/components/parameters/PathRentalTermId"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RentalTermWithoutId"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Id"}]}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"api_key": []}]}}}, "components": {"parameters": {"PathAccountId": {"in": "path", "name": "accountId", "required": true, "schema": {"type": "string"}}, "PathBrand": {"in": "path", "name": "brand", "required": true, "schema": {"type": "string"}}, "PathPriceListId": {"in": "path", "name": "priceListId", "required": true, "schema": {"type": "string"}}, "PathPriceRuleId": {"in": "path", "name": "priceRuleId", "required": true, "schema": {"type": "string"}}, "PathRentalTermId": {"in": "path", "name": "rentalTermId", "required": true, "schema": {"type": "string"}}, "PathSku": {"in": "path", "name": "sku", "required": true, "schema": {"type": "string"}}, "QueryAccountId": {"in": "query", "name": "accountId", "schema": {"type": "string"}}, "QueryExistingNotIncluded": {"description": "Options:\n * `remove` (default) - Remove any existing prices not included. All prices will be fixed.\n * `keep` - Keep any existing prices not included. They could be calculated or fixed.\n * `reset` - Any existing prices not included will be reset to their calculated values.", "in": "query", "name": "existingNotIncluded", "schema": {"enum": ["remove", "keep", "reset"], "type": "string"}}, "QueryLimit": {"name": "limit", "in": "query", "description": "Used to control the total number of results that will be returned. Default is 5000. Max is 5000.", "required": false, "schema": {"type": "string"}}, "QuerySkip": {"name": "skip", "in": "query", "description": "Set this value to skip some records as part of pagination. Default value is 0.", "required": false, "schema": {"type": "string"}}, "QueryTimezone": {"name": "timezone", "in": "query", "description": "Timezone to use for relative time calculations.  If not provided, UTC will be used.", "required": false, "schema": {"type": "string"}}}, "responses": {"Forbidden": {"description": "Forbidden"}, "Unauthorized": {"description": "Unauthorized"}}, "securitySchemes": {"api_key": {"description": "Set this to your API key. It can be obtained from the user settings within the Catalog Kiosk web application.\nhttps://my.catalogkiosk.com/ > Users > (select user) > User Settings", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-api-key", "in": "header"}}, "schemas": {"AccountSettings": {"properties": {"accountId": {"type": "string"}, "listPricingEnabled": {"type": "boolean"}, "packagedProductPriceCalculationMethod": {"enum": ["mergedCost", "mergedPrice"], "type": "string"}, "packagedProductPriceRound": {"description": "If `packagedProductPriceCalculationMethod` is set to `mergedPrice`, this field can be used to determine if the resulting summed package price should be rounded.  If `true`, rounding will occur if the components all have the same cents value and the resulting rounded price is greater than or equal to the summed price.", "type": "boolean"}, "packagedProductShowPriceIfComponentZero": {"type": "boolean"}, "rentalPricingEnabled": {"type": "boolean"}}, "required": ["accountId"], "type": "object"}, "BatchJob": {"properties": {"consumerId": {"type": "string"}, "context": {"additionalProperties": true, "type": "object"}, "jobId": {"type": "string"}, "requestTotalCount": {"type": "number"}}, "required": ["consumerId", "jobId", "requestTotalCount"], "type": "object"}, "ExpressionTypeFreightPrice": {"type": "object", "title": "ExpressionType: FreightPrice", "properties": {"expressionType": {"enum": ["freight"], "type": "string"}, "operation": {"type": "string"}}}, "ExpressionTypeMath": {"type": "object", "title": "ExpressionType: Math", "properties": {"expressionType": {"enum": ["math"], "type": "string"}, "operation": {"enum": ["add", "divide", "margin", "markup", "multiply", "subtract", "turns"], "type": "string"}, "value": {"type": "number"}}}, "ExpressionTypeRound": {"type": "object", "title": "ExpressionType: Round", "properties": {"expressionType": {"enum": ["round"], "type": "string"}, "direction": {"enum": ["down", "up"], "type": "string"}, "integerValue": {"nullable": true, "type": "number"}, "decimalValue": {"nullable": true, "type": "string"}, "maxAdjustment": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "value": {"nullable": true, "type": "number"}, "unit": {"enum": ["finite", "percent"], "type": "string"}}}}}, "Id": {"properties": {"id": {"type": "string"}}, "type": "object"}, "Name": {"properties": {"name": {"type": "string"}}, "type": "object"}, "PriceElementChangeJob": {"type": "object", "allOf": [{"$ref": "#/components/schemas/Id"}], "properties": {"accountId": {"type": "string"}, "elementId": {"description": "The PriceList, PriceRule, or Rental Term ID", "type": "string"}, "elementType": {"description": "The type of element that was changed", "enum": ["accountSettings", "priceList", "priceRule", "productEventBatch", "productFixedPricesSetBatch", "promoPriceListCalculation", "rentalTerm"], "type": "string"}}}, "PriceElementChangeJobId": {"type": "object", "properties": {"priceElementChangeJobId": {"type": "string"}}}, "PriceFieldCalculationRule": {"type": "object", "properties": {"basePriceDef": {"enum": ["cost", "costMaxMsrp", "msrp", "msrpPreferred"], "type": "string"}, "expressions": {"type": "array", "items": {"oneOf": [{"$ref": "#/components/schemas/ExpressionTypeFreightPrice"}, {"$ref": "#/components/schemas/ExpressionTypeMath"}, {"$ref": "#/components/schemas/ExpressionTypeRound"}]}}, "minIsMAP": {"type": "boolean"}}}, "PriceListCloneConfig": {"properties": {"accountId": {"type": "string"}, "clonePropertyMap": {"description": "At least one of these is required. The value is the Price List ID of the source Price List to clone from", "properties": {"layout": {"type": "string"}, "priceRuleIds": {"type": "string"}, "schedule": {"type": "string"}, "type": {"type": "string"}}, "type": "object"}, "newPriceListOverride": {"$ref": "#/components/schemas/PriceListWithoutId"}}}, "PriceListWithId": {"allOf": [{"$ref": "#/components/schemas/Id"}, {"$ref": "#/components/schemas/PriceListWithoutId"}], "title": "Price List with ID", "type": "object"}, "PriceListWithoutId": {"properties": {"accountId": {"type": "string"}, "layout": {"properties": {"saleIcon": {"properties": {"uuid": {"type": "string"}}, "type": "object"}}, "type": "object"}, "name": {"type": "string"}, "priceRuleIds": {"type": "array", "items": {"type": "string"}}, "schedule": {"properties": {"endDate": {"type": "string"}, "endTimeHour": {"maxLength": 2, "minLength": 2, "pattern": "(0[1-9])|(1[0-2])", "type": "string"}, "endTimeMinute": {"maxLength": 2, "minLength": 2, "pattern": "[0-5][0-9]", "type": "string"}, "endTimeMeridiem": {"enum": ["am", "pm"], "type": "string"}, "startDate": {"type": "string"}, "startTimeHour": {"maxLength": 2, "minLength": 2, "pattern": "(0[1-9])|(1[0-2])", "type": "string"}, "startTimeMinute": {"maxLength": 2, "minLength": 2, "pattern": "[0-5][0-9]", "type": "string"}, "startTimeMeridiem": {"enum": ["am", "pm"], "type": "string"}}, "type": "object"}, "type": {"enum": ["default", "promotional"], "type": "string"}}, "title": "Price List without ID", "type": "object"}, "PriceRuleWithId": {"allOf": [{"$ref": "#/components/schemas/Id"}, {"$ref": "#/components/schemas/PriceRuleWithoutId"}], "title": "Price Rule with ID", "type": "object"}, "PriceRuleWithoutId": {"type": "object", "title": "Price Rule without ID", "properties": {"accountId": {"type": "string"}, "calculation": {"type": "object", "properties": {"list": {"$ref": "#/components/schemas/PriceFieldCalculationRule"}, "rental": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/PriceFieldCalculationRule"}}, "retail": {"$ref": "#/components/schemas/PriceFieldCalculationRule"}}}, "enabled": {"type": "boolean"}, "isBaseRule": {"type": "boolean"}, "name": {"type": "string"}, "priceListId": {"type": "string"}, "productSelectionCriteriaOperator": {"enum": ["and", "or"], "type": "string"}, "productSelectionCriteria": {"type": "array", "items": {"properties": {"conditions": {"items": {"properties": {"field": {"pattern": "^basePrice|brand|categoryId|consumerBrand|directShipping|mapPrice|msrpPrice|productType|provider|sku|attributesMap\\.[a-zA-Z0-9]+$", "type": "string"}, "operator": {"enum": ["=", "!=", "in", "nin", ">", ">=", "<", "<="], "type": "string"}, "value": {"oneOf": [{"type": "number"}, {"type": "boolean"}, {"type": "array", "items": {"type": "string"}}]}}, "type": "object"}, "type": "array"}, "operation": {"enum": ["and", "or"], "type": "string"}}, "type": "object"}}, "productSelectionType": {"enum": ["all", "custom"], "type": "string"}}}, "Prices": {"type": "object", "description": "Pricing data for a specific Product", "title": "Prices", "additionalProperties": false, "properties": {"list": {"type": "number", "description": "List / MSRP suggested price", "title": "List"}, "rental": {"type": "object", "description": "Rental / Lease installment multiple payment price. Key corresponds to Rental Term ID", "title": "Rental", "additionalProperties": {"type": "number"}}, "retail": {"type": "number", "description": "Retail one-time purchase price", "title": "Retail"}}}, "Product": {"type": "object", "properties": {"basePrice": {"type": "number"}, "brand": {"type": "string"}, "edges": {"properties": {"category": {"items": {"type": "string"}, "type": "array"}}, "type": "object"}, "express_item": {"enum": ["0", "1"], "type": "string"}, "mapPrice": {"type": "number"}, "msrpPrice": {"type": "number"}, "sku": {"type": "string"}}}, "ProductEvent": {"allOf": [{"$ref": "#/components/schemas/ProductRef"}], "properties": {"changedProperties": {"description": "An array of properties that have changed for this product", "items": {"type": "string"}, "type": "array"}, "isDeleted": {"description": "true if this product was deleted", "type": "boolean"}, "isNew": {"description": "true if this product is being added for the first time", "type": "boolean"}}, "type": "object"}, "ProductPricesRecord": {"type": "object", "allOf": [{"$ref": "#/components/schemas/ProductRef"}], "properties": {"accountId": {"type": "string"}, "fixedPriceKeys": {"description": "List of `prices` keys that have fixed prices. This is only set when `isFixed` is `false` and at least one of the `prices` keys contains a calculated price and at least one contains a fixed price.", "items": {"pattern": "list|rental\\.[a-f0-9]{24}|retail", "type": "string"}, "type": "array"}, "isFixed": {"description": "Is this fixed (non-calculated) pricing for all prices keys?", "type": "boolean"}, "isFreightPriceApplied": {"description": "Flag that indicates if the freight cost is applied to the retail price value", "type": "boolean"}, "priceListId": {"type": "string"}, "priceRuleId": {"type": "string"}, "prices": {"$ref": "#/components/schemas/Prices"}}}, "ProductRef": {"type": "object", "properties": {"brand": {"type": "string"}, "sku": {"type": "string"}}}, "RentalTermWithId": {"allOf": [{"$ref": "#/components/schemas/Id"}, {"$ref": "#/components/schemas/RentalTermWithoutId"}]}, "RentalTermWithoutId": {"properties": {"accountId": {"type": "string"}, "installments": {"type": "number"}, "interval": {"enum": ["bi-weekly", "monthly", "semi-monthly", "weekly"], "type": "string"}, "name": {"type": "string"}}, "type": "object"}}}}