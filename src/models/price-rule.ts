import {ObjectId} from 'mongodb';
import {Components} from './contracts';

export type PriceRule = Components['schemas']['PriceRuleWithId'] & {
    _id?: ObjectId;
};

// Convenience helpers
export type ExpressionTypeMath = Components['schemas']['ExpressionTypeMath'];
export type ExpressionTypeRound = Components['schemas']['ExpressionTypeRound'];
export type ExpressionTypeFreightPrice = Components['schemas']['ExpressionTypeFreightPrice'];
export type PriceFieldCalculationRule = Components['schemas']['PriceFieldCalculationRule'];

export interface PriceRuleArchive {
    accountId: string;
    action: string;
    priceListId: string;
    priceRule: PriceRule;
    priceRuleId: string;
    timestamp: Date;
    userEmail: string;
}
