import { ObjectId } from 'mongodb';
import {ProductRef} from './product-ref';

export interface Product extends ProductRef {
    basePrice?: number;
    directShipping?: boolean;
    edges?: {
        category?: Array<ObjectId | string>;
    };
    // eslint-disable-next-line @typescript-eslint/naming-convention
    express_item?: string;
    mapPrice?: number;
    msrpPrice?: number;
    rates?: Array<{
        service: string;
        rate: number;
    }>;
}

export interface TopicMessageCatkProductBrandChangeRequest {
    provider: string;
    brandCurrent: string;
    brandNew: string;
}
