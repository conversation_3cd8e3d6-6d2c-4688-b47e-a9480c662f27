/**
 * This file was auto-generated by swagger-to-ts.
 * Do not make direct changes to the file.
 */

export interface Components {
  schemas: {
    AccountSettings: {
      accountId: string;
      listPricingEnabled?: boolean;
      packagedProductPriceCalculationMethod?: "mergedCost" | "mergedPrice";
      /**
       * If `packagedProductPriceCalculationMethod` is set to `mergedPrice`, this field can be used to determine if the resulting summed package price should be rounded.  If `true`, rounding will occur if the Components all have the same cents value and the resulting rounded price is greater than or equal to the summed price.
       */
      packagedProductPriceRound?: boolean;
      packagedProductShowPriceIfComponentZero?: boolean;
      rentalPricingEnabled?: boolean;
    };
    BatchJob: {
      consumerId: string;
      context?: { [key: string]: any };
      jobId: string;
      requestTotalCount: number;
    };
    ExpressionTypeFreightPrice: {
      expressionType?: "freight";
      operation?: string;
    };
    ExpressionTypeMath: {
      expressionType?: "math";
      operation?:
        | "add"
        | "divide"
        | "margin"
        | "markup"
        | "multiply"
        | "subtract"
        | "turns";
      value?: number;
    };
    ExpressionTypeRound: {
      expressionType?: "round";
      direction?: "down" | "up";
      integerValue?: number | null;
      decimalValue?: string | null;
      maxAdjustment?: {
        enabled?: boolean;
        value?: number | null;
        unit?: "finite" | "percent";
      };
    };
    Id: { id?: string };
    Name: { name?: string };
    PriceElementChangeJob: Components["schemas"]["Id"] & {
      accountId?: string;
      /**
       * The PriceList, PriceRule, or Rental Term ID
       */
      elementId?: string;
      /**
       * The type of element that was changed
       */
      elementType?:
        | "accountSettings"
        | "priceList"
        | "priceRule"
        | "productEventBatch"
        | "productFixedPricesSetBatch"
        | "promoPriceListCalculation"
        | "rentalTerm";
    };
    PriceElementChangeJobId: { priceElementChangeJobId?: string };
    PriceFieldCalculationRule: {
      basePriceDef?: "cost" | "costMaxMsrp" | "msrp" | "msrpPreferred";
      expressions?: (
        | Components["schemas"]["ExpressionTypeFreightPrice"]
        | Components["schemas"]["ExpressionTypeMath"]
        | Components["schemas"]["ExpressionTypeRound"]
      )[];
      minIsMAP?: boolean;
    };
    PriceListCloneConfig: {
      accountId?: string;
      /**
       * At least one of these is required. The value is the Price List ID of the source Price List to clone from
       */
      clonePropertyMap?: {
        layout?: string;
        priceRuleIds?: string;
        schedule?: string;
        type?: string;
      };
      newPriceListOverride?: Components["schemas"]["PriceListWithoutId"];
    };
    PriceListWithId: Components["schemas"]["Id"] &
      Components["schemas"]["PriceListWithoutId"];
    PriceListWithoutId: {
      accountId?: string;
      layout?: { saleIcon?: { uuid?: string } };
      name?: string;
      priceRuleIds?: string[];
      schedule?: {
        endDate?: string;
        endTimeHour?: string;
        endTimeMinute?: string;
        endTimeMeridiem?: "am" | "pm";
        startDate?: string;
        startTimeHour?: string;
        startTimeMinute?: string;
        startTimeMeridiem?: "am" | "pm";
      };
      type?: "default" | "promotional";
    };
    PriceRuleWithId: Components["schemas"]["Id"] &
      Components["schemas"]["PriceRuleWithoutId"];
    PriceRuleWithoutId: {
      accountId?: string;
      calculation?: {
        list?: Components["schemas"]["PriceFieldCalculationRule"];
        rental?: {
          [key: string]: Components["schemas"]["PriceFieldCalculationRule"];
        };
        retail?: Components["schemas"]["PriceFieldCalculationRule"];
      };
      enabled?: boolean;
      isBaseRule?: boolean;
      name?: string;
      priceListId?: string;
      productSelectionCriteriaOperator?: "and" | "or";
      productSelectionCriteria?: {
        conditions?: {
          field?: string;
          operator?: "=" | "!=" | "in" | "nin" | ">" | ">=" | "<" | "<=";
          value?: number | boolean | string[];
        }[];
        operation?: "and" | "or";
      }[];
      productSelectionType?: "all" | "custom";
    };
    /**
     * Pricing data for a specific Product
     */
    Prices: {
      /**
       * List / MSRP suggested price
       */
      list?: number;
      /**
       * Rental / Lease installment multiple payment price. Key corresponds to Rental Term ID
       */
      rental?: { [key: string]: number };
      /**
       * Retail one-time purchase price
       */
      retail?: number;
    };
    Product: {
      basePrice?: number;
      brand?: string;
      edges?: { category?: string[] };
      express_item?: "0" | "1";
      mapPrice?: number;
      msrpPrice?: number;
      sku?: string;
    };
    ProductEvent: Components["schemas"]["ProductRef"] & {
      /**
       * An array of properties that have changed for this product
       */
      changedProperties?: string[];
      /**
       * true if this product was deleted
       */
      isDeleted?: boolean;
      /**
       * true if this product is being added for the first time
       */
      isNew?: boolean;
    };
    ProductPricesRecord: Components["schemas"]["ProductRef"] & {
      accountId?: string;
      /**
       * List of `prices` keys that have fixed prices. This is only set when `isFixed` is `false` and at least one of the `prices` keys contains a calculated price and at least one contains a fixed price.
       */
      fixedPriceKeys?: string[];
      /**
       * Is this fixed (non-calculated) pricing for all prices keys?
       */
      isFixed?: boolean;
      priceListId?: string;
      priceRuleId?: string;
      prices?: Components["schemas"]["Prices"];
    };
    ProductRef: { brand?: string; sku?: string };
    RentalTermWithId: Components["schemas"]["Id"] &
      Components["schemas"]["RentalTermWithoutId"];
    RentalTermWithoutId: {
      accountId?: string;
      installments?: number;
      interval?: "bi-weekly" | "monthly" | "semi-monthly" | "weekly";
      name?: string;
    };
  };
  responses: {
    /**
     * Forbidden
     */
    Forbidden: { [key: string]: any };
    /**
     * Unauthorized
     */
    Unauthorized: { [key: string]: any };
  };
}
