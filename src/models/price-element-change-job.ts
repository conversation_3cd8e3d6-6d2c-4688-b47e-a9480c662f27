import {ObjectId} from 'mongodb';
import {Components} from './contracts';
import {PriceList} from './price-list';
import {PriceRule} from './price-rule';
import {ProductPrice} from './product-price';
import {Product} from './product';
import {FixedPriceChangeType} from './fixed-price-change-type';

type ProductEvent = Components['schemas']['ProductEvent'];

export interface ProductFixedPricesSet {
    priceListId: string;
    productPrices?: ProductPrice[];
    productsAndPrices?: Array<{prices: ProductPrice['prices']; product: Product}>;
    existingNotIncluded?: FixedPriceChangeType;
}

export type PriceElementChangeJob = Components['schemas']['PriceElementChangeJob'] & {
    _id?: ObjectId;
    batchJobId?: string;
    context?: any;
    createdAt?: string;
    elementBeforeChange?: PriceList | PriceRule;
    elementAfterChange?: PriceList | PriceRule | ProductEvent[] | ProductFixedPricesSet;
};
