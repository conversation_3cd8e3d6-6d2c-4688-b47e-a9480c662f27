import {APIGatewayEvent, Callback, Context} from 'aws-lambda';
import {AwsApiGatewayService, HttpError} from '@wondersign/serverless-services';
import * as _ from 'lodash';
import {AccountSettingsService} from '../services/account-settings.service';
import {AccountPermission} from './helpers/account-permission';

export async function getOne(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'account-settings: getOne:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const queryParams = AwsApiGatewayService.getRequestParams(event, {
            accountId: null,
        });

        // Ensure we have an accountId and permission to operate on it
        const accountId = _.get(queryParams, 'accountId');
        if (!accountId) {
            throw new HttpError('Missing required query parameter: accountId', 422);
        }
        AccountPermission.assertEventPermitsAccountId(event, accountId);

        callback(null, AwsApiGatewayService.formatPayload(await AccountSettingsService.getForAccountId(accountId)));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function replaceOne(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'account-settings: replaceOne:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const accountSettings = AwsApiGatewayService.extractRequestBody(event);
        AccountPermission.assertEventPermitsAccountId(event, accountSettings.accountId);
        callback(null, AwsApiGatewayService.formatPayload(await AccountSettingsService.save(accountSettings)));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}
