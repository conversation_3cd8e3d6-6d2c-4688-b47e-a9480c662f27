import {Context, SNSEvent} from 'aws-lambda';
import * as _ from 'lodash';
import {AccountAction, TopicMessageCatkAccountUpdates} from '../models/account';
import {AccountService} from '../services/account.service';

export async function onSnsEvent(event: SNSEvent, context: Context): Promise<void> {
    context.callbackWaitsForEmptyEventLoop = false;
    const logPrefix = 'account: onSnsEvent:';
    console.log(`${logPrefix} Entered with event: `, event);

    const eventData = JSON.parse(_.get(event, 'Records.0.Sns.Message')) as TopicMessageCatkAccountUpdates;
    const accountId = _.get(eventData, 'accountId');
    const action = _.get(eventData, 'action');
    if (_.isEmpty(accountId) || _.isEmpty(action)) {
        console.error(`${logPrefix} Missing required data`, eventData);
        return;
    }

    // Currently we're only supporting "deleted" event
    switch (action) {
        case AccountAction.deleted:
            await AccountService.deleteAllPricingDataWithAccountId(accountId);
            break;
        default:
            console.error(`${logPrefix} Unsupported action`, eventData);
            break;
    }

}
