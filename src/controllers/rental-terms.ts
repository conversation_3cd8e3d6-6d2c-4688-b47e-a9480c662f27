import {APIGatewayEvent, Callback, Context} from 'aws-lambda';
import {AwsApiGatewayService, HttpError} from '@wondersign/serverless-services';
import * as _ from 'lodash';
import {RentalTermService} from '../services/rental-term.service';
import {AccountPermission} from './helpers/account-permission';


export async function createOne(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'rental-terms: createOne:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const rentalTermService = new RentalTermService();
        const rentalTerm = AwsApiGatewayService.extractRequestBody(event);
        AccountPermission.assertEventPermitsAccountId(event, rentalTerm.accountId);
        const saveResponse = await rentalTermService.saveRentalTerm(rentalTerm);
        callback(null, AwsApiGatewayService.formatPayload(saveResponse));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function deleteOne(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'rental-terms: deleteOne:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const rentalTermId = _.get(event, 'pathParameters.rentalTermId');
        if (!rentalTermId) {
            throw new HttpError('Missing required path parameter: rentalTermId', 422);
        }
        const rentalTermService = new RentalTermService();
        const rentalTerms = await rentalTermService.queryRentalTerm({ids: [rentalTermId]});
        const rentalTerm = _.first(rentalTerms);
        if (rentalTerm) {
            AccountPermission.assertEventPermitsAccountId(event, rentalTerm.accountId);
        }
        await rentalTermService.deleteRentalTermWithId(rentalTermId);
        callback(null, AwsApiGatewayService.formatPayload(null));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function getOne(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'rental-terms: getOne:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const rentalTermId = _.get(event, 'pathParameters.rentalTermId');
        if (!rentalTermId) {
            throw new HttpError('Missing required path parameter: rentalTermId', 422);
        }
        const rentalTermService = new RentalTermService();
        const rentalTerms = await rentalTermService.queryRentalTerm({ids: [rentalTermId]});
        const rentalTerm = _.first(rentalTerms);
        if (rentalTerm) {
            AccountPermission.assertEventPermitsAccountId(event, rentalTerm.accountId);
        }
        callback(null, AwsApiGatewayService.formatPayload(rentalTerm));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function replaceOne(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'rental-terms: replaceOne:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const id = _.get(event, 'pathParameters.rentalTermId');
        if (!id) {
            throw new HttpError('Missing required path parameter: rentalTermId', 422);
        }
        const rentalTerm = AwsApiGatewayService.extractRequestBody(event);
        rentalTerm.id = id;
        AccountPermission.assertEventPermitsAccountId(event, rentalTerm.accountId);
        const rentalTermService = new RentalTermService();
        const saveResponse = await rentalTermService.saveRentalTerm(rentalTerm);
        callback(null, AwsApiGatewayService.formatPayload(saveResponse));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function search(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'rental-terms: search:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const allowedQueryKeys = [
            'accountId',
        ];
        const query = _.pick(
            AwsApiGatewayService.extractRequestBody(event),
            allowedQueryKeys,
        ) as { accountId: string};
        if (!query.accountId) {
            throw new HttpError('accountId is a required search field', 422);
        }
        const rentalTermService = new RentalTermService();
        const rentalTerms = await rentalTermService.queryRentalTerm(query);
        _.forEach(rentalTerms, (rentalTerm) => {
            AccountPermission.assertEventPermitsAccountId(event, rentalTerm.accountId);
        });
        callback(null, AwsApiGatewayService.formatPayload(rentalTerms));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function setOrder(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'rental-terms: setOrder:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const {rentalTermIds} = AwsApiGatewayService.extractRequestBody(event);
        const rentalTermService = new RentalTermService();
        const rentalTerms = await rentalTermService.queryRentalTerm({ids: rentalTermIds});
        _.forEach(rentalTerms, (rentalTerm) => {
            AccountPermission.assertEventPermitsAccountId(event, rentalTerm.accountId);
        });
        await rentalTermService.saveRentalTermIdsOrder(rentalTermIds);
        callback(null, AwsApiGatewayService.formatPayload(null));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}
