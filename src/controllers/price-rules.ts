import {APIGatewayEvent, Callback, Context} from 'aws-lambda';
import {AwsApiGatewayService, HttpError} from '@wondersign/serverless-services';
import * as _ from 'lodash';
import {PriceRuleService} from '../services/price-rule.service';
import {AccountPermission} from './helpers/account-permission';
import {User} from './helpers/user';

export async function createOne(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'price-rules: createOne:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const priceRule = AwsApiGatewayService.extractRequestBody(event);
        const priceRuleService = new PriceRuleService();
        AccountPermission.assertEventPermitsAccountId(event, priceRule.accountId);
        const userEmail = User.extractUserEmailFromEvent(event);
        const saveResult = await priceRuleService.savePriceRule(priceRule, false, userEmail);
        callback(null, AwsApiGatewayService.formatPayload(saveResult));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function deleteOne(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'price-rules: deleteOne:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const priceRuleId = _.get(event, 'pathParameters.priceRuleId');
        const userEmail = User.extractUserEmailFromEvent(event);
        if (!priceRuleId) {
            throw new HttpError('Missing required path parameter: priceRuleId', 422);
        }
        const priceRuleService = new PriceRuleService();
        const priceRules = await priceRuleService.queryPriceRule({ids: [priceRuleId]});
        const priceRule = _.first(priceRules);
        if (priceRule) {
            AccountPermission.assertEventPermitsAccountId(event, priceRule.accountId);
        }
        const deleteResult = await priceRuleService.deletePriceRuleWithId(priceRuleId, userEmail);
        callback(null, AwsApiGatewayService.formatPayload(deleteResult));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function getOne(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'price-rules: getOne:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const priceRuleId = _.get(event, 'pathParameters.priceRuleId');
        if (!priceRuleId) {
            throw new HttpError('Missing required path parameter: priceRuleId', 422);
        }
        const priceRuleService = new PriceRuleService();
        const priceRules = await priceRuleService.queryPriceRule({ids: [priceRuleId]});
        const priceRule = _.first(priceRules);
        if (priceRule) {
            AccountPermission.assertEventPermitsAccountId(event, priceRule.accountId);
        }
        callback(null, AwsApiGatewayService.formatPayload(priceRule));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function replaceOne(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'price-rules: replaceOne:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const id = _.get(event, 'pathParameters.priceRuleId');
        if (!id) {
            throw new HttpError('Missing required path parameter: priceRuleId', 422);
        }
        const priceRule = AwsApiGatewayService.extractRequestBody(event);
        priceRule.id = id;
        AccountPermission.assertEventPermitsAccountId(event, priceRule.accountId);
        const userEmail = User.extractUserEmailFromEvent(event);
        const priceRuleService = new PriceRuleService();
        const saveResult = await priceRuleService.savePriceRule(priceRule, false, userEmail);
        callback(null, AwsApiGatewayService.formatPayload(saveResult));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function updateOne(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'price-rules: updateOne:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const id = _.get(event, 'pathParameters.priceRuleId');
        if (!id) {
            throw new HttpError('Missing required path parameter: priceRuleId', 422);
        }
        const priceRule = AwsApiGatewayService.extractRequestBody(event);
        priceRule.id = id;
        let accountId = priceRule.accountId;
        const priceRuleService = new PriceRuleService();

        // If Account ID is not provided - we must fetch it from the existing object
        if (!accountId) {
            const priceRules = await priceRuleService.queryPriceRule({ids: [id]});
            accountId = _.get(_.first(priceRules), 'accountId');
        }

        if (accountId) {
            AccountPermission.assertEventPermitsAccountId(event, accountId);
        }

        const userEmail = User.extractUserEmailFromEvent(event);
        const saveResult = await priceRuleService.savePriceRule(priceRule, true, userEmail);
        callback(null, AwsApiGatewayService.formatPayload(saveResult));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function search(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'price-rules: search:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const allowedQueryKeys = [
            'ids',
            'priceListId',
        ];
        const query = _.pick(
            AwsApiGatewayService.extractRequestBody(event),
            allowedQueryKeys,
        ) as { ids?: string[]; priceListId?: string };
        if (_.isEmpty(query)) {
            throw new HttpError('One of "ids" or "priceListId" is a required search field', 422);
        }
        const priceRuleService = new PriceRuleService();
        const priceRules = await priceRuleService.queryPriceRule(query);
        _.forEach(priceRules, (priceRule) => {
            AccountPermission.assertEventPermitsAccountId(event, priceRule.accountId);
        });
        callback(null, AwsApiGatewayService.formatPayload(priceRules));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}


