import {APIGatewayEvent} from 'aws-lambda';
import * as _ from 'lodash';
import {HttpError} from '@wondersign/serverless-services';

export class AccountPermission {
    public static assertEventPermitsAccountId(event: APIGatewayEvent, accountId: string): void {
        const allowedAccountId = _.get(event, 'headers.x-ws-permitted-account-id');
        if (!allowedAccountId) {
            return;
        }

        if (allowedAccountId !== accountId) {
            throw new HttpError('Forbidden - You do not have permission for Account ID: ' + accountId, 403);
        }
    }
}
