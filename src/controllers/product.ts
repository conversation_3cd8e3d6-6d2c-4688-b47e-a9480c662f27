import {APIGatewayEvent, Callback, Context, SNSEvent, SQSEvent} from 'aws-lambda';
import {
    AwsApiGatewayService,
    AwsSqsService,
    HttpError,
    PaginationConfig,
} from '@wondersign/serverless-services';
import * as _ from 'lodash';
import {ProductPriceService} from '../services/product-price.service';
import {PriceElementChangeService} from '../services/price-element-change.service';
import {PriceListService} from '../services/price-list.service';
import {PriceRuleService} from '../services/price-rule.service';
import {PriceRule} from '../models/price-rule';
import {DateTimeService} from '../services/date-time.service';
import {FixedPriceChangeType} from '../models/fixed-price-change-type';
import {BatchJob} from '../models/batch-job';
import {TopicMessageCatkProductBrandChangeRequest} from '../models/product';
import {AccountPermission} from './helpers/account-permission';

export function calcBasePrice(event: APIGatewayEvent, context: Context, callback: Callback): void {
    const logPrefix = 'product: calcBasePrice:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const {basePriceDef, product} = AwsApiGatewayService.extractRequestBody(event);
        const productPriceService = new ProductPriceService();
        callback(null, AwsApiGatewayService.formatPayload(
            productPriceService.extractBasePriceFromProduct(product, basePriceDef),
        ));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export function calcDetail(event: APIGatewayEvent, context: Context, callback: Callback): void {
    const logPrefix = 'product: calcDetail:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const {priceFieldCalculationRule, product, rentalInstallments} = AwsApiGatewayService.extractRequestBody(event);
        const productPriceService = new ProductPriceService();

        const basePrice = productPriceService.extractBasePriceFromProduct(
            product,
            _.get(priceFieldCalculationRule, 'basePriceDef'),
            rentalInstallments,
        );

        const metadata = { isFreightPriceApplied: false };
        const calculatedPrice = productPriceService.calculateProductPriceWithFieldRule(
            product,
            priceFieldCalculationRule,
            rentalInstallments,
            metadata,
        );

        // If minIsMAP is used, set the value to false for the remaining calculation values and to see if it's enforced
        let minIsMAPEnforced = false;
        if (_.get(priceFieldCalculationRule, 'minIsMAP')) {
            priceFieldCalculationRule.minIsMAP = false;
            const calculatedPriceWithMapChange = productPriceService.calculateProductPriceWithFieldRule(
                product,
                priceFieldCalculationRule,
                rentalInstallments,
            );
            minIsMAPEnforced = (calculatedPrice !== calculatedPriceWithMapChange);
        }


        // Get the iterative prices by applying each step of the expressions
        const iterativeExpressions = [];
        const expressions = _.map(_.get(priceFieldCalculationRule, 'expressions'), (expression) => {
            iterativeExpressions.push(expression);
            return productPriceService.calculateProductPriceWithFieldRule(
                product,
                _.extend({}, priceFieldCalculationRule, {expressions: iterativeExpressions}),
                rentalInstallments,
                metadata,
            );
        });

        callback(null, AwsApiGatewayService.formatPayload({
            basePrice,
            calculatedPrice,
            expressions,
            minIsMAPEnforced,
            ...metadata,
        }));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function calcFromPriceRule(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'product: calcFromPriceRule:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const {priceRuleId} = _.get(event, 'pathParameters');
        if (!priceRuleId) {
            throw new HttpError('priceRuleId is a required parameter', 422);
        }

        const product = AwsApiGatewayService.extractRequestBody(event);
        const priceRuleService = new PriceRuleService();
        const priceRule: PriceRule = _.first(await priceRuleService.queryPriceRule({ids: [priceRuleId]}));
        if (_.isEmpty(priceRule)) {
            throw new HttpError(`priceRule with id: ${priceRuleId} could not be found`, 404);
        }

        AccountPermission.assertEventPermitsAccountId(event, priceRule.accountId);
        const productPriceService = new ProductPriceService();
        await productPriceService.initRentalTermMap(priceRule.accountId);
        await productPriceService.initAccountSettings(priceRule.accountId);
        const productPrices = productPriceService.calculateProductPricesWithPriceRule(
            priceRule.accountId,
            product,
            priceRule,
        );
        callback(null, AwsApiGatewayService.formatPayload(productPrices.prices));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function countPricedProducts(
    event: APIGatewayEvent,
    context: Context,
    callback: Callback
): Promise<void> {
    const logPrefix = 'product: countPricedProducts:';
    context.callbackWaitsForEmptyEventLoop = false;

    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const queryParams: {
            isFixed?: string;
            isMixed?: string;
            priceListId?: string;
            priceRuleId?: string;
        } = _.get(event, 'queryStringParameters', {});
        let accountId: string;

        if (queryParams.priceListId) {
            // Check if the price list exists
            const priceListId = queryParams.priceListId;
            const priceListService = new PriceListService();
            const priceLists = await priceListService.queryPriceList({ids: [priceListId]});
            const priceList = _.first(priceLists);

            if (!priceList) {
                throw new HttpError(`Unable to find the Price List by the provided priceListId: ${priceListId}`, 404);
            }

            accountId = priceList.accountId;
        } else if (queryParams.priceRuleId) {
            // Check if the price rule exists
            const priceRuleId = queryParams.priceRuleId;
            const priceRuleService = new PriceRuleService();
            const priceRules = await priceRuleService.queryPriceRule({ids: [priceRuleId]});
            const priceRule = _.first(priceRules);

            if (!priceRule) {
                throw new HttpError(`Unable to find the price rule by the provided priceRuleId: ${priceRuleId}`, 404);
            }

            accountId = priceRule.accountId;
        }

        if (!accountId) {
            throw new HttpError('An account could not be located for your supplied query', 400);
        }

        // Verify the account permission
        AccountPermission.assertEventPermitsAccountId(event, accountId);

        const countFilter: {
            isFixed?: boolean;
            isMixed?: boolean;
            priceListId?: string;
            priceRuleId?: string;
        } = _.pick(queryParams, ['priceRuleId', 'priceListId']);

        // Translate isFixed to boolean if set
        if (!_.isNil(queryParams.isFixed)) {
            countFilter.isFixed = _.toLower(queryParams.isFixed) === 'true' || _.toLower(queryParams.isFixed) === '1';
        }

        // Translate isMixed to boolean if set
        if (!_.isNil(queryParams.isMixed)) {
            countFilter.isMixed = _.toLower(queryParams.isMixed) === 'true' || _.toLower(queryParams.isMixed) === '1';
        }

        // Get count of products priced by this price rule
        const pricedProductsCount = await ProductPriceService.countProductPrices(countFilter);
        callback(null, AwsApiGatewayService.formatPayload(pricedProductsCount));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function eventBatch(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'product: eventBatch:';
    context.callbackWaitsForEmptyEventLoop = false;

    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const {accountId, batchJob, productEvents} = AwsApiGatewayService.extractRequestBody(event);

        if (!accountId || !productEvents) {
            throw new HttpError('accountId and productEvents are required properties', 422);
        }

        AccountPermission.assertEventPermitsAccountId(event, accountId);
        const productPriceService = new ProductPriceService();
        const response = await productPriceService.queueProductEventBatch(accountId, productEvents, batchJob);
        callback(null, AwsApiGatewayService.formatPayload(response));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function eventSingle(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'product: eventSingle:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const {accountId, productEvent} = AwsApiGatewayService.extractRequestBody(event);
        if (!accountId || !productEvent) {
            throw new HttpError('accountId and productEvent are required properties', 422);
        }

        AccountPermission.assertEventPermitsAccountId(event, accountId);
        const productPriceService = new ProductPriceService();
        await productPriceService.updatePricesForMutatedProducts(accountId, [productEvent]);
        callback(null, AwsApiGatewayService.formatPayload(null));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function fixedPricesDelete(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'product: fixedPricesDelete:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const {brand, priceListId, sku} = extractUriDecodedPathParameters(event, ['brand', 'priceListId', 'sku']);
        if (!brand || !priceListId || !sku) {
            throw new HttpError('priceListId, brand and sku are required fields', 422);
        }
        const productPriceService = new ProductPriceService();
        const productPrices = await productPriceService.queryProductPrices({priceListId, productRefs: [{brand, sku}]});
        if (_.get(productPrices, '0.isFixed') === true || !_.isEmpty(_.get(productPrices, '0.fixedPriceKeys'))) {
            AccountPermission.assertEventPermitsAccountId(event, productPrices[0].accountId);
            await productPriceService.deleteFixedPricesForPriceListIdAndProducts(priceListId, [{brand, sku}]);
        }
        callback(null, AwsApiGatewayService.formatPayload(null));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function fixedPricesDeleteAll(
    event: APIGatewayEvent,
    context: Context,
    callback: Callback,
): Promise<void> {
    const logPrefix = 'product: deleteAllFixedPrices:';
    context.callbackWaitsForEmptyEventLoop = false;

    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const {priceListId} = extractUriDecodedPathParameters(event, ['priceListId']);

        if (!priceListId) {
            throw new HttpError('priceListId is a required property', 422);
        }

        const priceListService = new PriceListService();
        const priceLists = await priceListService.queryPriceList({ids: [priceListId]});

        if (_.isEmpty(priceLists)) {
            throw new HttpError('Price List not found', 404);
        }

        const accountId = _.get(priceLists, '0.accountId');

        AccountPermission.assertEventPermitsAccountId(event, accountId);
        const productPriceService = new ProductPriceService();

        // Fetch existing fixed or mixed prices for this account and priceList
        const productPricesQueries = [
            productPriceService.queryProductPrices({
                isFixed: true,
                priceListId,
            }),
            productPriceService.queryProductPrices({
                isMixed: true,
                priceListId,
            }),
        ];

        const pricesFixedOrMixed = _.union(...await Promise.all(productPricesQueries));

        const pricesBatch = _.chunk(pricesFixedOrMixed, 10000);

        const batchJob: BatchJob = {
            consumerId: 'prs',
            jobId: `fixedPricesDeleteAll_${context.awsRequestId}`,
            requestTotalCount: _.size(pricesBatch),
        };

        // Queue the request to clear all fixed prices
        const responses = await Promise.all(
            _.map(pricesBatch, (prices) => productPriceService.queueProductFixedPricesBatch(
                accountId,
                priceListId,
                _.map(prices, (price) => ({
                    prices: {},
                    product: _.pick(price, ['brand', 'sku']),
                })),
                FixedPriceChangeType.reset,
                batchJob,
            )),
        );

        callback(null, AwsApiGatewayService.formatPayload(_.first(responses) || null));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function fixedPricesGet(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'product: fixedPricesGet:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const {brand, priceListId, sku} = extractUriDecodedPathParameters(event, ['brand', 'priceListId', 'sku']);
        if (!brand || !priceListId || !sku) {
            throw new HttpError('priceListId, brand and sku are required fields', 422);
        }
        const productPriceService = new ProductPriceService();
        const productPrices = await productPriceService.queryProductPrices({priceListId, productRefs: [{brand, sku}]});
        let prices = null;
        if (!_.isEmpty(productPrices)) {
            const productPrice = _.first(productPrices);
            AccountPermission.assertEventPermitsAccountId(event, productPrice.accountId);

            if (productPrice.isFixed === true) {
                prices = productPrice.prices;
            } else {
                prices = _.pick(productPrice.prices, productPrice.fixedPriceKeys);
            }
        }
        callback(null, AwsApiGatewayService.formatPayload(prices));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function fixedPricesSet(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'product: fixedPricesSet:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const {brand, priceListId, sku} = extractUriDecodedPathParameters(event, ['brand', 'priceListId', 'sku']);
        if (!brand || !priceListId || !sku) {
            throw new HttpError('priceListId, brand and sku are required fields', 422);
        }
        const prices = AwsApiGatewayService.extractRequestBody(event);

        const priceListService = new PriceListService();
        const priceLists = await priceListService.queryPriceList({ids: [priceListId]});
        const priceList = priceLists[0];
        if (_.isEmpty(priceList)) {
            throw new HttpError(`priceList with id: ${priceListId} could not be found`, 404);
        }

        const existingNotIncluded: FixedPriceChangeType = _.get(AwsApiGatewayService.getRequestParams(event, {
            existingNotIncluded: null,
        }), 'existingNotIncluded') || FixedPriceChangeType.remove;
        AccountPermission.assertEventPermitsAccountId(event, priceList.accountId);
        const productPriceService = new ProductPriceService();
        await productPriceService.updateFixedProductPrices(
            priceList.accountId,
            priceListId,
            [{
                prices,
                product: {
                    brand,
                    sku,
                },
            }],
            existingNotIncluded,
        );
        callback(null, AwsApiGatewayService.formatPayload(null));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function fixedPricesSetBatch(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'product: fixedPricesSetBatch:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const {accountId, batchJob, priceListId, productPrices} = AwsApiGatewayService.extractRequestBody(event);

        if (!accountId || !priceListId || !productPrices) {
            throw new HttpError('accountId, priceListId and productPrices are required fields', 422);
        }

        const priceListService = new PriceListService();
        const priceLists = await priceListService.queryPriceList({ids: [priceListId]});
        const priceList = priceLists[0];

        if (_.isEmpty(priceList)) {
            throw new HttpError(`priceList with id: ${priceListId} could not be found`, 404);
        }

        AccountPermission.assertEventPermitsAccountId(event, accountId);
        const existingNotIncluded: FixedPriceChangeType = _.get(AwsApiGatewayService.getRequestParams(event, {
            existingNotIncluded: null,
        }), 'existingNotIncluded') || FixedPriceChangeType.remove;
        const productPriceService = new ProductPriceService();
        const response = await productPriceService.queueProductFixedPricesBatch(
            accountId,
            priceListId,
            productPrices,
            existingNotIncluded,
            batchJob,
        );
        callback(null, AwsApiGatewayService.formatPayload(response));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function getActivePrices(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'product: getActivePrices:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const {accountId, products, timezone} = AwsApiGatewayService.extractRequestBody(event);
        if (!accountId) {
            throw new HttpError('accountId is a required field', 422);
        }
        if (_.isEmpty(products)) {
            throw new HttpError('products is a required field', 422);
        }
        AccountPermission.assertEventPermitsAccountId(event, accountId);
        const productPriceService = new ProductPriceService();
        const productPrices = await productPriceService.getActiveProductPrices(accountId, products, timezone);

        if (timezone) {
            await DateTimeService.trackTimezoneForAccountId(timezone, accountId);
        }

        callback(null, AwsApiGatewayService.formatPayload(productPrices));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function getProductPackagePrices(
    event: APIGatewayEvent,
    context: Context,
    callback: Callback
): Promise<void> {
    const logPrefix = 'product: getProductPackagePrices:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const {accountId, product, priceListId} = AwsApiGatewayService.extractRequestBody(event);
        if (!accountId) {
            throw new HttpError('accountId is a required field', 422);
        }
        if (!product) {
            throw new HttpError('product is a required field', 422);
        }
        const productPriceService = new ProductPriceService();
        const productPrices = await productPriceService.getProductPackagePrices(accountId, priceListId, product);
        callback(null, AwsApiGatewayService.formatPayload(productPrices.prices));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function getPricesPendingChanges(
    event: APIGatewayEvent,
    context: Context,
    callback: Callback,
): Promise<void> {
    const logPrefix = 'product: getPricesPendingChanges:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const accountId = _.get(event, 'pathParameters.accountId');
        if (!accountId) {
            throw new HttpError('accountId is a required path parameter', 422);
        }
        AccountPermission.assertEventPermitsAccountId(event, accountId);
        callback(null, AwsApiGatewayService.formatPayload(
            await PriceElementChangeService.getPriceElementChangesForAccountId(accountId),
        ));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function onPriceElementChange(event: SQSEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'product: onPriceElementChange:';
    context.callbackWaitsForEmptyEventLoop = false;

    class UnrecoverableError extends Error {
    }

    let accountId: string;
    let requeueTimer: NodeJS.Timeout;

    const requeueJobForAccount = async (): Promise<void> => {
        await PriceElementChangeService.releaseQueueLockForAccount(accountId);

        if (await PriceElementChangeService.hasExistingJobForAccount(accountId)) {
            await PriceElementChangeService.queueAccountId(accountId);
        }
    };

    try {
        console.log(`${logPrefix} Entered with Event: `, event);
        const awsSqsService = new AwsSqsService();
        const messages = awsSqsService.extractJobsFromEvent(event);
        console.log(`${logPrefix} Messages in Event: `, messages);
        const firstMessage = _.first(messages);
        accountId = firstMessage.accountId;

        if (_.isEmpty(accountId)) {
            throw new UnrecoverableError('accountId not found in queue message' + JSON.stringify(_.first(messages)));
        }

        // Make sure we requeue even if we happen to run out of time for a specific job
        requeueTimer = setTimeout(async () => {
            clearTimeout(requeueTimer);
            await requeueJobForAccount();
        }, context.getRemainingTimeInMillis() - 5000);

        // Set a worst case scenario max processing time
        const maxTimeForPriceElementChangeProcessingInMs = 1000 * 60;

        // Process job changes as long as we have sufficient time remaining
        while (context.getRemainingTimeInMillis() > maxTimeForPriceElementChangeProcessingInMs) {
            console.log(`${logPrefix} Requesting job for accountId: `, accountId);
            const priceElementChangeJob =
                await PriceElementChangeService.shiftPriceElementChangeForAccountId(accountId);

            if (!priceElementChangeJob) {
                console.log(`${logPrefix} Job queue is empty for accountId: `, accountId);
                break;
            }

            console.log(`${logPrefix} Processing price element change job: `, priceElementChangeJob);
            const productPriceService = new ProductPriceService();
            await productPriceService.updatePricesForPriceElementChange(priceElementChangeJob);
        }

        // If we made it to this point and the the requeue timer is still set,
        // clear it and do the same thing as the timer
        if (requeueTimer) {
            clearTimeout(requeueTimer);

            // If we weren't able to complete all of the jobs, make sure to re-queue the Account
            await requeueJobForAccount();
        }
    } catch (err) {
        console.error(`${logPrefix} Error: `, err);

        if (requeueTimer) {
            clearTimeout(requeueTimer);
        }

        // If we weren't able to complete all of the jobs, make sure to re-queue the Account
        if (accountId) {
            await requeueJobForAccount();
        }

        if (!(err instanceof UnrecoverableError)) {

            // This will cause this event to re-queue
            callback(err);
        }
    }
}

export async function onProductBrandChange(event: SNSEvent, context: Context): Promise<void> {
    context.callbackWaitsForEmptyEventLoop = false;
    const logPrefix = 'product: onproductBrandChange';
    console.log(`${logPrefix} Entered with Event:`, event);

    try {
        const eventData = JSON.parse(
            _.get(event, 'Records.0.Sns.Message')
        ) as TopicMessageCatkProductBrandChangeRequest;
        const provider = _.get(eventData, 'provider');
        const brandCurrent = _.get(eventData, 'brandCurrent');
        const brandNew = _.get(eventData, 'brandNew');
        if (_.isEmpty(provider) || _.isEmpty(brandCurrent) || _.isEmpty(brandNew)) {
            console.error(`${logPrefix} Missing required data`, eventData);
            return;
        }
        const productPriceService = new ProductPriceService();
        const priceRuleService = new PriceRuleService();
        await Promise.all([
            productPriceService.updateBrandFromProduct(brandCurrent, brandNew, logPrefix),
            priceRuleService.updateBrandFromProductSelectionCriteria(brandCurrent, brandNew, logPrefix),
        ]);
    } catch (err) {
        console.error(`${logPrefix} Error: `, err);
    }
}

export async function priceRuleMatch(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'product: priceRuleMatch:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const {priceListId, product} = AwsApiGatewayService.extractRequestBody(event);

        if (!priceListId) {
            throw new HttpError('priceListId is a required field', 422);
        }

        if (_.isEmpty(product)) {
            throw new HttpError('product is a required field', 422);
        }

        const priceListService = new PriceListService();
        const priceList = _.first(await priceListService.queryPriceList({ids: [priceListId]}));
        if (_.isEmpty(priceList)) {
            throw new HttpError(`priceList with id: ${priceListId} could not be found`, 404);
        }

        AccountPermission.assertEventPermitsAccountId(event, priceList.accountId);
        const productPriceService = new ProductPriceService();
        const matchedPriceRulesWithProducts = await productPriceService.matchPriceRulesForPriceListAndProducts(
            priceList,
            [product],
            true,
        );
        callback(null, AwsApiGatewayService.formatPayload(_.get(matchedPriceRulesWithProducts, '0.priceRule', null)));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function pricesSearch(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'product: search:';
    context.callbackWaitsForEmptyEventLoop = false;

    try {
        console.log(`${logPrefix} Entered with Event`, event);

        // Ensure queryStringParameters is an object
        event.queryStringParameters = event.queryStringParameters || {};

        const query = AwsApiGatewayService.extractRequestBody(event);
        const paginationConfig: PaginationConfig = {
            limit: {
                default: 5000,
                max: 5000,
            },
        };
        const queryParams = AwsApiGatewayService.extractPaginationInfo(event, paginationConfig);

        if (!query.accountId) {
            throw new HttpError('accountId is a required field', 422);
        }

        AccountPermission.assertEventPermitsAccountId(event, query.accountId);
        const productPriceService = new ProductPriceService();
        const productPrices = await productPriceService.queryProductPrices(query, queryParams);
        const totalProductPricesCount = productPriceService.getQueryTotalCount();

        // Set the hasNext value based on the totalCount, skip and limit properties
        paginationConfig.hasNext = (totalProductPricesCount > queryParams.skip + queryParams.limit);

        const extraHeaders = {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            'X-Total-Count': totalProductPricesCount,
        };

        const linkHeader = AwsApiGatewayService.getLinkHeaderForPagination(event, paginationConfig);
        if (linkHeader) {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            _.extend(extraHeaders, {Link: linkHeader});
        }

        callback(null, AwsApiGatewayService.formatPayload(productPrices, 200, extraHeaders));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function updatePrices(event: SQSEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'product: updatePrices:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event: `, event);
        const awsSqsService = new AwsSqsService();
        const messages = awsSqsService.extractJobsFromEvent(event);
        console.log(`${logPrefix} Messages in Event: `, messages);
        const {accountId, products} = _.first(messages);
        const productPriceService = new ProductPriceService();
        await productPriceService.updatePricesForMutatedProducts(accountId, products);
    } catch (err) {
        console.error(`${logPrefix} Error: `, err);

        // This will cause this event to re-queue
        callback(err);
    }
}

function extractUriDecodedPathParameters(event: APIGatewayEvent, paramKeys: string[]): { [id: string]: string } {
    const params = _.get(event, 'pathParameters');
    return _.mapValues(_.pick(params, paramKeys), (value) => decodeURIComponent(value));
}
