import {APIGatewayEvent, Callback, Context, ScheduledEvent} from 'aws-lambda';
import {AwsApiGatewayService, HttpError, PaginationConfig} from '@wondersign/serverless-services';
import * as _ from 'lodash';
import {PriceListService} from '../services/price-list.service';
import {PriceList} from '../models/price-list';
import {PriceRuleService} from '../services/price-rule.service';
import {PromotionalPriceListStatus} from '../models/promotional-price-list-status';
import {AccountPermission} from './helpers/account-permission';

export async function checkPromoSchedule(_event: ScheduledEvent, context: Context): Promise<void> {
    const logPrefix = 'price-lists: checkPromoSchedule:';
    context.callbackWaitsForEmptyEventLoop = false;

    try {
        await PriceListService.checkPromoPriceListSchedules();
    } catch (err) {
        console.error(`${logPrefix} Error: `, err);
    }
}

export async function cloneOne(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'price-lists: cloneOne:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const priceListService = new PriceListService();
        const priceListCloneConfig = AwsApiGatewayService.extractRequestBody(event);
        AccountPermission.assertEventPermitsAccountId(event, priceListCloneConfig.accountId);
        const saveResponse = await priceListService.clonePriceList(priceListCloneConfig);
        callback(null, AwsApiGatewayService.formatPayload(saveResponse));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function createOne(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'price-lists: createOne:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const priceListService = new PriceListService();
        const priceList = AwsApiGatewayService.extractRequestBody(event);
        AccountPermission.assertEventPermitsAccountId(event, priceList.accountId);
        const saveResponse = await priceListService.savePriceList(priceList);
        callback(null, AwsApiGatewayService.formatPayload(saveResponse));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function deleteOne(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'price-lists: deleteOne:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const priceListId = _.get(event, 'pathParameters.priceListId');
        if (!priceListId) {
            throw new HttpError('Missing required path parameter: priceListId', 422);
        }
        const priceListService = new PriceListService();
        const priceLists = await priceListService.queryPriceList({ids: [priceListId]});
        const priceList = _.first(priceLists);
        if (priceList) {
            AccountPermission.assertEventPermitsAccountId(event, priceList.accountId);
        }
        await priceListService.deletePriceListWithId(priceListId);
        callback(null, AwsApiGatewayService.formatPayload(null));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function getHistory(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'price-lists: getHistory:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const priceListId = _.get(event, 'pathParameters.priceListId');
        if (!priceListId) {
            throw new HttpError('Missing required path parameter: priceListId', 422);
        }

        const paginationConfig: PaginationConfig = {
            hasNext: false,
            limit: {
                default: 1000,
                max: 2000,
            },
        };
        const paginationInfo = AwsApiGatewayService.extractPaginationInfo(event, paginationConfig);
        const priceRuleService = new PriceRuleService();
        const priceRuleArchives = await priceRuleService.queryPriceRuleArchive({priceListId}, paginationInfo);
        const accountId = _.get(_.first(priceRuleArchives.data), 'accountId');

        if (accountId) {
            AccountPermission.assertEventPermitsAccountId(event, accountId);
        }

        // Check for pagination data
        if (priceRuleArchives.totalCount > paginationInfo.skip + paginationInfo.limit) {
            paginationConfig.hasNext = true;
        }

        const extraHeaders = {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            'X-Total-Count': priceRuleArchives.totalCount,
        };
        const linkHeader = AwsApiGatewayService.getLinkHeaderForPagination(event, paginationConfig);

        if (linkHeader) {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            _.extend(extraHeaders, {Link: linkHeader});
        }

        callback(null, AwsApiGatewayService.formatPayload(priceRuleArchives.data, 200, extraHeaders));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function getOne(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'price-lists: getOne:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const priceListId = _.get(event, 'pathParameters.priceListId');
        if (!priceListId) {
            throw new HttpError('Missing required path parameter: priceListId', 422);
        }
        const priceListService = new PriceListService();
        const priceLists = await priceListService.queryPriceList({ids: [priceListId]});
        const priceList = _.first(priceLists);
        if (priceList) {
            AccountPermission.assertEventPermitsAccountId(event, priceList.accountId);
        }
        callback(null, AwsApiGatewayService.formatPayload(priceList));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function getPromoStatus(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'price-lists: getPromoStatus:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const accountId = _.get(event, 'pathParameters.accountId');
        const {timezone} = AwsApiGatewayService.getRequestParams(event, {timezone: null});

        if (!accountId) {
            throw new HttpError('Missing required path parameter: accountId', 422);
        }

        AccountPermission.assertEventPermitsAccountId(event, accountId);
        const priceListService = new PriceListService();
        const priceLists = await priceListService.queryPriceList({accountId, type: 'promotional'});
        const priceListsWithStatus: Array<PriceList & {status: PromotionalPriceListStatus}> = _.map(
            priceLists,
            (priceList) => _.extend({
                status: PriceListService.getPromoPriceListStatus(priceList, timezone),
            }, priceList),
        );
        callback(null, AwsApiGatewayService.formatPayload(priceListsWithStatus));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function replaceOne(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'price-lists: replaceOne:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const id = _.get(event, 'pathParameters.priceListId');
        if (!id) {
            throw new HttpError('Missing required path parameter: priceListId', 422);
        }
        const priceList = AwsApiGatewayService.extractRequestBody(event);
        priceList.id = id;
        AccountPermission.assertEventPermitsAccountId(event, priceList.accountId);
        const priceListService = new PriceListService();
        const saveResponse = await priceListService.savePriceList(priceList);
        callback(null, AwsApiGatewayService.formatPayload(saveResponse));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function updateOne(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'price-lists: updateOne:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const id = _.get(event, 'pathParameters.priceListId');
        if (!id) {
            throw new HttpError('Missing required path parameter: priceListId', 422);
        }
        const priceList = AwsApiGatewayService.extractRequestBody(event);
        priceList.id = id;
        let accountId = priceList.accountId;
        const priceListService = new PriceListService();

        // If Account ID is not provided - we must fetch it from the existing object
        if (!accountId) {
            const priceLists = await priceListService.queryPriceList({ids: [id]});
            accountId = _.get(_.first(priceLists), 'accountId');
        }

        if (accountId) {
            AccountPermission.assertEventPermitsAccountId(event, accountId);
        }

        const saveResponse = await priceListService.savePriceList(priceList, true);
        callback(null, AwsApiGatewayService.formatPayload(saveResponse));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function search(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'price-lists: search:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const allowedQueryKeys = [
            'accountId',
            'type',
        ];
        const query = _.pick(
            AwsApiGatewayService.extractRequestBody(event),
            allowedQueryKeys,
        ) as { accountId: string; type?: PriceList['type'] };
        if (!query.accountId) {
            throw new HttpError('accountId is a required search field', 422);
        }
        const priceListService = new PriceListService();
        const priceLists = await priceListService.queryPriceList(query);
        _.forEach(priceLists, (priceList) => {
            AccountPermission.assertEventPermitsAccountId(event, priceList.accountId);
        });
        callback(null, AwsApiGatewayService.formatPayload(priceLists));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}

export async function setOrder(event: APIGatewayEvent, context: Context, callback: Callback): Promise<void> {
    const logPrefix = 'price-lists: setOrder:';
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        console.log(`${logPrefix} Entered with Event`, event);
        const {priceListIds} = AwsApiGatewayService.extractRequestBody(event);
        const priceListService = new PriceListService();
        const priceLists = await priceListService.queryPriceList({ids: priceListIds});

        _.forEach(priceLists, (priceList) => {
            AccountPermission.assertEventPermitsAccountId(event, priceList.accountId);
        });

        await priceListService.savePriceListIdsOrder(priceListIds);
        callback(null, AwsApiGatewayService.formatPayload(null));
    } catch (err) {
        AwsApiGatewayService.handleErrorResponse(err, callback);
    }
}
