import {Callback, Context} from 'aws-lambda';
import * as migrate from 'migrate-mongo';
import {MongoService} from '@wondersign/serverless-services';
import {PriceRule} from '../models/price-rule';

export async function run(_event: unknown, context: Context, callback: Callback): Promise<void> {
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        const {db} = await migrate.database.connect();
        const migrated = await migrate.up(db);
        callback(null, migrated.map((fileName: string) => `Migrated: ${fileName}`));
    } catch (err) {
        callback(err);
    }
}

export async function updateCategoryIds(_event: unknown, context: Context, callback: Callback): Promise<void> {
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        // Query for the list of values to migrate
        const categoryMigrationCollection = await MongoService.getCollection('_categoryMigration');
        const categoriesToMigrate = await categoryMigrationCollection.find({}).toArray();

        if (!categoriesToMigrate.length) {
            console.warn('No values found in collection: _categoryMigration. Aborting...');
            return;
        }

        // Build a migration map of sourceCategoryId => targetCategoryId
        const categoryMap = new Map<string, string>();
        for (const {sourceCategoryId, targetCategoryId} of categoriesToMigrate) {
            categoryMap.set(sourceCategoryId, targetCategoryId);
        }

        // Query for the matching sourceIds
        const priceRuleCollection = await MongoService.getCollection('priceRule');
        const priceRules = await priceRuleCollection.find<PriceRule>({
            // eslint-disable-next-line @typescript-eslint/naming-convention
            'productSelectionCriteria.conditions': {
                $elemMatch: {
                    field: 'categoryId',
                    value: {
                        $in: Array.from(categoryMap.keys()),
                    },
                },
            },
        }, {
            projection: {
                productSelectionCriteria: 1,
            },
        }).toArray();

        // Loop through the matches and build bulk writes for the targetIds
        const priceRuleBulkWrites = [];
        for (const priceRule of priceRules) {
            for (const {conditions} of priceRule.productSelectionCriteria) {
                for (const {field, value} of conditions) {
                    if (field !== 'categoryId') {
                        continue;
                    }

                    // Iterate the list in reverse order so we can remove elements as we go
                    for (let index = (value as string[]).length - 1; index >= 0; index--) {
                        const categoryId = value[index];
                        const targetCategoryId = categoryMap.get(categoryId);

                        if (targetCategoryId) {
                            if (!(value as string[]).includes(targetCategoryId)) {
                                value[index] = targetCategoryId;
                            } else {
                                (value as string[]).splice(index, 1);
                            }
                        }
                    }
                }
            }

            priceRuleBulkWrites.push({
                updateOne: {
                    filter: {
                        _id: priceRule._id,
                    },
                    update: {
                        $set: {
                            productSelectionCriteria: priceRule.productSelectionCriteria,
                        },
                    },
                },
            });
        }

        // Apply the bulk write
        if (priceRuleBulkWrites.length) {
            const results = await priceRuleCollection.bulkWrite(priceRuleBulkWrites, {
                ordered: false,
            });
            console.log('Completed updates with results: ', results);
        } else {
            console.warn('No bulk writes provided so no changes made.');
        }
    } catch (err) {
        console.error('Error migrating categoryIds: ', err);
        callback(err);
    }
}
