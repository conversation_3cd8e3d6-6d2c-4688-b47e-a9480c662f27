#!/usr/bin/env node
const fs = require('fs');
const yaml = require('js-yaml');

try {
    const envConfig = yaml.load(fs.readFileSync(__dirname + '/../env.yml', 'utf8'));

    // Manage the config
    const config = {
        mongodb: {
            url: envConfig.dbConnectionUri,
            databaseName: envConfig.dbName,
            options: {
                useNewUrlParser: true, // removes a deprecation warning when connecting
                //   connectTimeoutMS: 3600000, // increase connection timeout to 1 hour
                //   socketTimeoutMS: 3600000, // increase socket timeout to 1 hour
            },
        },

        // The migrations dir, can be an relative or absolute path. Only edit this when really necessary.
        migrationsDir: 'migrations',

        // The mongodb collection where the applied changes are stored. Only edit this when really necessary.
        changelogCollectionName: '_dbMigrations',
    };

    // Get a Javascript string version of the config
    const configJs = `
        const config = ${JSON.stringify(config, null, 2)};
        module.exports = config;
    `;

    // Write the Javascript config file
    fs.writeFileSync(__dirname + '/../migrate-mongo-config.js', configJs, {encoding: 'utf8'});

} catch (e) {
    console.error(e);
}
