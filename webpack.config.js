const path = require('path');
const CopyPlugin = require('copy-webpack-plugin');
const slsw = require('serverless-webpack');
const nodeExternals = require('webpack-node-externals');
const SourceMapDevToolPlugin = require("webpack").SourceMapDevToolPlugin;

module.exports = {
    entry: slsw.lib.entries,
    target: 'node',
    mode: 'production',
    module: {
        rules: [
            { test: /\.ts(x?)$/, use: 'ts-loader' },
            { test: /\.json$/,  type: 'json'},
        ],
    },
    resolve: {
        extensions: ['.ts', '.js', '.tsx', '.jsx'],
    },
    plugins: [
        new CopyPlugin({
            patterns: [
                { from: 'migrations/**', to: ''},
                { from: 'migrate-mongo-config.js', to: ''},
            ]
        }),
        new SourceMapDevToolPlugin({
            filename: '[name].js.map',
        }),
    ],
    externals: [nodeExternals()], // exclude external modules
    output: {
        libraryTarget: 'commonjs',
        path: path.join(__dirname, '.webpack'),
        filename: '[name].js',
    },
};
