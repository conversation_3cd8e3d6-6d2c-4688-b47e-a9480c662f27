image: node:20.14

definitions:
  caches:
    sonar: ~/.sonar/cache # Caching SonarCloud artifacts will speed up your build
  services:
    docker:
      memory: 3072
  steps:
    - step: &install-config-test-deploy
        name: Install, Config, Test and Deploy
        caches:
          - node
        script:
          - "mkdir -p ~/.aws"
          - "echo \"[serverless]\" > ~/.aws/credentials"
          - "echo \"aws_access_key_id=$AWS_ACCESS_KEY_ID\" >> ~/.aws/credentials"
          - "echo \"aws_secret_access_key=$AWS_SECRET_ACCESS_KEY\" >> ~/.aws/credentials"
          - "echo \"//registry.npmjs.org/:_authToken=$NPM_AUTH_TOKEN\" > ~/.npmrc"
          - "npm install"
          - "echo \"awsAccountId: \\\"$AWS_ACCOUNT_ID\\\"\" >> env.yml"
          - "echo \"datadogEnableDDTracing: $DD_TRACING_ENABLED\" >> env.yml"
          - "echo \"dbConnectionUri: \\\"$DB_CONNECTION_URI\\\"\" >> env.yml"
          - "echo \"dbName: \\\"$DB_NAME\\\"\" >> env.yml"
          - "echo \"memorySize: $MEMORY_SIZE\" >> env.yml"
          - "echo \"pssApiKey: \\\"$PSS_API_KEY\\\"\" >> env.yml"
          - "echo \"pssUrl: \\\"$PSS_URL\\\"\" >> env.yml"
          - "echo \"stage: $AWS_STAGE\" >> env.yml"
          - "npm run lint"
          - "npm run test:coverage"
          - "./build-scripts/build-migration-config.js"
          - "npx sls deploy"
          - "npx sls invoke -f MigrationRun"
        artifacts: # files accessible on BB Pipeline. Expire after 14 days
          - coverage/lcov.info
    - step: &build-test-sonarcloud
        name: Build, test and analyze on SonarCloud
        clone:
          depth: full    # SonarCloud scanner needs the full history to assign issues properly
        caches:
          - node
          - sonar
        script:
          - pipe: sonarsource/sonarcloud-scan:2.0.0
            variables:
              SONAR_TOKEN: ${SONAR_TOKEN}
              EXTRA_ARGS: '-Dsonar.sources=src -Dsonar.tests=src -Dsonar.test.inclusions="**/*.spec.ts" -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info'
    - step: &check-quality-gate-sonarcloud
        clone:
          enabled: false
        name: Check the Quality Gate on SonarCloud
        script:
          - pipe: sonarsource/sonarcloud-quality-gate:0.1.6

pipelines:
  custom:
    validation:
      - step:
          name: Validation
          deployment: develop
          caches:
            - node
          script:
            - "mkdir -p ~/.aws"
            - "echo \"[serverless]\" > ~/.aws/credentials"
            - "echo \"aws_access_key_id=$AWS_ACCESS_KEY_ID\" >> ~/.aws/credentials"
            - "echo \"aws_secret_access_key=$AWS_SECRET_ACCESS_KEY\" >> ~/.aws/credentials"
            - "echo \"//registry.npmjs.org/:_authToken=$NPM_AUTH_TOKEN\" > ~/.npmrc"
            - "npm install"
            - "cp env.example.yml env.yml"
            - "npm run lint"
            - "npm run test:coverage"
            - "./build-scripts/build-migration-config.js"
            - "npx sls webpack"
          artifacts: # files accessible on BB Pipeline. Expire after 14 days
            - coverage/lcov.info
      - step: *build-test-sonarcloud
      - step: *check-quality-gate-sonarcloud

  branches:
    develop:
      - step:
          <<: *install-config-test-deploy
          deployment: develop

    staging:
      - step:
          <<: *install-config-test-deploy
          deployment: staging

  tags:
    '*':
      - step:
          <<: *install-config-test-deploy
          deployment: production
