const config = {
    accountProductPriceChangeTopic: 'account-product-price-change',
    awsAccessKeyId: '',
    awsAccountId: 'awsAccountId1',
    awsRegion: 'us-east-1',
    awsSecretAccessKey: '',
    batchJobCollection: 'batchJob',
    dbConnectionUri: '',
    dbName: 'product-prices',
    namespace: 'local-sls-prs',
    priceElementChangeJobQueueName: 'price-element-change',
    priceElementChangeJobTopic: 'price-element-change',
    promoPriceListActionTopic: 'promo-price-list-action',
    pssApiKey: 'pssApiKey1',
    pssUrl: 'https://pps',
    updatePricesForProductsQueueName: 'update-price-for-products',
};

process.env = config;
export default config;
