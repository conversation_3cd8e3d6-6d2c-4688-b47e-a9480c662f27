import * as _ from 'lodash';
import {
    createAwsS3ServiceSpy,
    createAwsSnsServiceSpy,
    createMongoServiceSpy,
    getCollection,
    registerCollection,
} from '@wondersign/serverless-services';
import {ObjectId} from 'mongodb';
import {ProductPriceService} from '../../src/services/product-price.service';
import {createPriceListServiceSpy} from '../mocks/price-list.service.mock';
import {createPriceRuleServiceSpy} from '../mocks/price-rule.service.mock';
import {createProductSelectionServiceSpy} from '../mocks/product-selection.service.mock';
import {ProductRef} from '../../src/models/product-ref';
import {getPriceList} from '../mocks/price-list.mock';
import {
    getExpressionFreightPrice,
    getExpressionTypeMath,
    getExpressionTypeRound,
    getFieldCalculation,
    getPriceRule,
} from '../mocks/price-rule.mock';
import {Product} from '../../src/models/product';
import {ProductPrice} from '../../src/models/product-price';
import {createPriceElementChangeServiceSpy} from '../mocks/price-element-change.service.mock';
import {PriceElementChangeJob} from '../../src/models/price-element-change-job';
import {PriceFieldCalculationRule, PriceRule} from '../../src/models/price-rule';
import {createRentalTermServiceSpy} from '../mocks/rental-term.service.mock';
import {getProductPrice} from '../mocks/product-price.mock';
import {getRentalTerm} from '../mocks/rental-term.mock';
import {createAccountSettingsServiceSpy} from '../mocks/account-settings.service.mock';
import {createProductPackageServiceSpy} from '../mocks/product-package.service.mock';
import {AccountSettings, PackageCalculationMethod} from '../../src/models/account-settings';
import {ProductPackageData} from '../../src/models/product-package-data';
import {ObjectHelperService} from '../../src/services/object-helper.service';
import {createBatchJobServiceSpy} from '../mocks/batch-job.service.mock';
import {BatchJob} from '../../src/models/batch-job';
import {Components} from '../../src/models/contracts';
import {MongoHelperService} from '../../src/services/mongo-helper.service';
import {FixedPriceChangeType} from '../../src/models/fixed-price-change-type';
import {PriceList} from '../../src/models/price-list';

describe('ProductPriceService', () => {

    let accountId: string;
    let accountSettingsService;
    let awsS3Service;
    let awsSnsService;
    let batchJobService;
    let mongoService;
    let priceElementChangeService;
    let priceListService;
    let priceRuleService;
    let productPackageService;
    let productPriceCollection;
    const productPriceCollectionName = 'productPrice';
    let productPriceService: ProductPriceService;
    let productSelectionService;
    let rentalTermService;
    beforeEach(async () => {
        accountId = 'accountId1';
        awsS3Service = createAwsS3ServiceSpy();
        awsSnsService = createAwsSnsServiceSpy();
        accountSettingsService = createAccountSettingsServiceSpy();
        batchJobService = createBatchJobServiceSpy();
        mongoService = createMongoServiceSpy();
        priceElementChangeService = createPriceElementChangeServiceSpy();
        priceListService = createPriceListServiceSpy();
        priceRuleService = createPriceRuleServiceSpy();
        productPackageService = createProductPackageServiceSpy();
        productSelectionService = createProductSelectionServiceSpy();
        rentalTermService = createRentalTermServiceSpy();

        // Register the collection and ensure it's empty for each run
        await registerCollection(productPriceCollectionName);
        productPriceCollection = getCollection(productPriceCollectionName);
        await new Promise((resolve) => productPriceCollection.deleteMany({}, resolve));

        mongoService.getCollection.and.callFake(
            (collectionName) => Promise.resolve(getCollection(collectionName)),
        );

        const accountSettings: AccountSettings = {
            accountId,
        };
        accountSettingsService.getForAccountId.and.returnValue(Promise.resolve(accountSettings));

        productPriceService = new ProductPriceService();
    });

    it('should exist', () => {
        expect(productPriceService).toBeTruthy();
    });

    describe('calculateProductPricesWithPriceRule', () => {
        beforeEach(async () => {
            await productPriceService.initAccountSettings(accountId);
        });

        it('should perform complex math operation', () => {
            const brand = 'brand1';
            const sku1 = 'sku1';
            const priceListId = 'priceListId1';
            const priceRuleId = 'priceRuleId1';

            const priceRule = getPriceRule({
                accountId,
                calculation: {
                    retail: getFieldCalculation({
                        expressions: [

                            // +10.01
                            getExpressionTypeMath({
                                operation: 'add',
                                value: 10.01,
                            }),

                            // -5.98
                            getExpressionTypeMath({
                                operation: 'subtract',
                                value: 5.98,
                            }),

                            // x2.5
                            getExpressionTypeMath({
                                operation: 'multiply',
                                value: 2.5,
                            }),

                            // / 6.3
                            getExpressionTypeMath({
                                operation: 'divide',
                                value: 6.3,
                            }),

                            // Gross Margin: 20%
                            getExpressionTypeMath({
                                operation: 'margin',
                                value: 20,
                            }),

                            // Markup: 15
                            getExpressionTypeMath({
                                operation: 'markup',
                                value: 15,
                            }),
                        ],
                    }),
                },
                id: priceRuleId,
                priceListId,
            });

            let expectedRetailPrice = 100; // Base Price
            expectedRetailPrice += 10.01;
            expectedRetailPrice -= 5.98;
            expectedRetailPrice *= 2.5;
            expectedRetailPrice /= 6.3;
            expectedRetailPrice *= (100 / (100 - 20)); // Margin
            expectedRetailPrice *= (1 + (15 / 100)); // Markup
            expectedRetailPrice = _.round(expectedRetailPrice, 2); // Rounded to 2 decimal places

            const product: Product = {
                basePrice: 100,
                brand,
                sku: sku1,
            };

            const expectedProductPrices: ProductPrice = {
                accountId,
                brand,
                isFixed: false,
                priceListId,
                priceRuleId,
                prices: {
                    rental: {},
                    retail: expectedRetailPrice,
                },
                sku: sku1,
            };

            expect(productPriceService.calculateProductPricesWithPriceRule(accountId, product, priceRule))
                .toEqual(expectedProductPrices);
        });

        it('should calculate accurate rental term', async () => {
            const brand = 'brand1';
            const sku1 = 'sku1';
            const priceListId = 'priceListId1';
            const priceRuleId = 'priceRuleId1';
            const rentalTermId = (new ObjectId()).toString();

            const mathMultiplier = 2.5;
            const priceRule = getPriceRule({
                accountId,
                calculation: {
                    rental: {
                        [rentalTermId]: getFieldCalculation({
                            expressions: [

                                // x2.5
                                getExpressionTypeMath({
                                    operation: 'multiply',
                                    value: mathMultiplier,
                                }),
                            ],
                        }),
                    },
                },
                id: priceRuleId,
                priceListId,
            });

            const rentalInstallments = 12;
            rentalTermService.queryRentalTerm.and.returnValue(Promise.resolve([
                getRentalTerm({
                    accountId,
                    id: rentalTermId,
                    installments: rentalInstallments,
                }),
            ]));

            let expectedRentalPrice = 100; // Base Price
            expectedRentalPrice /= rentalInstallments; // Divide by number of installments
            expectedRentalPrice *= mathMultiplier;
            expectedRentalPrice = _.round(expectedRentalPrice, 2); // Rounded to 2 decimal places

            const product: Product = {
                basePrice: 100,
                brand,
                sku: sku1,
            };

            const expectedProductPrices: ProductPrice = {
                accountId,
                brand,
                isFixed: false,
                priceListId,
                priceRuleId,
                prices: {
                    rental: {
                        [rentalTermId]: expectedRentalPrice,
                    },
                },
                sku: sku1,
            };

            await productPriceService.initRentalTermMap(accountId);
            expect(productPriceService.calculateProductPricesWithPriceRule(accountId, product, priceRule))
                .toEqual(expectedProductPrices);
        });

        it('should throw an exception if rental term map is not initialized', () => {
            const brand = 'brand1';
            const sku1 = 'sku1';
            const priceListId = 'priceListId1';
            const priceRuleId = 'priceRuleId1';
            const rentalTermId = (new ObjectId()).toString();

            const priceRule = getPriceRule({
                accountId,
                calculation: {
                    rental: {
                        [rentalTermId]: getFieldCalculation({}),
                    },
                },
                id: priceRuleId,
                priceListId,
            });

            const product: Product = {
                basePrice: 100,
                brand,
                sku: sku1,
            };

            try {
                productPriceService.calculateProductPricesWithPriceRule(accountId, product, priceRule);
                fail('No exception was thrown for not initializing rental term map');
            } catch (err) {
                expect(err.message).toEqual('Rental Term Map must be initialized before calculating prices');
            }
        });

        it('should retain fixed prices', () => {
            const brand = 'brand1';
            const sku1 = 'sku1';
            const priceListId = 'priceListId1';
            const priceRuleId = 'priceRuleId1';

            const priceRule = getPriceRule({
                accountId,
                calculation: {
                    list: getFieldCalculation({
                        expressions: [
                            // x2.5
                            getExpressionTypeMath({
                                operation: 'multiply',
                                value: 2.5,
                            }),
                        ],
                    }),
                    retail: getFieldCalculation({
                        expressions: [
                            // x2.5
                            getExpressionTypeMath({
                                operation: 'multiply',
                                value: 2.5,
                            }),
                        ],
                    }),
                },
                id: priceRuleId,
                priceListId,
            });

            const basePrice = 100;
            let expectedRetailPrice = basePrice; // Base Price
            expectedRetailPrice *= 2.5;

            const listPrice = 555.55;

            const product: Product = {
                basePrice,
                brand,
                sku: sku1,
            };

            const existingProductPrices: ProductPrice = {
                accountId,
                brand,
                fixedPriceKeys: ['list'],
                isFixed: false,
                priceListId,
                priceRuleId,
                prices: {
                    list: listPrice,
                    rental: {},
                    retail: 222.22,
                },
                sku: sku1,
            };

            const expectedProductPrices: ProductPrice = {
                accountId,
                brand,
                fixedPriceKeys: ['list'],
                isFixed: false,
                priceListId,
                priceRuleId,
                prices: {
                    list: listPrice,
                    rental: {},
                    retail: expectedRetailPrice,
                },
                sku: sku1,
            };

            expect(productPriceService.calculateProductPricesWithPriceRule(
                accountId,
                product,
                priceRule,
                existingProductPrices.fixedPriceKeys,
                existingProductPrices.prices,
            ))
                .toEqual(expectedProductPrices);
        });

        it('should convert to fixed prices when all prices are fixed', () => {
            const brand = 'brand1';
            const sku1 = 'sku1';
            const priceListId = 'priceListId1';
            const priceRuleId = 'priceRuleId1';

            const priceRule = getPriceRule({
                accountId,
                calculation: {
                    list: getFieldCalculation({
                        expressions: [
                            // x2.5
                            getExpressionTypeMath({
                                operation: 'multiply',
                                value: 2.5,
                            }),
                        ],
                    }),
                    retail: getFieldCalculation({
                        expressions: [
                            // x2.5
                            getExpressionTypeMath({
                                operation: 'multiply',
                                value: 2.5,
                            }),
                        ],
                    }),
                },
                id: priceRuleId,
                priceListId,
            });

            const basePrice = 100;
            const listPrice = 555.55;
            const retailPrice = 333.33;

            const product: Product = {
                basePrice,
                brand,
                sku: sku1,
            };

            const existingProductPrices: ProductPrice = {
                accountId,
                brand,
                fixedPriceKeys: ['list', 'retail'],
                isFixed: false,
                priceListId,
                priceRuleId,
                prices: {
                    list: listPrice,
                    rental: {
                        abc123: 444.44,
                    },
                    retail: retailPrice,
                },
                sku: sku1,
            };

            const expectedProductPrices: ProductPrice = {
                accountId,
                brand,
                isFixed: true,
                priceListId,
                prices: {
                    list: listPrice,
                    rental: {},
                    retail: retailPrice,
                },
                sku: sku1,
            };

            expect(productPriceService.calculateProductPricesWithPriceRule(
                accountId,
                product,
                priceRule,
                existingProductPrices.fixedPriceKeys,
                existingProductPrices.prices,
            ))
                .toEqual(expectedProductPrices);
        });

        it('should apply isFreightPriceApplied if price rule has freight expression', () => {
            const brand = 'brand1';
            const sku1 = 'sku1';
            const priceListId = 'priceListId1';
            const priceRuleId = 'priceRuleId1';

            const priceRule = getPriceRule({
                accountId,
                calculation: {
                    retail: getFieldCalculation({
                        expressions: [
                            // Direct To Consumer
                            getExpressionFreightPrice({
                                expressionType: 'freight',
                                operation: 'Direct To Consumer',
                            }),
                        ],
                    }),
                },
                id: priceRuleId,
                priceListId,
            });

            const freightDirectToConsumerRate = 20.55;

            const basePrice = 100;
            let expectedRetailPrice = basePrice; // Base Price
            expectedRetailPrice += freightDirectToConsumerRate;

            const product: Product = {
                basePrice,
                brand,
                rates: [{
                    rate: freightDirectToConsumerRate,
                    service: 'Direct To Consumer',
                }],
                sku: sku1,
            };

            const existingProductPrices: ProductPrice = {
                accountId,
                brand,
                fixedPriceKeys: ['list'],
                isFixed: false,
                priceListId,
                priceRuleId,
                prices: {
                    rental: {},
                    retail: basePrice,
                },
                sku: sku1,
            };

            const expectedProductPrices: ProductPrice = {
                accountId,
                brand,
                fixedPriceKeys: ['list'],
                isFixed: false,
                isFreightPriceApplied: true,
                priceListId,
                priceRuleId,
                prices: {
                    rental: {},
                    retail: expectedRetailPrice,
                },
                sku: sku1,
            };

            expect(productPriceService.calculateProductPricesWithPriceRule(
                accountId,
                product,
                priceRule,
                existingProductPrices.fixedPriceKeys,
                existingProductPrices.prices,
            ))
                .toEqual(expectedProductPrices);
        });

        describe('rounding', () => {

            let brand;
            let priceListId;
            let priceRule: PriceRule;
            let priceRuleId;
            let sku1;
            beforeEach(() => {
                brand = 'brand1';
                sku1 = 'sku1';

                priceListId = 'priceListId1';
                priceRuleId = 'priceRuleId1';

                priceRule = getPriceRule({
                    accountId,
                    calculation: {
                        retail: getFieldCalculation(),
                    },
                    id: priceRuleId,
                    priceListId,
                });
            });

            it('should perform round up with cents only', () => {
                const expectedRetailPrice = 100.99;
                const product: Product = {
                    basePrice: 100.49,
                    brand,
                    sku: sku1,
                };
                priceRule.calculation.retail.expressions = [
                    getExpressionTypeRound({
                        decimalValue: '99',
                        direction: 'up',
                    }),
                ];

                const expectedProductPrices: ProductPrice = {
                    accountId,
                    brand,
                    isFixed: false,
                    priceListId,
                    priceRuleId,
                    prices: {
                        rental: {},
                        retail: expectedRetailPrice,
                    },
                    sku: sku1,
                };
                expect(productPriceService.calculateProductPricesWithPriceRule(accountId, product, priceRule))
                    .toEqual(expectedProductPrices);
            });

            it('should perform round down with cents only', () => {
                const expectedRetailPrice = 99.99;
                const product: Product = {
                    basePrice: 100.49,
                    brand,
                    sku: sku1,
                };
                priceRule.calculation.retail.expressions = [
                    getExpressionTypeRound({
                        decimalValue: '99',
                        direction: 'down',
                    }),
                ];

                const expectedProductPrices: ProductPrice = {
                    accountId,
                    brand,
                    isFixed: false,
                    priceListId,
                    priceRuleId,
                    prices: {
                        rental: {},
                        retail: expectedRetailPrice,
                    },
                    sku: sku1,
                };
                expect(productPriceService.calculateProductPricesWithPriceRule(accountId, product, priceRule))
                    .toEqual(expectedProductPrices);
            });

            it('should perform round up with integer and cents', () => {
                const expectedRetailPrice = 109.99;
                const product: Product = {
                    basePrice: 100.49,
                    brand,
                    sku: sku1,
                };
                priceRule.calculation.retail.expressions = [
                    getExpressionTypeRound({
                        decimalValue: '99',
                        direction: 'up',
                        integerValue: 9,
                    }),
                ];

                const expectedProductPrices: ProductPrice = {
                    accountId,
                    brand,
                    isFixed: false,
                    priceListId,
                    priceRuleId,
                    prices: {
                        rental: {},
                        retail: expectedRetailPrice,
                    },
                    sku: sku1,
                };
                expect(productPriceService.calculateProductPricesWithPriceRule(accountId, product, priceRule))
                    .toEqual(expectedProductPrices);
            });

            it('should perform round up with just cents', () => {
                const expectedRetailPrice = 100.99;
                const product: Product = {
                    basePrice: 100.49,
                    brand,
                    sku: sku1,
                };
                priceRule.calculation.retail.expressions = [
                    getExpressionTypeRound({
                        decimalValue: '99',
                        direction: 'up',
                    }),
                ];

                const expectedProductPrices: ProductPrice = {
                    accountId,
                    brand,
                    isFixed: false,
                    priceListId,
                    priceRuleId,
                    prices: {
                        rental: {},
                        retail: expectedRetailPrice,
                    },
                    sku: sku1,
                };
                expect(productPriceService.calculateProductPricesWithPriceRule(accountId, product, priceRule))
                    .toEqual(expectedProductPrices);
            });

            it('should perform round up with just integer', () => {
                const expectedRetailPrice = 109.49;
                const product: Product = {
                    basePrice: 100.49,
                    brand,
                    sku: sku1,
                };
                priceRule.calculation.retail.expressions = [
                    getExpressionTypeRound({
                        direction: 'up',
                        integerValue: 9,
                    }),
                ];

                const expectedProductPrices: ProductPrice = {
                    accountId,
                    brand,
                    isFixed: false,
                    priceListId,
                    priceRuleId,
                    prices: {
                        rental: {},
                        retail: expectedRetailPrice,
                    },
                    sku: sku1,
                };
                expect(productPriceService.calculateProductPricesWithPriceRule(accountId, product, priceRule))
                    .toEqual(expectedProductPrices);
            });

            it('should perform round up to the next dollar if required to match cents', () => {
                const expectedRetailPrice = 101.49;
                const product: Product = {
                    basePrice: 100.99,
                    brand,
                    sku: sku1,
                };
                priceRule.calculation.retail.expressions = [
                    getExpressionTypeRound({
                        decimalValue: '49',
                        direction: 'up',
                    }),
                ];

                const expectedProductPrices: ProductPrice = {
                    accountId,
                    brand,
                    isFixed: false,
                    priceListId,
                    priceRuleId,
                    prices: {
                        rental: {},
                        retail: expectedRetailPrice,
                    },
                    sku: sku1,
                };
                expect(productPriceService.calculateProductPricesWithPriceRule(accountId, product, priceRule))
                    .toEqual(expectedProductPrices);
            });

            it('should perform round down with integer and cents', () => {
                const expectedRetailPrice = 98.99;
                const product: Product = {
                    basePrice: 100.49,
                    brand,
                    sku: sku1,
                };
                priceRule.calculation.retail.expressions = [
                    getExpressionTypeRound({
                        decimalValue: '99',
                        direction: 'down',
                        integerValue: 8,
                    }),
                ];

                const expectedProductPrices: ProductPrice = {
                    accountId,
                    brand,
                    isFixed: false,
                    priceListId,
                    priceRuleId,
                    prices: {
                        rental: {},
                        retail: expectedRetailPrice,
                    },
                    sku: sku1,
                };
                expect(productPriceService.calculateProductPricesWithPriceRule(accountId, product, priceRule))
                    .toEqual(expectedProductPrices);
            });

            it('should perform round down with just cents', () => {
                const expectedRetailPrice = 99.99;
                const product: Product = {
                    basePrice: 100.49,
                    brand,
                    sku: sku1,
                };
                priceRule.calculation.retail.expressions = [
                    getExpressionTypeRound({
                        decimalValue: '99',
                        direction: 'down',
                    }),
                ];

                const expectedProductPrices: ProductPrice = {
                    accountId,
                    brand,
                    isFixed: false,
                    priceListId,
                    priceRuleId,
                    prices: {
                        rental: {},
                        retail: expectedRetailPrice,
                    },
                    sku: sku1,
                };
                expect(productPriceService.calculateProductPricesWithPriceRule(accountId, product, priceRule))
                    .toEqual(expectedProductPrices);
            });

            it('should perform round down with just integer', () => {
                const expectedRetailPrice = 99.49;
                const product: Product = {
                    basePrice: 100.49,
                    brand,
                    sku: sku1,
                };
                priceRule.calculation.retail.expressions = [
                    getExpressionTypeRound({
                        direction: 'down',
                        integerValue: 9,
                    }),
                ];

                const expectedProductPrices: ProductPrice = {
                    accountId,
                    brand,
                    isFixed: false,
                    priceListId,
                    priceRuleId,
                    prices: {
                        rental: {},
                        retail: expectedRetailPrice,
                    },
                    sku: sku1,
                };
                expect(productPriceService.calculateProductPricesWithPriceRule(accountId, product, priceRule))
                    .toEqual(expectedProductPrices);
            });

            it('should always not round if rounding down would result in a negative value', () => {
                const expectedRetailPrice = 8.98;
                const product: Product = {
                    basePrice: 8.98,
                    brand,
                    sku: sku1,
                };
                priceRule.calculation.retail.expressions = [
                    getExpressionTypeRound({
                        decimalValue: '99',
                        direction: 'down',
                        integerValue: 9,
                    }),
                ];

                const expectedProductPrices: ProductPrice = {
                    accountId,
                    brand,
                    isFixed: false,
                    priceListId,
                    priceRuleId,
                    prices: {
                        rental: {},
                        retail: expectedRetailPrice,
                    },
                    sku: sku1,
                };
                expect(productPriceService.calculateProductPricesWithPriceRule(accountId, product, priceRule))
                    .toEqual(expectedProductPrices);
            });

            it('should respect max adjustment by finite', () => {
                const product: Product = {
                    basePrice: 0,
                    brand,
                    sku: sku1,
                };
                priceRule.calculation.retail.expressions = [
                    getExpressionTypeRound({
                        decimalValue: '99',
                        direction: 'down',
                        integerValue: 9,
                        maxAdjustment: {
                            enabled: true,
                            unit: 'finite',
                            value: 5,
                        },
                    }),
                ];

                const expectedProductPrices: ProductPrice = {
                    accountId,
                    brand,
                    isFixed: false,
                    priceListId,
                    priceRuleId,
                    prices: {
                        rental: {},
                        retail: 0,
                    },
                    sku: sku1,
                };

                // Should round since the round amount is within the maxAdjustment value
                product.basePrice = 23.50;
                expectedProductPrices.prices.retail = 19.99;
                expect(productPriceService.calculateProductPricesWithPriceRule(accountId, product, priceRule))
                    .toEqual(expectedProductPrices);

                // Should not round since the round amount is beyond the maxAdjustment value
                product.basePrice = 27.50;
                expectedProductPrices.prices.retail = 27.50;
                expect(productPriceService.calculateProductPricesWithPriceRule(accountId, product, priceRule))
                    .toEqual(expectedProductPrices);
            });

            it('should respect max adjustment by percent', () => {
                const product: Product = {
                    basePrice: 0,
                    brand,
                    sku: sku1,
                };
                priceRule.calculation.retail.expressions = [
                    getExpressionTypeRound({
                        decimalValue: '99',
                        direction: 'down',
                        integerValue: 9,
                        maxAdjustment: {
                            enabled: true,
                            unit: 'percent',
                            value: 20,
                        },
                    }),
                ];

                const expectedProductPrices: ProductPrice = {
                    accountId,
                    brand,
                    isFixed: false,
                    priceListId,
                    priceRuleId,
                    prices: {
                        rental: {},
                        retail: 0,
                    },
                    sku: sku1,
                };

                // Should round since the round amount is within the maxAdjustment value
                product.basePrice = 23.50;
                expectedProductPrices.prices.retail = 19.99;
                expect(productPriceService.calculateProductPricesWithPriceRule(accountId, product, priceRule))
                    .toEqual(expectedProductPrices);

                // Should not round since the round amount is beyond the maxAdjustment value
                product.basePrice = 27.50;
                expectedProductPrices.prices.retail = 27.50;
                expect(productPriceService.calculateProductPricesWithPriceRule(accountId, product, priceRule))
                    .toEqual(expectedProductPrices);
            });

            it('should should not round a $0 price', () => {
                const expectedRetailPrice = 0.00;
                const product: Product = {
                    basePrice: 0,
                    brand,
                    sku: sku1,
                };
                priceRule.calculation.retail.expressions = [
                    getExpressionTypeRound({
                        decimalValue: '99',
                        direction: 'up',
                        integerValue: 9,
                    }),
                ];

                const expectedProductPrices: ProductPrice = {
                    accountId,
                    brand,
                    isFixed: false,
                    priceListId,
                    priceRuleId,
                    prices: {
                        rental: {},
                        retail: expectedRetailPrice,
                    },
                    sku: sku1,
                };
                expect(productPriceService.calculateProductPricesWithPriceRule(accountId, product, priceRule))
                    .toEqual(expectedProductPrices);
            });

        });
    });

    describe('calculateProductPriceWithFieldRule', () => {
        it('should prevent a negative result', () => {
            const product: Product = {
                basePrice: 100,
                brand: 'brand1',
                sku: 'sku1',
            };
            const calculation: PriceFieldCalculationRule = {
                basePriceDef: 'cost',
                expressions: [
                    {
                        expressionType: 'math',
                        operation: 'subtract',
                        value: 101,
                    },
                ],
            };
            expect(productPriceService.calculateProductPriceWithFieldRule(product, calculation)).toBe(0);
        });

        it('should apply freight calculation if service exists into rates array', () => {
            const product: Product = {
                basePrice: 100,
                brand: 'brand1',
                rates: [{ rate: 12.34, service: 'Direct To Consumer' }],
                sku: 'sku1',
            };

            const calculation: PriceFieldCalculationRule = {
                basePriceDef: 'cost',
                expressions: [
                    {
                        expressionType: 'freight',
                        operation: 'Direct To Consumer',
                    },
                ],
            };
            expect(productPriceService.calculateProductPriceWithFieldRule(product, calculation)).toBe(112.34);
        });

        it('should set flag isFreightPriceApplied to true if freight price is applied', () => {
            const product: Product = {
                basePrice: 100,
                brand: 'brand1',
                rates: [{ rate: 12.34, service: 'Direct To Consumer' }],
                sku: 'sku1',
            };

            const calculation: PriceFieldCalculationRule = {
                basePriceDef: 'cost',
                expressions: [
                    {
                        expressionType: 'freight',
                        operation: 'Direct To Consumer',
                    },
                ],
            };
            const metadata = { isFreightPriceApplied: false };
            productPriceService.calculateProductPriceWithFieldRule(product, calculation, 0, metadata);
            expect(metadata.isFreightPriceApplied).toBe(true);
        });

        it('should NOT apply freight calculation if service NOT exists into rates array', () => {
            const product: Product = {
                basePrice: 100,
                brand: 'brand1',
                rates: [{ rate: 12.34, service: 'Fulfillment by Giga' }],
                sku: 'sku1',
            };

            const calculation: PriceFieldCalculationRule = {
                basePriceDef: 'cost',
                expressions: [
                    {
                        expressionType: 'freight',
                        operation: 'Direct To Consumer',
                    },
                ],
            };
            expect(productPriceService.calculateProductPriceWithFieldRule(product, calculation)).toBe(100);
        });

        it('should NOT apply freight calculation if rates array is empty', () => {
            const product: Product = {
                basePrice: 100,
                brand: 'brand1',
                rates: [],
                sku: 'sku1',
            };

            const calculation: PriceFieldCalculationRule = {
                basePriceDef: 'cost',
                expressions: [
                    {
                        expressionType: 'freight',
                        operation: 'Direct To Consumer',
                    },
                ],
            };
            expect(productPriceService.calculateProductPriceWithFieldRule(product, calculation)).toBe(100);
        });

        it('should prevent a negative result if rate is negative and bigger than basePrice', () => {
            const product: Product = {
                basePrice: 100,
                brand: 'brand1',
                rates: [{ rate: -120.34, service: 'Direct To Consumer' }],
                sku: 'sku1',
            };

            const calculation: PriceFieldCalculationRule = {
                basePriceDef: 'cost',
                expressions: [
                    {
                        expressionType: 'freight',
                        operation: 'Direct To Consumer',
                    },
                ],
            };
            expect(productPriceService.calculateProductPriceWithFieldRule(product, calculation)).toBe(0);
        });

        it('should prvent freight calculation is basePriceDef is MSRP', () => {
            const product: Product = {
                basePrice: 100,
                brand: 'brand1',
                msrpPrice: 123,
                rates: [{ rate: -120.34, service: 'Direct To Consumer' }],
                sku: 'sku1',
            };

            const calculation: PriceFieldCalculationRule = {
                basePriceDef: 'msrp',
                expressions: [
                    {
                        expressionType: 'freight',
                        operation: 'Direct To Consumer',
                    },
                ],
            };
            expect(productPriceService.calculateProductPriceWithFieldRule(product, calculation)).toBe(123);
        });

        it('should prevent freight calculation is minIsMap is true', () => {
            const product: Product = {
                basePrice: 100,
                brand: 'brand1',
                rates: [{ rate: 12.34, service: 'Direct To Consumer' }],
                sku: 'sku1',
            };

            const calculation: PriceFieldCalculationRule = {
                basePriceDef: 'cost',
                expressions: [
                    {
                        expressionType: 'freight',
                        operation: 'Direct To Consumer',
                    },
                ],
                minIsMAP: true,
            };
            expect(productPriceService.calculateProductPriceWithFieldRule(product, calculation)).toBe(100);
        });
    });

    describe('countProductPrices', () => {
        it('should return count of product prices by price rule id', async () => {
            const priceRuleId = 'priceRuleId1';
            const countDocuments = spyOn(productPriceCollection, 'countDocuments');
            await ProductPriceService.countProductPrices({priceRuleId});
            expect(countDocuments).toHaveBeenCalledWith({priceRuleId}, MongoHelperService.getDefaultReadPreference());
        });

        it('should return count of fixed product prices by price list id', async () => {
            const priceListId = 'priceListId1';
            const countDocuments = spyOn(productPriceCollection, 'countDocuments');
            await ProductPriceService.countProductPrices({isFixed: true, priceListId});
            expect(countDocuments).toHaveBeenCalledWith(
                {isFixed: true, priceListId},
                MongoHelperService.getDefaultReadPreference(),
            );
        });

        it('should throw an exception if not querying by a key', async () => {
            try {
                await ProductPriceService.countProductPrices({isFixed: true});
                fail('No exception was thrown');
            } catch (err) {
                expect(err.message)
                    .toEqual('Getting count of product prices requires at least priceListId or priceRuleId');
            }

        });
    });

    describe('deleteFixedPricesForPriceListIdAndProducts', () => {
        let deleteManySpy;

        beforeEach(() => {
            deleteManySpy = spyOn(productPriceCollection, 'deleteMany');
        });

        it('should send an appropriate query to MongoDB for price list id and product ref', async () => {
            const brand1 = 'brand1';
            const sku1 = 'sku1';
            const priceListId1 = 'priceListId1';

            expect(await productPriceService.deleteFixedPricesForPriceListIdAndProducts(
                priceListId1,
                [{brand: brand1, sku: sku1}],
            ))
                .toBeUndefined();

            expect(deleteManySpy).toHaveBeenCalledWith({
                $or: [
                    {
                        isFixed: true,
                    },
                    {
                        fixedPriceKeys: {
                            $exists: true,
                            $ne: [],
                        },
                    },
                ],
                brand: {
                    $in: [brand1],
                },
                priceListId: priceListId1,
                sku: {
                    $in: [sku1],
                },
            }, MongoHelperService.getDefaultWriteConcern());
        });

        it('should update prices to calculated prices after removing fixed prices', async () => {
            const brand1 = 'brand1';
            const sku1 = 'sku1';
            const priceListId1 = 'priceListId1';
            const priceRuleId1 = 'priceRuleId1';
            const productPrice1 = getProductPrice({
                brand: brand1,
                isFixed: true,
                priceListId: priceListId1,
                sku: sku1,
            });
            await productPriceCollection.insertOne(productPrice1);

            expect(_.size(await productPriceCollection.find({}).toArray())).toEqual(1);

            priceListService.queryPriceList.and.returnValue(Promise.resolve([
                getPriceList({
                    accountId,
                    id: priceListId1,
                    priceRuleIds: [
                        priceRuleId1,
                    ],
                }),
            ]));

            const multiplyValue = 2;
            priceRuleService.queryPriceRule.and.returnValue(Promise.resolve([
                getPriceRule({
                    accountId,
                    calculation: {
                        retail: {
                            basePriceDef: 'cost',
                            expressions: [
                                getExpressionTypeMath({
                                    operation: 'multiply',
                                    value: multiplyValue,
                                }),
                            ],
                        },
                    },
                    enabled: true,
                    id: priceRuleId1,
                    priceListId: priceListId1,
                }),
            ]));

            productSelectionService.match.and.returnValue(Promise.resolve([
                {
                    brand: brand1,
                    selectionId: priceRuleId1,
                    sku: sku1,
                },
            ]));

            const basePrice = 5.00;
            productSelectionService.fetch.and.returnValue(Promise.resolve([
                {
                    basePrice,
                    brand: brand1,
                    sku: sku1,
                },
            ]));

            spyOn(productPriceService, 'queryProductPrices').and.returnValue(Promise.resolve([]));

            const saveProductPricesSpy = spyOn(productPriceService, 'saveProductPrices');

            expect(await productPriceService.deleteFixedPricesForPriceListIdAndProducts(
                priceListId1,
                [{brand: brand1, sku: sku1}]),
            )
                .toBeUndefined();
            expect(saveProductPricesSpy).toHaveBeenCalledWith([
                getProductPrice({
                    accountId,
                    brand: brand1,
                    isFixed: false,
                    priceListId: priceListId1,
                    priceRuleId: priceRuleId1,
                    prices: {
                        rental: {},
                        retail: _.round(basePrice * multiplyValue, 2),
                    },
                    sku: sku1,
                }),
            ]);
        });

    });

    describe('deletePricesForDeletedProducts', () => {
        it('should execute a bulk write with deleteMany operations', async () => {
            const brand = 'brand1';
            const sku1 = 'sku1';
            const sku2 = 'sku2';
            const productRefs: ProductRef[] = [
                {
                    brand,
                    sku: sku1,
                },
                {
                    brand,
                    sku: sku2,
                },
            ];

            const productPriceCollectionBulkWriteSpy = spyOn(productPriceCollection, 'bulkWrite');
            productPriceCollectionBulkWriteSpy.and.returnValue(Promise.resolve());

            await productPriceService.deletePricesForDeletedProducts(accountId, productRefs);
            expect(productPriceCollectionBulkWriteSpy).toHaveBeenCalledWith([
                {
                    deleteMany: {
                        filter: {
                            accountId,
                            brand,
                            sku: sku1,
                        },
                    },
                },
                {
                    deleteMany: {
                        filter: {
                            accountId,
                            brand,
                            sku: sku2,
                        },
                    },
                },
            ], MongoHelperService.getDefaultWriteConcern());
        });
    });

    describe('deleteProductPricesWithAccountId', () => {
        it('should delete product prices from the collection', async () => {
            const deleteManySpy = spyOn(productPriceCollection, 'deleteMany');
            await ProductPriceService.deleteProductPricesWithAccountId(accountId);
            expect(deleteManySpy).toHaveBeenCalledWith({accountId});
        });
    });

    describe('deleteProductPricesWithPriceListId', () => {
        it('should delete product prices with the priceListId', async () => {
            const brand = 'brand1';
            const priceListId = 'priceListId1';
            const productPrices: ProductPrice[] = [
                {
                    accountId,
                    brand,
                    isFixed: false,
                    priceListId,
                    prices: {},
                    sku: 'sku1',
                },
                {
                    accountId,
                    brand,
                    isFixed: false,
                    priceListId,
                    prices: {},
                    sku: 'sku2',
                },
                {
                    accountId,
                    brand,
                    isFixed: false,
                    priceListId: 'priceListId2',
                    prices: {},
                    sku: 'sku1',
                },
            ];

            await productPriceCollection.insertMany(productPrices);

            await ProductPriceService.deleteProductPricesWithPriceListId(priceListId);

            const foundProductPrices = await productPriceCollection.find({}).toArray();

            expect(foundProductPrices.length).toEqual(1);
            expect(foundProductPrices[0].priceListId).not.toEqual(priceListId);
        });
    });

    describe('extractBasePriceFromProduct', () => {

        let basePrice: number;
        let basePriceDef: PriceFieldCalculationRule['basePriceDef'];
        let brand: string;
        let expectedBasePrice: number;
        let msrp: number;
        let product: Product;
        let sku1: string;
        beforeEach(() => {
            brand = 'brand1';
            sku1 = 'sku1';
            basePrice = 100;
            product = {
                basePrice,
                brand,
                sku: sku1,
            };
        });

        it('should use cost', () => {
            basePriceDef = 'cost';
            expectedBasePrice = basePrice;
            expect(productPriceService.extractBasePriceFromProduct(product, basePriceDef)).toEqual(expectedBasePrice);

            // With no cost (basePrice)
            expectedBasePrice = null;
            delete product.basePrice;
            expect(productPriceService.extractBasePriceFromProduct(product, basePriceDef)).toEqual(expectedBasePrice);
        });

        it('should use msrp', () => {

            // msrp - when no MSRP is provided in the product, no value should be set
            basePriceDef = 'msrp';
            expectedBasePrice = null;
            expect(productPriceService.extractBasePriceFromProduct(product, basePriceDef)).toEqual(expectedBasePrice);

            // msrp - when MSRP is provided in the product, it should be used
            msrp = 200;
            product.msrpPrice = msrp;
            expectedBasePrice = msrp;
            expect(productPriceService.extractBasePriceFromProduct(product, basePriceDef)).toEqual(expectedBasePrice);
        });

        it('should prefer msrp', () => {

            // msrpPreferred - when no MSRP is provided in the product, basePrice (cost) should be used
            basePriceDef = 'msrpPreferred';
            delete product.msrpPrice;
            expectedBasePrice = basePrice;
            expect(productPriceService.extractBasePriceFromProduct(product, basePriceDef)).toEqual(expectedBasePrice);


            // msrpPreferred - when MSRP is provided in the product, it should be used
            msrp = 200;
            product.msrpPrice = msrp;
            expectedBasePrice = msrp;
            expect(productPriceService.extractBasePriceFromProduct(product, basePriceDef)).toEqual(expectedBasePrice);

        });

        it('should use a max of msrp', () => {

            // costMaxMsrp - when no MSRP is provided in the product, basePrice (cost) should be used
            basePriceDef = 'costMaxMsrp';
            delete product.msrpPrice;
            expectedBasePrice = basePrice;
            expect(productPriceService.extractBasePriceFromProduct(product, basePriceDef)).toEqual(expectedBasePrice);


            // costMaxMsrp - when MSRP is provided in the product AND it's less than cost, it should be used
            msrp = 90;
            product.msrpPrice = msrp;
            expectedBasePrice = msrp;
            expect(productPriceService.extractBasePriceFromProduct(product, basePriceDef)).toEqual(expectedBasePrice);


            // costMaxMsrp - when MSRP is provided in the product AND it's greater than cost, cost should be used
            msrp = 120;
            product.msrpPrice = msrp;
            expectedBasePrice = basePrice;
            expect(productPriceService.extractBasePriceFromProduct(product, basePriceDef)).toEqual(expectedBasePrice);
        });
    });

    describe('getActiveProductPrices', () => {
        const brand = 'brand1';
        const sku1 = 'sku1';
        const sku2 = 'sku2';
        const sku3 = 'sku3';
        let productRefs: ProductRef[];

        it('should return active product prices for supplied products', async () => {
            productRefs = [
                {
                    brand,
                    sku: sku1,
                },
                {
                    brand,
                    sku: sku2,
                },
            ];

            const priceListId = (new ObjectId()).toString();
            const priceRuleId = (new ObjectId()).toString();
            const priceList: PriceList = {
                _id: new ObjectId(priceListId),
                priceRuleIds: [
                    priceRuleId,
                ],
                type: 'default',
            };

            const accountSettings: AccountSettings = {
                accountId,
            };

            const queriedProductPrices: ProductPrice[] = [
                {
                    accountId,
                    brand,
                    isFixed: false,
                    priceListId,
                    priceRuleId,
                    prices: {
                        retail: 5.55,
                    },
                    sku: sku1,
                },
            ];

            priceRuleService.queryPriceRule.and.returnValue(Promise.resolve([]));
            priceListService.queryPriceList.and.returnValue(Promise.resolve([priceList]));
            accountSettingsService.getForAccountId.and.returnValue(Promise.resolve(accountSettings));
            const queryProductPricesSpy = spyOn(productPriceService, 'queryProductPrices');
            queryProductPricesSpy.and.returnValue(Promise.resolve(queriedProductPrices));

            const actualActiveProductPrices = await productPriceService.getActiveProductPrices(accountId, productRefs);
            expect(actualActiveProductPrices).toEqual(queriedProductPrices);
        });

        it('should update summed package prices when promo price list is in effect', async () => {
            productRefs = [
                {
                    brand,
                    sku: sku1,
                },
                {
                    brand,
                    sku: sku2,
                },
            ];

            const priceListIdDefault = (new ObjectId()).toString();
            const priceListIdPromo = (new ObjectId()).toString();
            const priceRuleId = (new ObjectId()).toString();
            const priceListPromo: PriceList = {
                id: priceListIdPromo,
                priceRuleIds: [
                    priceRuleId,
                ],
                type: 'promotional',
            };

            const priceListDefault: PriceList = {
                id: priceListIdDefault,
                priceRuleIds: [
                    priceRuleId,
                ],
                type: 'default',
            };

            const accountSettings: AccountSettings = {
                accountId,
                packagedProductPriceCalculationMethod: PackageCalculationMethod.mergedPrice,
            };

            const defaultProductPrices: ProductPrice[] = [
                {
                    accountId,
                    brand,
                    isFixed: false,
                    priceListId: priceListIdDefault,
                    priceRuleId,
                    prices: {
                        retail: 5.55,
                    },
                    sku: sku1,
                },
                {
                    accountId,
                    brand,
                    isFixed: false,
                    priceListId: priceListIdDefault,
                    priceRuleId,
                    prices: {
                        retail: 2.22,
                    },
                    sku: sku2,
                },
                {
                    accountId,
                    brand,
                    isFixed: false,
                    priceListId: priceListIdDefault,
                    priceRuleId,
                    prices: {
                        retail: 3.33,
                    },
                    sku: sku3,
                },
            ];

            const promoProductPrices: ProductPrice[] = [
                {
                    accountId,
                    brand,
                    isFixed: false,
                    priceListId: priceListIdDefault,
                    priceRuleId,
                    prices: {
                        retail: 1.11,
                    },
                    sku: sku3,
                },
            ];

            const productPackageData: ProductPackageData[] = [
                {
                    brand,
                    components: [
                        {
                            brand,
                            quantity: 1,
                            sku: sku2,
                        },
                        {
                            brand,
                            quantity: 1,
                            sku: sku3,
                        },
                    ],
                    isPackage: true,
                    sku: sku1,
                },
                {
                    brand,
                    isComponent: true,
                    packages: [
                        {
                            brand,
                            quantity: 1,
                            sku: sku1,
                        },
                    ],
                    sku: sku2,
                },
                {
                    brand,
                    isComponent: true,
                    packages: [
                        {
                            brand,
                            quantity: 1,
                            sku: sku1,
                        },
                    ],
                    sku: sku3,
                },
            ];

            const expectedProductPrices: ProductPrice[] = [
                {
                    accountId,
                    brand,
                    isFixed: false,
                    isPromoPrice: true,
                    priceListId: priceListIdDefault,
                    prices: {
                        rental: {},
                        retail: 3.33,
                    },
                    sku: sku1,
                },
                {
                    accountId,
                    brand,
                    isFixed: false,
                    priceListId: priceListIdDefault,
                    priceRuleId,
                    prices: {
                        retail: 2.22,
                    },
                    sku: sku2,
                },
            ];

            priceRuleService.queryPriceRule.and.returnValue(Promise.resolve([]));
            priceListService.isActivePromotionalPriceList.and.returnValue(true);
            priceListService.queryPriceList.and.returnValue(Promise.resolve([priceListPromo, priceListDefault]));
            productSelectionService.fetchPackageData.and.returnValue(Promise.resolve(productPackageData));
            productPackageService.extractPackageProducts.and.returnValue(
                _.filter(productPackageData, ['isPackage', true])
            );
            productPackageService.getUniqueComponentRefsFromPackageProducts.and.returnValue(
                _.map(
                    _.filter(productPackageData, ['isComponent', true]),
                    (data) => _.pick(data, ['brand', 'sku']),
                ),
            );
            accountSettingsService.getForAccountId.and.returnValue(Promise.resolve(accountSettings));
            const queryProductPricesSpy = spyOn(productPriceService, 'queryProductPrices');
            queryProductPricesSpy.and.callFake(({priceListId}) => {
                let productPrices: ProductPrice[];

                switch (priceListId) {
                    case priceListIdDefault:
                        productPrices = defaultProductPrices;
                        break;

                    case priceListIdPromo:
                        productPrices = promoProductPrices;
                        break;
                }

                return Promise.resolve(productPrices);
            });

            const timezone = 'UTC-5';
            const actualActiveProductPrices = await productPriceService.getActiveProductPrices(
                accountId,
                productRefs,
                timezone,
            );
            expect(actualActiveProductPrices).toEqual(expectedProductPrices);
        });

        it('should return isFreightPriceApplied flag if product price includes "freight" expression', async () => {
            productRefs = [
                {
                    brand,
                    sku: sku1,
                },
                {
                    brand,
                    sku: sku2,
                },
            ];

            const priceListId = (new ObjectId()).toString();
            const priceRuleId = (new ObjectId()).toString();
            const priceList: PriceList = {
                _id: new ObjectId(priceListId),
                priceRuleIds: [
                    priceRuleId,
                ],
                type: 'default',
            };

            const accountSettings: AccountSettings = {
                accountId,
            };

            priceRuleService.queryPriceRule.and.returnValue(Promise.resolve([getPriceRule({
                accountId,
                calculation: {
                    retail: {
                        basePriceDef: 'cost',
                        expressions: [
                            getExpressionFreightPrice(),
                        ],
                    },
                },
                enabled: true,
                id: priceRuleId,
                priceListId,
            })]));

            const queriedProductPrices: ProductPrice[] = [
                {
                    accountId,
                    brand,
                    isFixed: false,
                    isFreightPriceApplied: true,
                    priceListId,
                    priceRuleId,
                    prices: {
                        retail: 5.55,
                    },
                    sku: sku1,
                },
            ];

            priceListService.queryPriceList.and.returnValue(Promise.resolve([priceList]));
            accountSettingsService.getForAccountId.and.returnValue(Promise.resolve(accountSettings));

            const queryProductPricesSpy = spyOn(productPriceService, 'queryProductPrices');
            queryProductPricesSpy.and.returnValue(Promise.resolve(queriedProductPrices));

            const actualActiveProductPrices = await productPriceService.getActiveProductPrices(accountId, productRefs);
            expect(actualActiveProductPrices).toEqual(queriedProductPrices);
        });
    });

    describe('getProductPackagePrices', () => {
        const brand = 'brand1';
        const sku1 = 'sku1';
        const sku2 = 'sku2';
        const sku3 = 'sku3';

        it('should return the calculated prices for a package product', async () => {
            const productRef: ProductRef = {
                brand,
                sku: sku1,
            };
            const priceRuleId = (new ObjectId()).toString();
            const priceListId = (new ObjectId()).toString();

            const priceList: PriceList = {
                id: priceListId,
                priceRuleIds: [
                    priceRuleId,
                ],
                type: 'default',
            };

            const accountSettings: AccountSettings = {
                accountId,
                packagedProductPriceCalculationMethod: PackageCalculationMethod.mergedPrice,
            };

            const defaultProductPrices: ProductPrice[] = [
                {
                    accountId,
                    brand,
                    isFixed: false,
                    priceListId,
                    priceRuleId,
                    prices: {
                        list: 6.66,
                        retail: 5.55,
                    },
                    sku: sku1,
                },
                {
                    accountId,
                    brand,
                    isFixed: false,
                    priceListId,
                    priceRuleId,
                    prices: {
                        list: 2.34,
                        retail: 2.22,
                    },
                    sku: sku2,
                },
                {
                    accountId,
                    brand,
                    isFixed: false,
                    priceListId,
                    priceRuleId,
                    prices: {
                        list: 1.23,
                        retail: 3.33,
                    },
                    sku: sku3,
                },
            ];

            const productPackageData: ProductPackageData[] = [
                {
                    brand,
                    components: [
                        {
                            brand,
                            quantity: 1,
                            sku: sku2,
                        },
                        {
                            brand,
                            quantity: 1,
                            sku: sku3,
                        },
                    ],
                    isPackage: true,
                    sku: sku1,
                },
            ];

            const expectedProductPrices: ProductPrice = {
                accountId,
                brand,
                isFixed: false,
                priceListId,
                prices: {
                    list: 3.57,
                    rental: {},
                    retail: 5.55,
                },
                sku: sku1,
            };

            accountSettingsService.getForAccountId.and.returnValue(Promise.resolve(accountSettings));
            priceListService.queryPriceList.and.returnValue(Promise.resolve([priceList]));
            productSelectionService.fetchPackageData.and.returnValue(Promise.resolve(productPackageData));
            productPackageService.extractPackageProducts.and.returnValue(
                _.filter(productPackageData, ['isPackage', true])
            );
            productPackageService.getUniqueComponentRefsFromPackageProducts.and.returnValue(
                _.map(
                    _.filter(productPackageData, ['isComponent', true]),
                    (data) => _.pick(data, ['brand', 'sku']),
                ),
            );
            const queryProductPricesSpy = spyOn(productPriceService, 'queryProductPrices');
            // eslint-disable-next-line @typescript-eslint/no-shadow
            queryProductPricesSpy.and.callFake(({priceListId}) => {
                let productPrices: ProductPrice[];

                switch (priceListId) {
                    case priceListId:
                        productPrices = defaultProductPrices;
                        break;
                }

                return Promise.resolve(productPrices);
            });

            const actualActiveProductPrices = await productPriceService.getProductPackagePrices(
                accountId,
                priceListId,
                productRef,
            );
            expect(actualActiveProductPrices).toEqual(expectedProductPrices);
        });

        it('should return undefined if account calculation method is not mergedPrice', async () => {
            const productRef: ProductRef = {
                brand,
                sku: sku1,
            };
            const priceListId = (new ObjectId()).toString();

            const accountSettings: AccountSettings = {
                accountId,
                packagedProductPriceCalculationMethod: PackageCalculationMethod.mergedCost,
            };

            accountSettings.packagedProductPriceCalculationMethod = PackageCalculationMethod.mergedCost;

            accountSettingsService.getForAccountId.and.returnValue(Promise.resolve(accountSettings));

            const actualActiveProductPrices = await productPriceService.getProductPackagePrices(
                accountId,
                priceListId,
                productRef,
            );
            expect(actualActiveProductPrices).toEqual(undefined);
        });

        it('should return undefined if priceLists results is empty', async () => {
            const productRef: ProductRef = {
                brand,
                sku: sku1,
            };
            const priceListId = (new ObjectId()).toString();

            const accountSettings: AccountSettings = {
                accountId,
                packagedProductPriceCalculationMethod: PackageCalculationMethod.mergedPrice,
            };

            accountSettingsService.getForAccountId.and.returnValue(Promise.resolve(accountSettings));
            priceListService.queryPriceList.and.returnValue(Promise.resolve([]));

            const actualActiveProductPrices = await productPriceService.getProductPackagePrices(
                accountId,
                priceListId,
                productRef,
            );
            expect(actualActiveProductPrices).toEqual(undefined);
        });

        it('should return undefined if package data is empty', async () => {
            const productRef: ProductRef = {
                brand,
                sku: sku1,
            };
            const priceListId = (new ObjectId()).toString();
            const priceRuleId = (new ObjectId()).toString();

            const accountSettings: AccountSettings = {
                accountId,
                packagedProductPriceCalculationMethod: PackageCalculationMethod.mergedPrice,
            };

            const priceList: PriceList = {
                id: priceListId,
                priceRuleIds: [
                    priceRuleId,
                ],
                type: 'default',
            };


            accountSettingsService.getForAccountId.and.returnValue(Promise.resolve(accountSettings));
            priceListService.queryPriceList.and.returnValue(Promise.resolve([priceList]));
            productSelectionService.fetchPackageData.and.returnValue(Promise.resolve([]));

            const actualActiveProductPrices = await productPriceService.getProductPackagePrices(
                accountId,
                priceListId,
                productRef,
            );
            expect(actualActiveProductPrices).toEqual(undefined);
        });
    });

    describe('getQueryTotalCount', () => {

        it('should return the total result count even if a smaller limit is applied', async () => {
            await productPriceCollection.insertMany([
                getProductPrice({accountId}),
                getProductPrice({accountId}),
            ]);

            await productPriceService.queryProductPrices({accountId}, {limit: 1});
            expect(productPriceService.getQueryTotalCount()).toEqual(2);
        });

        it('should return the total result count if no limit is applied', async () => {
            await productPriceCollection.insertMany([
                getProductPrice({accountId}),
                getProductPrice({accountId}),
            ]);

            await productPriceService.queryProductPrices({accountId});
            expect(productPriceService.getQueryTotalCount()).toEqual(2);
        });

    });

    describe('initAccountSettings', () => {
        it('should only call accountSettingsService once for the given accountId', async () => {
            const accountSettings: AccountSettings = {
                accountId,
            };

            accountSettingsService.getForAccountId.and.returnValue(Promise.resolve(accountSettings));

            await productPriceService.initAccountSettings(accountId);
            await productPriceService.initAccountSettings(accountId);

            expect(accountSettingsService.getForAccountId).toHaveBeenCalledWith(accountId);
            expect(accountSettingsService.getForAccountId).toHaveBeenCalledTimes(1);
        });
    });

    describe('initRentalTermMap', () => {

        it('should query rental terms for the provided account', async () => {
            expect(await productPriceService.initRentalTermMap(accountId)).toBeUndefined();
            expect(rentalTermService.queryRentalTerm).toHaveBeenCalledWith({accountId});
        });

        it('should query rental terms only once', async () => {
            await productPriceService.initRentalTermMap(accountId);
            expect(rentalTermService.queryRentalTerm).toHaveBeenCalled();
            rentalTermService.queryRentalTerm.calls.reset();
            await productPriceService.initRentalTermMap(accountId);
            expect(rentalTermService.queryRentalTerm).not.toHaveBeenCalled();
        });

    });

    describe('matchPriceRulesForPriceListAndProducts', () => {

        let productRefs: Array<ProductRef | Product>;
        beforeEach(() => {
            productRefs = [];
        });

        it('should properly order price rules for matching', async () => {
            const brand = 'brand1';
            const sku1 = 'sku1';
            const sku2 = 'sku2';
            const sku3 = 'sku3';
            productRefs = [
                {
                    brand,
                    sku: sku1,
                },
                {
                    brand,
                    sku: sku2,
                },
                {
                    brand,
                    sku: sku3,
                },
            ];

            const priceRuleId1 = 'priceRuleId1';
            const priceRuleId2 = 'priceRuleId2';
            const priceList = getPriceList({
                accountId,
                priceRuleIds: [priceRuleId1, priceRuleId2],
            });

            const priceRule1 = getPriceRule({
                accountId,
                id: priceRuleId1,
            });
            const priceRule2 = getPriceRule({
                accountId,
                id: priceRuleId2,
            });

            priceRuleService.queryPriceRule.and.returnValue(Promise.resolve([priceRule2, priceRule1]));

            await productPriceService.matchPriceRulesForPriceListAndProducts(priceList, productRefs);
            expect(productSelectionService.match).toHaveBeenCalledWith(
                accountId,
                jasmine.any(Object),
                [{
                    id: priceRuleId1,
                    isSelectAll: false,
                    selectionGroups: [],
                    selectionGroupsOperator: null,
                }, {
                    id: priceRuleId2,
                    isSelectAll: false,
                    selectionGroups: [],
                    selectionGroupsOperator: null,
                }],
                false,
            );
        });

        it('should include entire product in match and response', async () => {
            const brand = 'brand1';
            const sku1 = 'sku1';
            const sku2 = 'sku2';
            const sku3 = 'sku3';
            productRefs = [
                {
                    brand,
                    mapPrice: 50.00,
                    sku: sku1,
                },
                {
                    brand,
                    mapPrice: 70.00,
                    sku: sku2,
                },
                {
                    brand,
                    mapPrice: 90.00,
                    sku: sku3,
                },
            ];

            const priceRuleId1 = 'priceRuleId1';
            const priceRuleId2 = 'priceRuleId2';
            const priceList = getPriceList({
                accountId,
                priceRuleIds: [priceRuleId1, priceRuleId2],
            });

            const priceRule1 = getPriceRule({
                accountId,
                id: priceRuleId1,
            });
            const priceRule2 = getPriceRule({
                accountId,
                id: priceRuleId2,
            });

            priceRuleService.queryPriceRule.and.returnValue(Promise.resolve([priceRule2, priceRule1]));
            productSelectionService.match.and.returnValue(Promise.resolve([
                {
                    brand,
                    selectionId: priceRuleId1,
                    sku: sku1,
                },
                {
                    brand,
                    selectionId: priceRuleId2,
                    sku: sku2,
                },
                {
                    brand,
                    selectionId: priceRuleId1,
                    sku: sku3,
                },
            ]));

            expect(await productPriceService.matchPriceRulesForPriceListAndProducts(priceList, productRefs, true))
                .toEqual([
                    {
                        priceRule: priceRule1,
                        product: productRefs[0],
                    },
                    {
                        priceRule: priceRule2,
                        product: productRefs[1],
                    },
                    {
                        priceRule: priceRule1,
                        product: productRefs[2],
                    },
                ]);
            expect(productSelectionService.match).toHaveBeenCalledWith(
                accountId,
                productRefs,
                [{
                    id: priceRuleId1,
                    isSelectAll: false,
                    selectionGroups: [],
                    selectionGroupsOperator: null,
                }, {
                    id: priceRuleId2,
                    isSelectAll: false,
                    selectionGroups: [],
                    selectionGroupsOperator: null,
                }],
                true,
            );
        });

    });

    describe('queryProductPrices', () => {

        it('should find product prices by priceListId', async () => {
            const priceListId = 'priceListId1';
            const productPrice1 = getProductPrice({
                accountId,
                priceListId,
            });
            const productPrice2 = getProductPrice({
                accountId,
                priceListId: 'priceListId2',
            });
            await productPriceCollection.insertMany([
                productPrice1,
                productPrice2,
            ]);

            const productPrices = await productPriceService.queryProductPrices({priceListId});
            expect(_.omit(productPrices[0], ['_id'])).toEqual(productPrice1);
            expect(_.size(productPrices)).toEqual(1);
        });

        it('should respect the supplied limit', async () => {
            const priceListId = 'priceListId1';
            const productPrice1 = getProductPrice({
                accountId,
                priceListId,
                sku: 'sku1',
            });
            const productPrice2 = getProductPrice({
                accountId,
                priceListId,
                sku: 'sku2',
            });
            await productPriceCollection.insertMany([
                productPrice1,
                productPrice2,
            ]);

            const productPrices = await productPriceService.queryProductPrices({priceListId}, {limit: 1});
            expect(_.omit(productPrices[0], ['_id'])).toEqual(productPrice1);
            expect(_.size(productPrices)).toEqual(1);
        });

        it('should throw an error if an indexed search key is not provided', async () => {
            try {
                await productPriceService.queryProductPrices({isFixed: true});
                fail('No error thrown');
            } catch (err) {
                expect(err.message).toEqual('You must include a valid indexed field when querying for product prices');
            }
        });

        it('should find or omit mixed prices', async () => {
            const priceListId = 'priceListId1';
            const productPrice1 = getProductPrice({
                accountId,
                priceListId,
            });
            const productPrice2 = getProductPrice({
                accountId,
                fixedPriceKeys: [
                    'retail',
                ],
                priceListId,
            });
            await productPriceCollection.insertMany([
                productPrice1,
                productPrice2,
            ]);

            const allProductPrices = await productPriceService.queryProductPrices({
                priceListId,
            });
            expect(_.size(allProductPrices)).toEqual(2);

            const mixedProductPrices = await productPriceService.queryProductPrices({
                isMixed: true,
                priceListId,
            });
            expect(_.size(mixedProductPrices)).toEqual(1);
            expect(mixedProductPrices[0].priceListId).toEqual(productPrice2.priceListId);

            const nonMixedProductPrices = await productPriceService.queryProductPrices({
                isMixed: false,
                priceListId,
            });
            expect(_.size(nonMixedProductPrices)).toEqual(1);
            expect(nonMixedProductPrices[0].priceListId).toEqual(productPrice1.priceListId);
        });
    });

    describe('queueProductEventBatch', () => {

        let batchJob: BatchJob;
        let productEvents: Array<Components['schemas']['ProductEvent']>;
        beforeEach(() => {
            productEvents = [{
                brand: 'brand1',
                isNew: true,
                sku: 'sku1',
            }];
            batchJob = {
                consumerId: 'consumerId1',
                jobId: 'jobId1',
                requestTotalCount: 1,
            };
        });

        it('should receive a batch request', async () => {
            await productPriceService.queueProductEventBatch(accountId, productEvents, batchJob);
            expect(batchJobService.receiveRequest).toHaveBeenCalledWith(batchJob);
        });

        it('should queue the product events', async () => {
            const batchJobId = 'batchJobId1';
            batchJobService.receiveRequest.and.returnValue(Promise.resolve(_.extend({}, batchJob, {
                id: batchJobId,
            })));

            await productPriceService.queueProductEventBatch(accountId, productEvents, batchJob);
            expect(priceElementChangeService.queuePriceElementChangeJob).toHaveBeenCalledWith({
                accountId,
                batchJobId,
                elementAfterChange: productEvents,
                elementType: 'productEventBatch',
            });
        });
    });

    describe('queueProductFixedPricesBatch', () => {

        let batchJob: BatchJob;
        const priceListId = 'priceListId1';
        let productsAndPrices: Array<{ prices: ProductPrice['prices']; product: ProductRef }>;
        const existingNotIncluded = FixedPriceChangeType.remove;
        beforeEach(() => {
            productsAndPrices = [{
                prices: {
                    retail: 5.55,
                },
                product: {
                    brand: 'brand1',
                    sku: 'sku1',
                },
            }];
            batchJob = {
                consumerId: 'consumerId1',
                jobId: 'jobId1',
                requestTotalCount: 1,
            };
        });

        it('should receive a batch request', async () => {
            await productPriceService.queueProductFixedPricesBatch(
                accountId,
                priceListId,
                productsAndPrices,
                existingNotIncluded,
                batchJob,
            );
            expect(batchJobService.receiveRequest).toHaveBeenCalledWith(batchJob);
        });

        it('should queue the product events', async () => {
            const batchJobId = 'batchJobId1';
            batchJobService.receiveRequest.and.returnValue(Promise.resolve(_.extend({}, batchJob, {
                id: batchJobId,
            })));

            await productPriceService.queueProductFixedPricesBatch(
                accountId,
                priceListId,
                productsAndPrices,
                existingNotIncluded,
                batchJob,
            );
            expect(priceElementChangeService.queuePriceElementChangeJob).toHaveBeenCalledWith({
                accountId,
                batchJobId,
                elementAfterChange: {
                    existingNotIncluded,
                    priceListId,
                    productsAndPrices,
                },
                elementType: 'productFixedPricesSetBatch',
            });
        });
    });

    describe('saveProductPrices', () => {
        it('should set the expiration date for promotional product prices', async () => {
            const priceListId = 'priceListId1';
            const brand = 'brand1';
            const sku = 'sku1';
            const productPrice = getProductPrice({
                brand,
                priceListId,
                sku,
            });
            const productPrices: ProductPrice[] = [productPrice];

            const priceList = getPriceList({
                id: priceListId,
                type: 'promotional',
            });
            priceListService.queryPriceList.and.returnValue(Promise.resolve([priceList]));

            const expirationDate = new Date();
            priceListService.getExpirationDateForPromoPriceList.and.returnValue(expirationDate);
            const bulkWriteSpy = spyOn(productPriceCollection, 'bulkWrite');

            await productPriceService.saveProductPrices(productPrices);

            expect(bulkWriteSpy).toHaveBeenCalledWith([{
                replaceOne: {
                    filter: {
                        brand,
                        priceListId,
                        sku,
                    },
                    replacement: _.extend({expirationDate}, productPrice),
                    upsert: true,
                },
            }], MongoHelperService.getDefaultWriteConcern());
            expect(priceListService.queryPriceList).toHaveBeenCalledWith({ids: [priceListId]});
            expect(priceListService.getExpirationDateForPromoPriceList).toHaveBeenCalledWith(priceList);
        });

        it('should not interact with the database if there are no product prices provided', async () => {
            await productPriceService.saveProductPrices([]);

            expect(mongoService.getCollection).not.toHaveBeenCalled();
        });

        it('should send an SNS message with accountIds from the saved prices', async () => {
            const priceListId = 'priceListId1';
            const brand = 'brand1';
            const sku = 'sku1';
            const productPrice = getProductPrice({
                accountId,
                brand,
                priceListId,
                sku,
            });
            const productPrices: ProductPrice[] = [productPrice];

            const priceList = getPriceList({
                accountId,
                id: priceListId,
            });
            priceListService.queryPriceList.and.returnValue(Promise.resolve([priceList]));
            spyOn(productPriceCollection, 'bulkWrite');

            await productPriceService.saveProductPrices(productPrices);

            expect(awsSnsService.sendSnsMessage).toHaveBeenCalledWith(
                [{
                    accountId,
                    s3Key: jasmine.any(String),
                }],
                process.env.accountProductPriceChangeTopic,
                'Account Product Price Changes',
                process.env.awsAccountId,
            );
        });

        it('should save price changes to S3', async () => {
            const priceListId = 'priceListId1';
            const brand = 'brand1';
            const sku = 'sku1';
            const productPrice = getProductPrice({
                accountId,
                brand,
                priceListId,
                sku,
            });
            const productPrices: ProductPrice[] = [productPrice];

            const priceList = getPriceList({
                accountId,
                id: priceListId,
            });
            priceListService.queryPriceList.and.returnValue(Promise.resolve([priceList]));
            spyOn(productPriceCollection, 'bulkWrite');

            await productPriceService.saveProductPrices(productPrices);

            expect(awsS3Service.writeToS3).toHaveBeenCalledWith(jasmine.any(String), JSON.stringify(productPrices));
        });
    });

    describe('unsetRentalTermIdForAccountId', () => {
        it('should pull the rental key from fixedPriceKeys and unset price for the specific term', async () => {
            const rentalTermId1 = 'rentalTermId1';

            const updateManySpy = spyOn(productPriceCollection, 'updateMany');

            await productPriceService.unsetRentalTermIdForAccountId(rentalTermId1, accountId);

            expect(updateManySpy).toHaveBeenCalledWith({
                accountId,
            }, {
                $pull: {
                    fixedPriceKeys: `rental.${rentalTermId1}`,
                },
                $unset: {
                    [`prices.rental.${rentalTermId1}`]: 1,
                },
            }, MongoHelperService.getDefaultWriteConcern());
        });
    });

    describe('updateBrandFromProduct', () => {
        it('should update the brand value for matched products', async () => {
            const priceListId = 'priceListTestId1';
            const brand = 'myBrand';
            await productPriceCollection.insertOne(getProductPrice({brand, priceListId}));

            const brandCurrent = brand;
            const brandNew = 'newBrand';
            const logPrefix = 'testLog';

            await productPriceService.updateBrandFromProduct(brandCurrent, brandNew, logPrefix);

            const updatedDoc = await productPriceCollection.findOne({priceListId});
            expect(updatedDoc.brand).toEqual(brandNew);

        });

        it('should not update anything if no matched products', async () => {
            const priceListId = 'priceListTestId1';
            const brand = 'myBrand';
            await productPriceCollection.insertOne(getProductPrice({brand, priceListId}));

            const brandCurrent = 'otherBrand';
            const brandNew = 'newBrand';
            const logPrefix = 'testLog';

            await productPriceService.updateBrandFromProduct(brandCurrent, brandNew, logPrefix);

            const updatedDoc = await productPriceCollection.findOne({priceListId});
            // Product created keeps with original brand
            expect(updatedDoc.brand).toEqual(brand);

        });
    });

    describe('updateExpirationDateForPriceListId', () => {
        it('should set the expiration date for all prices for a specific price list', async () => {
            const priceListId = 'priceListId1';
            const expirationDate = new Date();
            await productPriceCollection.insertOne(getProductPrice({priceListId}));

            await ProductPriceService.updateExpirationDateForPriceListId(priceListId, expirationDate);

            const existingDoc = await productPriceCollection.findOne({priceListId});
            expect(existingDoc.expirationDate).toEqual(expirationDate);
        });
    });

    describe('updateFixedProductPrices', () => {
        it('should save product prices and update package prices', async () => {
            const priceListId = 'priceListId1';
            const brand = 'brand1';
            const sku = 'sku1';
            const productsAndPrices: Array<{ prices: ProductPrice['prices']; product: ProductRef }> = [{
                prices: {
                    retail: 5.55,
                },
                product: {
                    brand,
                    sku,
                },
            }];

            const saveProductPricesSpy = spyOn(productPriceService, 'saveProductPrices');
            const updatePackagePricesForComponentsSpy = spyOn(productPriceService, 'updatePackagePricesForComponents');

            await productPriceService.updateFixedProductPrices(accountId, priceListId, productsAndPrices);
            expect(saveProductPricesSpy).toHaveBeenCalledWith([{
                accountId,
                brand,
                isFixed: true,
                priceListId,
                prices: productsAndPrices[0].prices,
                sku,
            }]);
            expect(updatePackagePricesForComponentsSpy).toHaveBeenCalledWith(
                [{brand, sku}],
                accountId,
                priceListId,
            );
        });

        it('should keep existing non-included prices', async () => {
            const priceListId = 'priceListId1';
            const brand = 'brand1';
            const sku = 'sku1';
            const existingNotIncluded: FixedPriceChangeType = FixedPriceChangeType.keep;
            const productsAndPrices: Array<{ prices: ProductPrice['prices']; product: ProductRef }> = [{
                prices: {
                    retail: 5.55,
                },
                product: {
                    brand,
                    sku,
                },
            }];

            const existingProductPrice: ProductPrice = {
                accountId,
                brand,
                isFixed: false,
                priceListId,
                prices: {
                    list: 6.66,
                    rental: {
                        rentalId1: 2.22,
                    },
                    retail: 4.44,
                },
                sku,
            };

            const queryProductPriceSpy = spyOn(productPriceService, 'queryProductPrices');
            const saveProductPricesSpy = spyOn(productPriceService, 'saveProductPrices');

            queryProductPriceSpy.and.returnValue(Promise.resolve([
                existingProductPrice,
            ]));

            await productPriceService.updateFixedProductPrices(
                accountId,
                priceListId,
                productsAndPrices,
                existingNotIncluded,
            );

            expect(queryProductPriceSpy).toHaveBeenCalledWith({
                priceListId,
                productRefs: [productsAndPrices[0].product],
            });
            expect(saveProductPricesSpy).toHaveBeenCalledWith([{
                accountId,
                brand,
                fixedPriceKeys: ['retail'],
                isFixed: false,
                priceListId,
                prices: {
                    list: existingProductPrice.prices.list,
                    rental: existingProductPrice.prices.rental,
                    retail: productsAndPrices[0].prices.retail,
                },
                sku,
            }]);
        });

        it('should omit list and rental pricing if not enabled', async () => {
            const priceListId = 'priceListId1';
            const brand = 'brand1';
            const sku = 'sku1';
            const productsAndPrices: Array<{ prices: ProductPrice['prices']; product: ProductRef }> = [{
                prices: {
                    list: 1.11,
                    rental: {
                        rentalId1: 3.33,
                    },
                    retail: 5.55,
                },
                product: {
                    brand,
                    sku,
                },
            }];

            const accountSettings: AccountSettings = {
                accountId,
                listPricingEnabled: false,
                rentalPricingEnabled: false,
            };
            accountSettingsService.getForAccountId.and.returnValue(Promise.resolve(accountSettings));

            const saveProductPricesSpy = spyOn(productPriceService, 'saveProductPrices');

            await productPriceService.updateFixedProductPrices(accountId, priceListId, productsAndPrices);
            expect(saveProductPricesSpy).toHaveBeenCalledWith([{
                accountId,
                brand,
                isFixed: true,
                priceListId,
                prices: _.pick(productsAndPrices[0].prices, 'retail'),
                sku,
            }]);
        });
    });

    describe('updatePackagePricesForComponents', () => {
        let accountSettings: AccountSettings;
        const brand = 'brand1';
        let componentRefs: ProductRef[];
        let existingProductPrices: ProductPrice[];
        let packageProductsWithComponents: ProductPackageData[];
        let priceList: PriceList;
        const priceListId = 'priceListId1';
        let queryProductPricesSpy;
        let saveProductPricesSpy;

        beforeEach(() => {
            componentRefs = [
                {
                    brand,
                    sku: 'component1',
                },
                {
                    brand,
                    sku: 'component2',
                },
            ];

            priceList = getPriceList({
                id: priceListId,
            });

            priceListService.queryPriceList.and.returnValue(Promise.resolve([
                priceList,
            ]));

            accountSettings = {
                accountId,
                packagedProductPriceCalculationMethod: PackageCalculationMethod.mergedPrice,
                packagedProductPriceRound: false,
                packagedProductShowPriceIfComponentZero: false,
            };
            packageProductsWithComponents = [
                {
                    brand,
                    components: [
                        {
                            ...componentRefs[0],
                            quantity: 1,
                        },
                        {
                            ...componentRefs[1],
                            quantity: 1,
                        },
                    ],
                    isPackage: true,
                    sku: 'package1',
                },
            ];

            existingProductPrices = [
                {
                    accountId,
                    brand,
                    isFixed: false,
                    priceListId,
                    prices: {
                        retail: 1.99,
                    },
                    sku: componentRefs[0].sku,
                },
                {
                    accountId,
                    brand,
                    isFixed: false,
                    priceListId,
                    prices: {
                        retail: 2.99,
                    },
                    sku: componentRefs[1].sku,
                },
            ];

            accountSettingsService.getForAccountId.and.returnValue(Promise.resolve(accountSettings));
            productSelectionService.fetchPackageData.and.returnValue(Promise.resolve([]));
            productPackageService.getPackageProductsWithComponents.and.returnValue(packageProductsWithComponents);
            queryProductPricesSpy = spyOn(productPriceService, 'queryProductPrices');
            queryProductPricesSpy.and.returnValue(Promise.resolve(existingProductPrices));
            saveProductPricesSpy = spyOn(productPriceService, 'saveProductPrices');
            saveProductPricesSpy.and.returnValue(Promise.resolve());
        });

        it('should not round prices if packagedProductPriceRound is not true', async () => {
            const expectedProductPrice: ProductPrice = {
                accountId,
                brand,
                isFixed: false,
                priceListId,
                prices: {
                    rental: {},
                    retail: 4.98,
                },
                sku: packageProductsWithComponents[0].sku,
            };

            await productPriceService.updatePackagePricesForComponents(componentRefs, accountId, priceListId);

            expect(saveProductPricesSpy).toHaveBeenCalledWith([expectedProductPrice]);
        });

        it('should round prices if packagedProductPriceRound is true and component cents match', async () => {
            accountSettings.packagedProductPriceRound = true;

            const expectedProductPrice: ProductPrice = {
                accountId,
                brand,
                isFixed: false,
                priceListId,
                prices: {
                    rental: {},
                    retail: 4.99,
                },
                sku: packageProductsWithComponents[0].sku,
            };

            await productPriceService.updatePackagePricesForComponents(componentRefs, accountId, priceListId);

            expect(saveProductPricesSpy).toHaveBeenCalledWith([expectedProductPrice]);
        });

        it('should not round prices if component cents match do not match', async () => {
            accountSettings.packagedProductPriceRound = true;
            existingProductPrices[0].prices.retail = 1.98;
            existingProductPrices[1].prices.retail = 2.99;

            const expectedProductPrice: ProductPrice = {
                accountId,
                brand,
                isFixed: false,
                priceListId,
                prices: {
                    rental: {},
                    retail: 4.97,
                },
                sku: packageProductsWithComponents[0].sku,
            };

            await productPriceService.updatePackagePricesForComponents(componentRefs, accountId, priceListId);

            expect(saveProductPricesSpy).toHaveBeenCalledWith([expectedProductPrice]);
        });

        it('should not round prices if rounded price is less than sum', async () => {
            accountSettings.packagedProductPriceRound = true;
            existingProductPrices[0].prices.retail = 1.24;
            existingProductPrices[1].prices.retail = 2.24;

            const expectedProductPrice: ProductPrice = {
                accountId,
                brand,
                isFixed: false,
                priceListId,
                prices: {
                    rental: {},
                    retail: 3.48,
                },
                sku: packageProductsWithComponents[0].sku,
            };

            await productPriceService.updatePackagePricesForComponents(componentRefs, accountId, priceListId);

            expect(saveProductPricesSpy).toHaveBeenCalledWith([expectedProductPrice]);
        });

        it('should retain fixed price for non-calculated value', async () => {
            existingProductPrices.push({
                accountId,
                brand,
                fixedPriceKeys: [
                    'list',
                ],
                isFixed: false,
                priceListId,
                prices: {
                    list: 5.55,
                    retail: 2.99,
                },
                sku: packageProductsWithComponents[0].sku,
            });

            const expectedProductPrice: ProductPrice = {
                accountId,
                brand,
                fixedPriceKeys: [
                    'list',
                ],
                isFixed: false,
                priceListId,
                prices: {
                    list: 5.55,
                    rental: {},
                    retail: 4.98,
                },
                sku: packageProductsWithComponents[0].sku,
            };

            await productPriceService.updatePackagePricesForComponents(componentRefs, accountId, priceListId);

            expect(saveProductPricesSpy).toHaveBeenCalledWith([expectedProductPrice]);
        });

        it('should not generate a price if a component is missing', async () => {
            existingProductPrices.pop();

            await productPriceService.updatePackagePricesForComponents(componentRefs, accountId, priceListId);

            expect(saveProductPricesSpy).not.toHaveBeenCalled();
        });
    });

    describe('updatePricesForMutatedProducts', () => {

        let deletePricesForDeletedProductsSpy;
        let matchPriceRulesForPriceListAndProductsSpy;
        let mutatedProductRefs: Array<ProductRef & {
            changedProperties?: string[];
            isDeleted?: boolean;
            isNew?: boolean;
        }>;
        let queryProductPricesSpy;
        let saveProductPricesSpy;
        beforeEach(() => {
            mutatedProductRefs = [];

            deletePricesForDeletedProductsSpy = spyOn(productPriceService, 'deletePricesForDeletedProducts');
            matchPriceRulesForPriceListAndProductsSpy = spyOn(
                productPriceService,
                'matchPriceRulesForPriceListAndProducts',
            );
            queryProductPricesSpy = spyOn(productPriceService, 'queryProductPrices');
            saveProductPricesSpy = spyOn(productPriceService, 'saveProductPrices');

            deletePricesForDeletedProductsSpy.and.returnValue(Promise.resolve());
            queryProductPricesSpy.and.returnValue(Promise.resolve());
            saveProductPricesSpy.and.returnValue(Promise.resolve());
        });

        it('should send new and changed property products to be price rule matched', async () => {
            const brand = 'brand1';
            const skuNew = 'sku1';
            const skuDeleted = 'sku2';
            const skuChanged = 'sku3';
            mutatedProductRefs = [
                {
                    brand,
                    isNew: true,
                    sku: skuNew,
                },
                {
                    brand,
                    isDeleted: true,
                    sku: skuDeleted,
                },
                {
                    brand,
                    changedProperties: ['msrpPrice'],
                    sku: skuChanged,
                },
            ];

            const priceRuleId1 = 'priceRuleId1';
            const priceList = getPriceList({
                accountId,
                priceRuleIds: [priceRuleId1],
            });

            const priceRule = getPriceRule({
                accountId,
                id: priceRuleId1,
            });

            priceListService.queryPriceList.and.returnValue(Promise.resolve([priceList]));
            priceRuleService.queryPriceRule.and.returnValue(Promise.resolve([priceRule]));

            await productPriceService.updatePricesForMutatedProducts(accountId, mutatedProductRefs);
            expect(matchPriceRulesForPriceListAndProductsSpy).toHaveBeenCalledWith(
                priceList,
                [{
                    brand,
                    sku: skuNew,
                }, {
                    brand,
                    sku: skuChanged,
                }],
            );
        });

        describe('updatePricesForPriceList', () => {

            let brand: string;
            let packageData: ProductPackageData[];
            let priceListId: string;
            let priceRuleId1: string;
            let products: Product[];
            let sku1: string;
            let skuPack1: string;
            let skuPack2: string;
            let skuComp1: string;
            let skuComp2: string;
            let skuComp3: string;
            beforeEach(() => {
                brand = 'brand1';
                sku1 = 'sku1';
                skuPack1 = 'skuPack1';
                skuPack2 = 'skuPack2';
                skuComp1 = 'skuComp1';
                skuComp2 = 'skuComp2';
                skuComp3 = 'skuComp3';

                mutatedProductRefs = [
                    {
                        brand,
                        isNew: true,
                        sku: sku1,
                    },
                    {
                        brand,
                        isNew: true,
                        sku: skuPack1,
                    },
                    {
                        brand,
                        isNew: true,
                        sku: skuComp1,
                    },
                    {
                        brand,
                        changedProperties: ['basePrice'],
                        sku: skuComp2,
                    },
                ];

                priceListId = 'priceListId';
                priceRuleId1 = 'priceRuleId1';
                const priceList = getPriceList({
                    accountId,
                    id: priceListId,
                    priceRuleIds: [priceRuleId1],
                });

                const priceRule = getPriceRule({
                    accountId,
                    calculation: {
                        retail: {
                            basePriceDef: 'cost',
                        },
                    },
                    id: priceRuleId1,
                    priceListId,
                });

                const existingPricesForPriceList: ProductPrice[] = [
                    {
                        accountId,
                        brand,
                        isFixed: false,
                        priceListId,
                        priceRuleId: priceRuleId1,
                        prices: {
                            retail: 50,
                        },
                        sku: skuPack2,
                    },
                    {
                        accountId,
                        brand,
                        isFixed: false,
                        priceListId,
                        priceRuleId: priceRuleId1,
                        prices: {
                            retail: 20,
                        },
                        sku: skuComp2,
                    },
                    {
                        accountId,
                        brand,
                        isFixed: false,
                        priceListId,
                        priceRuleId: priceRuleId1,
                        prices: {
                            retail: 30,
                        },
                        sku: skuComp3,
                    },
                ];

                packageData = [
                    {
                        brand,
                        components: [
                            {
                                brand,
                                quantity: 1,
                                sku: skuComp1,
                            },
                            {
                                brand,
                                quantity: 2,
                                sku: skuComp2,
                            },
                        ],
                        isPackage: true,
                        sku: skuPack1,
                    },
                    {
                        brand,
                        components: [
                            {
                                brand,
                                quantity: 2,
                                sku: skuComp2,
                            },
                            {
                                brand,
                                quantity: 3,
                                sku: skuComp3,
                            },
                        ],
                        isPackage: true,
                        sku: skuPack2,
                    },
                    {
                        brand,
                        isComponent: true,
                        packages: [
                            {
                                brand,
                                quantity: 1,
                                sku: skuPack1,
                            },
                        ],
                        sku: skuComp1,
                    },
                    {
                        brand,
                        isComponent: true,
                        packages: [
                            {
                                brand,
                                quantity: 2,
                                sku: skuPack1,
                            },
                            {
                                brand,
                                quantity: 2,
                                sku: skuPack2,
                            },
                        ],
                        sku: skuComp2,
                    },
                ];

                // This should return package products as well if using mergedCost method
                products = [
                    {
                        basePrice: 100,
                        brand,
                        sku: sku1,
                    },
                    {
                        basePrice: 10,
                        brand,
                        sku: skuComp1,
                    },
                    {
                        basePrice: 20,
                        brand,
                        sku: skuComp2,
                    },
                ];

                priceListService.queryPriceList.and.returnValue(Promise.resolve([priceList]));
                priceRuleService.queryPriceRule.and.returnValue(Promise.resolve([priceRule]));
                queryProductPricesSpy.and.returnValue(Promise.resolve(existingPricesForPriceList));
                productSelectionService.fetchPackageData.and.returnValue(Promise.resolve(packageData));
                matchPriceRulesForPriceListAndProductsSpy.and.callFake((argPriceList, productRefs) => _.map(
                    productRefs,
                    (product) => ({
                        priceRule,
                        product,
                    }),
                ));
                productSelectionService.fetch.and.returnValue(Promise.resolve(products));
            });

            describe('packageProducts', () => {

                let calculateProductPricesWithPriceRuleSpy;
                beforeEach(() => {
                    calculateProductPricesWithPriceRuleSpy = spyOn(
                        productPriceService,
                        'calculateProductPricesWithPriceRule',
                    );
                    calculateProductPricesWithPriceRuleSpy.and.callFake((argAccountId, product, priceRule) => {
                        const productPrice: ProductPrice = {
                            accountId: argAccountId,
                            brand: product.brand,
                            isFixed: false,
                            priceListId: priceRule.priceListId,
                            priceRuleId: priceRule.id,
                            prices: {
                                list: null,
                                rental: {},
                                retail: null,
                            },
                            sku: product.sku,
                        };

                        const basePrice = product.basePrice;
                        _.forOwn(priceRule.calculation, (value, field) => {
                            if (field === 'rental') {
                                _.forOwn(value, (calculation, rentalId) => {
                                    productPrice.prices.rental[rentalId] = _.isNil(calculation) ? null : basePrice;
                                });
                            } else {
                                productPrice.prices[field] = _.isNil(value) ? null : basePrice;
                            }
                        });
                        return ObjectHelperService.removeNil(productPrice);
                    });
                });

                describe('mergedCost', () => {

                    beforeEach(() => {
                        const accountSettings: AccountSettings = {
                            accountId: 'accountId1',
                            packagedProductPriceCalculationMethod: PackageCalculationMethod.mergedCost,
                        };
                        accountSettingsService.getForAccountId.and.returnValue(Promise.resolve(accountSettings));

                        // Append the package product which is treated like a regular product for mergedCost
                        products.push({
                            basePrice: 100,
                            brand,
                            sku: skuPack1,
                        });
                    });

                    it('should not request package data', async () => {
                        await productPriceService.updatePricesForMutatedProducts(accountId, mutatedProductRefs);
                        expect(productSelectionService.fetchPackageData).not.toHaveBeenCalled();
                    });

                    it('should calculate price using price rule and basePrice', async () => {
                        const expectedSavedPrices = [
                            {
                                accountId,
                                brand,
                                isFixed: false,
                                priceListId,
                                priceRuleId: priceRuleId1,
                                prices: {
                                    rental: {},
                                    retail: 100,
                                },
                                sku: sku1,
                            },
                            {
                                accountId,
                                brand,
                                isFixed: false,
                                priceListId,
                                priceRuleId: priceRuleId1,
                                prices: {
                                    rental: {},
                                    retail: 10,
                                },
                                sku: skuComp1,
                            },
                            {
                                accountId,
                                brand,
                                isFixed: false,
                                priceListId,
                                priceRuleId: priceRuleId1,
                                prices: {
                                    rental: {},
                                    retail: 20,
                                },
                                sku: skuComp2,
                            },
                            {
                                accountId,
                                brand,
                                isFixed: false,
                                priceListId,
                                priceRuleId: priceRuleId1,
                                prices: {
                                    rental: {},
                                    retail: 100,
                                },
                                sku: skuPack1,
                            },
                        ];
                        await productPriceService.updatePricesForMutatedProducts(accountId, mutatedProductRefs);
                        expect(saveProductPricesSpy).toHaveBeenCalledWith(expectedSavedPrices);
                    });
                });

                describe('mergedPrice', () => {

                    let accountSettings: AccountSettings;
                    beforeEach(() => {
                        accountSettings = {
                            accountId: 'accountId1',
                            packagedProductPriceCalculationMethod: PackageCalculationMethod.mergedPrice,
                            packagedProductShowPriceIfComponentZero: false,
                        };
                        accountSettingsService.getForAccountId.and.returnValue(Promise.resolve(accountSettings));
                    });

                    it('should request package data', async () => {
                        await productPriceService.updatePricesForMutatedProducts(accountId, mutatedProductRefs);
                        expect(productSelectionService.fetchPackageData).toHaveBeenCalled();
                    });

                    it('should calculate price based on component prices', async () => {
                        productPackageService.getPackageProductsWithComponents.and.returnValue(
                            _.filter(packageData, (data) => data.isPackage),
                        );

                        const expectedComponentPrices = [
                            {
                                accountId,
                                brand,
                                isFixed: false,
                                priceListId,
                                priceRuleId: priceRuleId1,
                                prices: {
                                    rental: {},
                                    retail: 100,
                                },
                                sku: 'sku1',
                            },
                            {
                                accountId,
                                brand,
                                isFixed: false,
                                priceListId,
                                priceRuleId: priceRuleId1,
                                prices: {
                                    rental: {},
                                    retail: 10,
                                },
                                sku: 'skuComp1',
                            },
                            {
                                accountId,
                                brand,
                                isFixed: false,
                                priceListId,
                                priceRuleId: priceRuleId1,
                                prices: {
                                    rental: {},
                                    retail: 20,
                                },
                                sku: 'skuComp2',
                            },
                        ];

                        const expectedPackagePrices = [
                            {
                                accountId,
                                brand,
                                isFixed: false,
                                priceListId,
                                prices: {
                                    rental: {},
                                    retail: 50,
                                },
                                sku: skuPack1,
                            },
                            {
                                accountId,
                                brand,
                                isFixed: false,
                                priceListId,
                                prices: {
                                    rental: {},
                                    retail: 130,
                                },
                                sku: skuPack2,
                            },
                        ];

                        await productPriceService.updatePricesForMutatedProducts(accountId, mutatedProductRefs);
                        const saveProductPricesCallArgs = saveProductPricesSpy.calls.allArgs();
                        expect(saveProductPricesCallArgs[0][0]).toEqual(expectedComponentPrices);
                        expect(saveProductPricesCallArgs[1][0]).toEqual(expectedPackagePrices);
                    });
                });
            });
        });
    });

    describe('updatePricesForPriceElementChange', () => {
        let priceElementChangeJob: PriceElementChangeJob;
        let queryProductPricesSpy;
        let saveProductPricesSpy;
        beforeEach(() => {
            priceElementChangeJob = {
                accountId,
                createdAt: '',
                elementAfterChange: undefined,
                elementBeforeChange: undefined,
                elementId: '',
                elementType: undefined,
                id: '',
            };

            queryProductPricesSpy = spyOn(productPriceService, 'queryProductPrices');
            saveProductPricesSpy = spyOn(productPriceService, 'saveProductPrices');

            queryProductPricesSpy.and.returnValue(Promise.resolve());
            saveProductPricesSpy.and.returnValue(Promise.resolve());
        });

        it('should publish the completion event', async () => {
            await productPriceService.updatePricesForPriceElementChange(priceElementChangeJob);
            expect(priceElementChangeService.publishPriceElementChangeJobCompletion)
                .toHaveBeenCalledWith(priceElementChangeJob);
        });

        it('should not trigger a change for price list if priceRuleIds have not changed', async () => {
            const priceListId = 'priceListId1';
            priceElementChangeJob.elementType = 'priceList';
            priceElementChangeJob.elementId = priceListId;
            const priceListBefore = getPriceList({
                accountId,
                id: priceListId,
                name: 'Price List Before',
                priceRuleIds: [],
            });
            const priceListAfter = _.cloneDeep(priceListBefore);
            priceListAfter.name = 'Price List After';
            priceElementChangeJob.elementBeforeChange = priceListBefore;
            priceElementChangeJob.elementAfterChange = priceListAfter;
            priceListService.queryPriceList.and.returnValue(Promise.resolve([priceListAfter]));

            await productPriceService.updatePricesForPriceElementChange(priceElementChangeJob);
            expect(productSelectionService.fetch).not.toHaveBeenCalled();
        });

        it('should trigger a change for price list if priceRuleIds have changed', async () => {
            const priceListId = 'priceListId1';
            const priceRuleId1 = 'priceRuleId1';
            const priceRuleId2 = 'priceRuleId2';
            priceElementChangeJob.elementType = 'priceList';
            priceElementChangeJob.elementId = priceListId;
            const priceListBefore = getPriceList({
                accountId,
                id: priceListId,
                name: 'Price List Before',
                priceRuleIds: [
                    priceRuleId1,
                    priceRuleId2,
                ],
            });
            const priceListAfter = _.cloneDeep(priceListBefore);
            priceListAfter.priceRuleIds = [
                priceRuleId2,
                priceRuleId1,
            ];
            priceListAfter.name = 'Price List After';
            priceElementChangeJob.elementBeforeChange = priceListBefore;
            priceElementChangeJob.elementAfterChange = priceListAfter;
            priceListService.queryPriceList.and.returnValue(Promise.resolve([priceListAfter]));

            await productPriceService.updatePricesForPriceElementChange(priceElementChangeJob);
            expect(productSelectionService.fetch).toHaveBeenCalled();
        });

        it('should not trigger a change for price rule if calculation or selection has not changed', async () => {
            const priceRuleId1 = 'priceRuleId1';
            const priceRuleId2 = 'priceRuleId2';
            const priceListId = 'priceListId';
            const brand1 = 'brand1';
            const brand2 = 'brand2';
            const priceList = getPriceList({
                accountId,
                id: priceListId,
                name: 'Price List',
                priceRuleIds: [
                    priceRuleId1,
                    priceRuleId2,
                ],
            });
            priceElementChangeJob.elementType = 'priceRule';
            priceElementChangeJob.elementId = priceRuleId1;
            const priceRuleBefore = getPriceRule({
                accountId,
                id: priceRuleId1,
                name: 'Price Rule Before',
                priceListId,
            });
            const priceRuleAfter = _.cloneDeep(priceRuleBefore);
            priceRuleAfter.name = 'Price Rule After';
            priceElementChangeJob.elementBeforeChange = priceRuleBefore;
            priceElementChangeJob.elementAfterChange = priceRuleAfter;
            priceListService.queryPriceList.and.returnValue(Promise.resolve([priceList]));
            priceRuleService.queryPriceRule.and.returnValue([priceRuleAfter]);
            queryProductPricesSpy.and.returnValue(Promise.resolve([
                {brand: brand1, sku: 'sku1'},
                {brand: brand2, sku: 'sku2'},
            ]));

            await productPriceService.updatePricesForPriceElementChange(priceElementChangeJob);
            expect(productSelectionService.fetch).not.toHaveBeenCalled();
        });

        it('should trigger a match for price rule if selection has changed', async () => {
            const priceRuleId1 = 'priceRuleId1';
            const priceRuleId2 = 'priceRuleId2';
            const priceListId = 'priceListId';
            const brand1 = 'brand1';
            const brand2 = 'brand2';
            const priceList = getPriceList({
                accountId,
                id: priceListId,
                name: 'Price List',
                priceRuleIds: [
                    priceRuleId1,
                    priceRuleId2,
                ],
            });
            priceElementChangeJob.elementType = 'priceRule';
            priceElementChangeJob.elementId = priceRuleId1;
            const priceRuleBefore = getPriceRule({
                accountId,
                enabled: true,
                id: priceRuleId1,
                name: 'Price Rule Before',
                priceListId,
                productSelectionCriteria: [
                    {
                        conditions: [
                            {
                                field: 'brand',
                                operator: 'in',
                                value: [brand1, brand2],
                            },
                        ],
                        operation: 'and',
                    },
                ],
            });
            const priceRuleAfter = _.cloneDeep(priceRuleBefore);
            priceRuleAfter.productSelectionCriteria[0].conditions[0].value = [brand1];
            priceElementChangeJob.elementBeforeChange = priceRuleBefore;
            priceElementChangeJob.elementAfterChange = priceRuleAfter;
            priceListService.queryPriceList.and.returnValue(Promise.resolve([priceList]));
            priceRuleService.queryPriceRule.and.returnValue([priceRuleAfter]);
            queryProductPricesSpy.and.returnValue(Promise.resolve([
                {brand: brand1, sku: 'sku1'},
                {brand: brand2, sku: 'sku2'},
            ]));

            await productPriceService.updatePricesForPriceElementChange(priceElementChangeJob);
            expect(productSelectionService.match).toHaveBeenCalled();
        });

        it('should trigger a calculation for price rule if calculation has changed', async () => {
            const priceRuleId1 = 'priceRuleId1';
            const priceRuleId2 = 'priceRuleId2';
            const priceListId = 'priceListId';
            const brand1 = 'brand1';
            const brand2 = 'brand2';
            const priceList = getPriceList({
                accountId,
                id: priceListId,
                name: 'Price List',
                priceRuleIds: [
                    priceRuleId1,
                    priceRuleId2,
                ],
            });
            priceElementChangeJob.elementType = 'priceRule';
            priceElementChangeJob.elementId = priceRuleId1;
            const priceRuleBefore = getPriceRule({
                accountId,
                calculation: {
                    retail: {
                        basePriceDef: 'cost',
                        expressions: [],
                        minIsMAP: false,
                    },
                },
                enabled: true,
                id: priceRuleId1,
                name: 'Price Rule Before',
                priceListId,
            });
            const priceRuleAfter = _.cloneDeep(priceRuleBefore);
            priceRuleAfter.calculation.retail.basePriceDef = 'msrp';
            priceElementChangeJob.elementBeforeChange = priceRuleBefore;
            priceElementChangeJob.elementAfterChange = priceRuleAfter;
            priceListService.queryPriceList.and.returnValue(Promise.resolve([priceList]));
            priceRuleService.queryPriceRule.and.returnValue([priceRuleAfter]);
            queryProductPricesSpy.and.returnValue(Promise.resolve([
                {brand: brand1, sku: 'sku1'},
                {brand: brand2, sku: 'sku2'},
            ]));

            await productPriceService.updatePricesForPriceElementChange(priceElementChangeJob);
            expect(productSelectionService.match).not.toHaveBeenCalled();
            expect(productSelectionService.fetch).toHaveBeenCalled();
        });

        describe('processAccountSettingsChange', () => {
            let accountSettingsBeforeChange: AccountSettings;
            let accountSettingsAfterChange: AccountSettings;
            let priceList: PriceList;

            const priceRuleId1 = 'priceRuleId1';
            const priceListId = 'priceListId';
            const brand = 'brand1';
            const sku1 = 'sku1';
            const sku2 = 'sku2';
            const sku3 = 'sku3';

            beforeEach(() => {
                accountSettingsBeforeChange = {
                    accountId,
                };

                accountSettingsAfterChange = {
                    accountId,
                };

                priceElementChangeJob.elementType = 'accountSettings';
                priceElementChangeJob.elementAfterChange = accountSettingsAfterChange;
                priceElementChangeJob.elementBeforeChange = accountSettingsBeforeChange;

                accountSettingsService.getForAccountId.and.returnValue(Promise.resolve(accountSettingsAfterChange));

                priceList = getPriceList({
                    accountId,
                    id: priceListId,
                    name: 'Price List',
                    priceRuleIds: [
                        priceRuleId1,
                    ],
                });
                priceListService.queryPriceList.and.returnValue(Promise.resolve([priceList]));

                const productPackageData: ProductPackageData[] = [
                    {
                        brand,
                        components: [
                            {
                                brand,
                                quantity: 1,
                                sku: sku2,
                            },
                            {
                                brand,
                                quantity: 1,
                                sku: sku3,
                            },
                        ],
                        isPackage: true,
                        sku: sku1,
                    },
                    {
                        brand,
                        isComponent: true,
                        packages: [
                            {
                                brand,
                                quantity: 1,
                                sku: sku1,
                            },
                        ],
                        sku: sku2,
                    },
                    {
                        brand,
                        isComponent: true,
                        packages: [
                            {
                                brand,
                                quantity: 1,
                                sku: sku1,
                            },
                        ],
                        sku: sku3,
                    },
                ];

                productSelectionService.fetchPackageData.and.returnValue(
                    Promise.resolve(productPackageData)
                );

                productPackageService.extractPackageProducts.and.returnValue(
                    _.filter(productPackageData, ['isPackage', true])
                );
                productPackageService.getUniqueComponentRefsFromPackageProducts.and.returnValue(
                    _.map(
                        _.filter(productPackageData, ['isComponent', true]),
                        (data) => _.pick(data, ['brand', 'sku']),
                    ),
                );
            });

            it('should not update prices if there is no reason', async () => {
                _.extend(accountSettingsBeforeChange, {
                    listPricingEnabled: false,
                    rentalPricingEnabled: false,
                });

                _.extend(accountSettingsAfterChange, {
                    listPricingEnabled: false,
                    rentalPricingEnabled: false,
                });

                await productPriceService.updatePricesForPriceElementChange(priceElementChangeJob);

                expect(queryProductPricesSpy).not.toHaveBeenCalled();
                expect(saveProductPricesSpy).not.toHaveBeenCalled();
            });

            it('should remove list and rental prices if they are disabled', async () => {
                _.extend(accountSettingsBeforeChange, {
                    listPricingEnabled: true,
                    rentalPricingEnabled: true,
                });

                _.extend(accountSettingsAfterChange, {
                    listPricingEnabled: false,
                    rentalPricingEnabled: false,
                });

                const existingProductPrices: ProductPrice[] = [
                    {
                        accountId,
                        brand,
                        fixedPriceKeys: ['list'],
                        isFixed: false,
                        priceListId,
                        priceRuleId: priceRuleId1,
                        prices: {
                            list: 1.11,
                            rental: {
                                rentalId1: 1.11,
                            },
                            retail: 1.11,
                        },
                        sku: sku1,
                    },
                    {
                        accountId,
                        brand,
                        isFixed: true,
                        priceListId,
                        prices: {
                            list: 2.22,
                            rental: {
                                rentalId1: 2.22,
                            },
                            retail: 2.22,
                        },
                        sku: sku2,
                    },
                    {
                        accountId,
                        brand,
                        fixedPriceKeys: ['rental.rentalId1'],
                        isFixed: false,
                        priceListId,
                        priceRuleId: priceRuleId1,
                        prices: {
                            list: 3.33,
                            rental: {
                                rentalId1: 1.11,
                            },
                            retail: 1.11,
                        },
                        sku: sku3,
                    },
                ];

                queryProductPricesSpy.and.returnValue(Promise.resolve(_.cloneDeep(existingProductPrices)));

                await productPriceService.updatePricesForPriceElementChange(priceElementChangeJob);

                expect(saveProductPricesSpy).toHaveBeenCalledWith(
                    _.map(existingProductPrices, (existingProductPrice) => {
                        const productPrice = _.omit(existingProductPrice, ['prices.list', 'prices.rental']);
                        if (productPrice.fixedPriceKeys) {
                            productPrice.fixedPriceKeys = _.filter(
                                productPrice.fixedPriceKeys,
                                (fixedPriceKey) => !_.includes(['list', 'rental.rentalId1'], fixedPriceKey)
                            );
                        }
                        return productPrice;
                    })
                );
            });

            it('should update package product prices if package settings have changed', async () => {
                _.extend(accountSettingsBeforeChange, {
                    packagedProductPriceCalculationMethod: PackageCalculationMethod.mergedPrice,
                });

                _.extend(accountSettingsAfterChange, {
                    packagedProductPriceCalculationMethod: PackageCalculationMethod.mergedCost,
                });

                const existingProductPrices: ProductPrice[] = [
                    {
                        accountId,
                        brand,
                        fixedPriceKeys: ['list'],
                        isFixed: false,
                        priceListId,
                        priceRuleId: priceRuleId1,
                        prices: {
                            list: 1.11,
                            rental: {
                                rentalId1: 1.11,
                            },
                            retail: 1.11,
                        },
                        sku: sku1,
                    },
                    {
                        accountId,
                        brand,
                        isFixed: true,
                        priceListId,
                        prices: {
                            list: 2.22,
                            rental: {
                                rentalId1: 2.22,
                            },
                            retail: 2.22,
                        },
                        sku: sku2,
                    },
                    {
                        accountId,
                        brand,
                        fixedPriceKeys: ['rental.rentalId1'],
                        isFixed: false,
                        priceListId,
                        priceRuleId: priceRuleId1,
                        prices: {
                            list: 3.33,
                            rental: {
                                rentalId1: 1.11,
                            },
                            retail: 1.11,
                        },
                        sku: sku3,
                    },
                ];

                queryProductPricesSpy.and.returnValue(Promise.resolve(_.cloneDeep(existingProductPrices)));

                const updatePricesForPriceListSpy = spyOn(productPriceService as any, 'updatePricesForPriceList');

                await productPriceService.updatePricesForPriceElementChange(priceElementChangeJob);

                expect(updatePricesForPriceListSpy).toHaveBeenCalledWith(
                    priceList,
                    false,
                    [{brand, sku: sku1}],
                    [{brand, sku: sku1}],
                );
            });

        });

    });
});
