import {createMongoServiceSpy, getCollection, registerCollection} from '@wondersign/serverless-services';
import * as _ from 'lodash';
import {AccountSettingsService} from '../../src/services/account-settings.service';
import {getAccountSettings} from '../mocks/account-settings.mock';
import {createPriceElementChangeServiceSpy} from '../mocks/price-element-change.service.mock';
import {AccountSettings, PackageCalculationMethod} from '../../src/models/account-settings';
import { createValidationServiceServiceSpy } from '../mocks/validation-service.mock';

describe('accountSettingsService', () => {

    let accountId;
    let actualPriceElementChangeJob;
    let mongoService;
    let priceElementChangeJobId;
    let priceElementChangeService;
    let accountSettingsCollection;
    let validationService;
    const accountSettingsCollectionName = 'accountSettings';
    beforeEach(async () => {
        accountId = 'accountId1';
        mongoService = createMongoServiceSpy();
        priceElementChangeService = createPriceElementChangeServiceSpy();
        validationService = createValidationServiceServiceSpy();

        // Register the collection and ensure it's empty for each run
        await registerCollection(accountSettingsCollectionName);
        accountSettingsCollection = getCollection(accountSettingsCollectionName);
        await new Promise((resolve) => accountSettingsCollection.deleteMany({}, resolve));

        mongoService.getCollection.and.callFake(
            (collectionName) => Promise.resolve(getCollection(collectionName)),
        );

        priceElementChangeJobId = 'priceElementChangeJobId1';
        priceElementChangeService.queuePriceElementChangeJob.and.callFake((priceElementJob) => {
            actualPriceElementChangeJob = priceElementJob;

            // There is a bug in MongoMock that does not support removing _id via projection from findOneAndUpdate
            // eslint-disable-next-line no-underscore-dangle
            delete actualPriceElementChangeJob.elementAfterChange._id;

            return Promise.resolve(priceElementChangeJobId);
        });
    });

    it('should exists', () => {
        expect(AccountSettingsService).toBeTruthy();
    });

    describe('getForAccountId', () => {

        it('should find existing accountSettings by accountId', async () => {
            const accountSettings = getAccountSettings({
                accountId,
            });
            await accountSettingsCollection.insertOne(accountSettings);
            expect(await AccountSettingsService.getForAccountId(accountId)).toEqual(accountSettings);
        });

    });

    describe('isPackageRepricingRequired', () => {
        it('should return false if there are no changes', () => {
            const accountSettingsBefore: AccountSettings = getAccountSettings({
                packagedProductPriceCalculationMethod: PackageCalculationMethod.mergedPrice,
            });

            const accountSettingsAfter: AccountSettings = getAccountSettings({
                packagedProductPriceCalculationMethod: PackageCalculationMethod.mergedPrice,
            });

            expect(AccountSettingsService.isPackageRepricingRequired(accountSettingsBefore, accountSettingsAfter))
                .toBe(false);
        });

        it('should return true if there is a change in calculation method', () => {
            const accountSettingsBefore: AccountSettings = getAccountSettings({
                packagedProductPriceCalculationMethod: PackageCalculationMethod.mergedCost,
            });

            const accountSettingsAfter: AccountSettings = getAccountSettings({
                packagedProductPriceCalculationMethod: PackageCalculationMethod.mergedPrice,
            });

            expect(AccountSettingsService.isPackageRepricingRequired(accountSettingsBefore, accountSettingsAfter))
                .toBe(true);
        });

        it('should return true if using mergedPrice method and showPriceIfComponentZero changes', () => {
            const accountSettingsBefore: AccountSettings = getAccountSettings({
                packagedProductPriceCalculationMethod: PackageCalculationMethod.mergedPrice,
                packagedProductShowPriceIfComponentZero: false,
            });

            const accountSettingsAfter: AccountSettings = getAccountSettings({
                packagedProductPriceCalculationMethod: PackageCalculationMethod.mergedPrice,
                packagedProductShowPriceIfComponentZero: true,
            });

            expect(AccountSettingsService.isPackageRepricingRequired(accountSettingsBefore, accountSettingsAfter))
                .toBe(true);
        });

        it('should return true if using mergedPrice method and packagedProductPriceRound changes', () => {
            const accountSettingsBefore: AccountSettings = getAccountSettings({
                packagedProductPriceCalculationMethod: PackageCalculationMethod.mergedPrice,
                packagedProductPriceRound: false,
            });

            const accountSettingsAfter: AccountSettings = getAccountSettings({
                packagedProductPriceCalculationMethod: PackageCalculationMethod.mergedPrice,
                packagedProductPriceRound: true,
            });

            expect(AccountSettingsService.isPackageRepricingRequired(accountSettingsBefore, accountSettingsAfter))
                .toBe(true);
        });

        it('should return false if using mergedCost method and packagedProductPriceRound changes', () => {
            const accountSettingsBefore: AccountSettings = getAccountSettings({
                packagedProductPriceCalculationMethod: PackageCalculationMethod.mergedCost,
                packagedProductPriceRound: false,
            });

            const accountSettingsAfter: AccountSettings = getAccountSettings({
                packagedProductPriceCalculationMethod: PackageCalculationMethod.mergedCost,
                packagedProductPriceRound: true,
            });

            expect(AccountSettingsService.isPackageRepricingRequired(accountSettingsBefore, accountSettingsAfter))
                .toBe(false);
        });
    });

    describe('save', () => {

        beforeEach(() => {
            accountSettingsCollection.findOneAndReplace = jasmine.createSpy(
                'accountSettingsCollection.findOneAndReplace'
            );
            accountSettingsCollection.findOneAndReplace.and.returnValue(Promise.resolve({value: undefined}));
        });

        it('should save AccountSettings and return a priceElementChangeJobId', async () => {
            const accountSettings = getAccountSettings({
                accountId,
            });

            expect(await AccountSettingsService.save(accountSettings)).toEqual({
                priceElementChangeJobId,
            });
            const mostRecentCall = accountSettingsCollection.findOneAndReplace.calls.mostRecent();
            const mostRecentCallArgs = _.get(mostRecentCall, 'args');
            expect(mostRecentCallArgs[0]).toEqual({accountId});
            expect(mostRecentCallArgs[1]).toEqual(accountSettings);
            expect(mostRecentCallArgs[2]).toEqual({
                includeResultMetadata: true,
                projection: {
                    _id: 0,
                },
                returnDocument: 'before',
                upsert: true,
                writeConcern: {
                    j: true,
                    w: 'majority',
                },
            });
        });

        it('should throw an Http Error when validation fails', async () => {
            const accountSettings = getAccountSettings({
                accountId,
            });
            validationService.validate.and.callFake(() => Promise.resolve({
                errors: [{ dataPath: '.', message: 'fails' }],
                isValid: false,
            }));
            try {
                await AccountSettingsService.save(accountSettings);
            } catch (error) {
                expect(error.message).toEqual('accountSettings . fails');
                expect(error.statusCode).toEqual(422);
            }
        });
    });
});
