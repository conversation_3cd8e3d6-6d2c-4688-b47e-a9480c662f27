import * as _ from 'lodash';
import {
    createAwsSnsServiceSpy,
    createMongoServiceSpy,
    getCollection,
    registerCollection,
} from '@wondersign/serverless-services';
import * as moment from 'moment-timezone';
import {ObjectId} from 'mongodb';
import {createPriceElementChangeServiceSpy} from '../mocks/price-element-change.service.mock';
import {PriceListService} from '../../src/services/price-list.service';
import {PriceList} from '../../src/models/price-list';
import {getPriceList} from '../mocks/price-list.mock';
import {createPriceRuleServiceSpy} from '../mocks/price-rule.service.mock';
import {getPriceRule} from '../mocks/price-rule.mock';
import {PriceRule} from '../../src/models/price-rule';
import {Components} from '../../src/models/contracts';
import {createDateTimeServiceSpy} from '../mocks/date-time.service.mock';
import config from '../helpers/env';
import {PromotionalPriceListStatus} from '../../src/models/promotional-price-list-status';
import {createProductPriceServiceSpy} from '../mocks/product-price.service.mock';
import {MongoHelperService} from '../../src/services/mongo-helper.service';

describe('PriceListService', () => {

    let accountId;
    let awsSnsService;
    let dateTimeService;
    let mongoService;
    let priceElementChangeService;
    let priceListCollection;
    const priceListCollectionName = 'priceList';
    let priceListService: PriceListService;
    let priceRuleService;
    let productPriceService;
    let promoStateCollection;
    const promoStateCollectionName = 'promoPriceListState';
    beforeEach(async () => {
        accountId = 'accountId1';
        awsSnsService = createAwsSnsServiceSpy();
        dateTimeService = createDateTimeServiceSpy();
        mongoService = createMongoServiceSpy();
        priceElementChangeService = createPriceElementChangeServiceSpy();
        priceRuleService = createPriceRuleServiceSpy();
        productPriceService = createProductPriceServiceSpy();

        // Register the collection and ensure it's empty for each run
        await registerCollection(priceListCollectionName);
        priceListCollection = getCollection(priceListCollectionName);
        await new Promise((resolve) => priceListCollection.deleteMany({}, resolve));

        await registerCollection(promoStateCollectionName);
        promoStateCollection = getCollection(promoStateCollectionName);
        await new Promise((resolve) => promoStateCollection.deleteMany({}, resolve));

        mongoService.getCollection.and.callFake(
            (collectionName) => Promise.resolve(getCollection(collectionName)),
        );

        priceListService = new PriceListService();
    });

    it('should exists', () => {
        expect(priceListService).toBeTruthy();
    });

    describe('checkPromoPriceListSchedules', () => {

        it('should send an SNS event for a promo start when an Account has a subscribed timezone', async () => {
            const timezone = 'Etc/GMT+1';
            const startMoment = moment().tz(timezone);
            const endMoment = moment().add(30, 'hours').tz(timezone);
            const priceList = getPriceList({
                accountId: 'accountId1',
                schedule: {
                    endDate: endMoment.format('Y-MM-DD'),
                    endTimeHour: endMoment.format('hh'),
                    endTimeMeridiem: endMoment.format('a') as 'am' | 'pm',
                    endTimeMinute: endMoment.format('mm'),
                    startDate: startMoment.format('Y-MM-DD'),
                    startTimeHour: startMoment.format('hh'),
                    startTimeMeridiem: startMoment.format('a') as 'am' | 'pm',
                    startTimeMinute: startMoment.format('mm'),
                },
                type: 'promotional',
            });
            const insertResult = await priceListCollection.insertOne(priceList);
            priceList.id = insertResult.insertedId.toString();

            const timezones = [
                timezone,
            ];
            dateTimeService.getTimezonesGroupedByOffset.and.returnValue(Promise.resolve([
                {
                    accountIds: [
                        priceList.accountId,
                    ],
                    offset: '-01:00',
                    timezones,
                },
            ]));
            dateTimeService.get24HourWithMeridiem.and.callThrough();
            dateTimeService.isRelativeTimeMatchForUtcTimestamp.and.callThrough();

            await PriceListService.checkPromoPriceListSchedules();
            expect(awsSnsService.sendSnsMessage).toHaveBeenCalledWith(
                [
                    {
                        accountId: priceList.accountId,
                        action: 'start',
                        priceListId: priceList.id,
                        timezones,
                    },
                ],
                config.promoPriceListActionTopic,
                'Promo Price List start / end',
                process.env.awsAccountId
            );
        });

        it('should send an SNS event for a promo end when an Account has a subscribed timezone', async () => {
            const timezone = 'Etc/GMT+1';
            const startMoment = moment().subtract(30, 'hours').tz(timezone);
            const endMoment = moment().tz(timezone);
            const priceList = getPriceList({
                accountId: 'accountId1',
                schedule: {
                    endDate: endMoment.format('Y-MM-DD'),
                    endTimeHour: endMoment.format('hh'),
                    endTimeMeridiem: endMoment.format('a') as 'am' | 'pm',
                    endTimeMinute: endMoment.format('mm'),
                    startDate: startMoment.format('Y-MM-DD'),
                    startTimeHour: startMoment.format('hh'),
                    startTimeMeridiem: startMoment.format('a') as 'am' | 'pm',
                    startTimeMinute: startMoment.format('mm'),
                },
                type: 'promotional',
            });
            const insertResult = await priceListCollection.insertOne(priceList);
            priceList.id = insertResult.insertedId.toString();

            const timezones = [
                timezone,
            ];
            dateTimeService.getTimezonesGroupedByOffset.and.returnValue(Promise.resolve([
                {
                    accountIds: [
                        priceList.accountId,
                    ],
                    offset: '-01:00',
                    timezones,
                },
            ]));
            dateTimeService.get24HourWithMeridiem.and.callThrough();
            dateTimeService.isRelativeTimeMatchForUtcTimestamp.and.callThrough();

            await PriceListService.checkPromoPriceListSchedules();
            expect(awsSnsService.sendSnsMessage).toHaveBeenCalledWith(
                [
                    {
                        accountId: priceList.accountId,
                        action: 'end',
                        priceListId: priceList.id,
                        timezones,
                    },
                ],
                config.promoPriceListActionTopic,
                'Promo Price List start / end',
                process.env.awsAccountId
            );
        });

        it('should not send an SNS event when an Account does not have a subscribed timezone', async () => {
            const timezone = 'Etc/GMT+1';
            const startMoment = moment().subtract(30, 'hours').tz(timezone);
            const endMoment = moment().tz(timezone);
            const priceList = getPriceList({
                accountId: 'accountId1',
                schedule: {
                    endDate: endMoment.format('Y-MM-DD'),
                    endTimeHour: endMoment.format('hh'),
                    endTimeMeridiem: endMoment.format('a') as 'am' | 'pm',
                    endTimeMinute: endMoment.format('mm'),
                    startDate: startMoment.format('Y-MM-DD'),
                    startTimeHour: startMoment.format('hh'),
                    startTimeMeridiem: startMoment.format('a') as 'am' | 'pm',
                    startTimeMinute: startMoment.format('mm'),
                },
                type: 'promotional',
            });
            const insertResult = await priceListCollection.insertOne(priceList);
            priceList.id = insertResult.insertedId.toString();

            const timezones = [
                timezone,
            ];
            dateTimeService.getTimezonesGroupedByOffset.and.returnValue(Promise.resolve([
                {
                    accountIds: [
                        priceList.accountId + '-other',
                    ],
                    offset: '-01:00',
                    timezones,
                },
            ]));
            dateTimeService.get24HourWithMeridiem.and.callThrough();
            dateTimeService.isRelativeTimeMatchForUtcTimestamp.and.callThrough();

            await PriceListService.checkPromoPriceListSchedules();
            expect(awsSnsService.sendSnsMessage).not.toHaveBeenCalled();
        });

        it('should queue a priceElementChange if price list is not queued and prices are not calculated', async () => {
            const timezone = 'Etc/GMT+1';
            const startMoment = moment.utc().add(2, 'hours').tz(timezone);
            const endMoment = moment.utc().add(72, 'hours').tz(timezone);
            const priceList = getPriceList({
                accountId: 'accountId1',
                schedule: {
                    endDate: endMoment.format('Y-MM-DD'),
                    endTimeHour: endMoment.format('hh'),
                    endTimeMeridiem: endMoment.format('a') as 'am' | 'pm',
                    endTimeMinute: endMoment.format('mm'),
                    startDate: startMoment.format('Y-MM-DD'),
                    startTimeHour: startMoment.format('hh'),
                    startTimeMeridiem: startMoment.format('a') as 'am' | 'pm',
                    startTimeMinute: startMoment.format('mm'),
                },
                type: 'promotional',
            });
            delete priceList._id;
            const insertResult = await priceListCollection.insertOne(_.cloneDeep(priceList));
            priceList.id = insertResult.insertedId.toString();
            dateTimeService.get24HourWithMeridiem.and.callThrough();
            dateTimeService.isRelativeTimeMatchForUtcTimestamp.and.callThrough();

            await PriceListService.checkPromoPriceListSchedules();

            const promoState = await promoStateCollection.findOne({priceListId: priceList.id});
            expect(promoState.arePricesCalculated).toBe(false);
            expect(promoState.isQueued).toBe(true);
            expect(priceElementChangeService.queuePriceElementChangeJob).toHaveBeenCalledWith({
                accountId: priceList.accountId,
                elementAfterChange: priceList,
                elementBeforeChange: priceList,
                elementId: priceList.id,
                elementType: 'promoPriceListCalculation',
            });
        });

        it('should not queue a priceElementChange if price list prices are calculated', async () => {
            const timezone = 'Etc/GMT+1';
            const startMoment = moment.utc().add(2, 'hours').tz(timezone);
            const endMoment = moment.utc().add(72, 'hours').tz(timezone);
            const priceList = getPriceList({
                accountId: 'accountId1',
                schedule: {
                    endDate: endMoment.format('Y-MM-DD'),
                    endTimeHour: endMoment.format('hh'),
                    endTimeMeridiem: endMoment.format('a') as 'am' | 'pm',
                    endTimeMinute: endMoment.format('mm'),
                    startDate: startMoment.format('Y-MM-DD'),
                    startTimeHour: startMoment.format('hh'),
                    startTimeMeridiem: startMoment.format('a') as 'am' | 'pm',
                    startTimeMinute: startMoment.format('mm'),
                },
                type: 'promotional',
            });
            delete priceList._id;
            const insertResult = await priceListCollection.insertOne(_.cloneDeep(priceList));
            priceList.id = insertResult.insertedId.toString();
            dateTimeService.get24HourWithMeridiem.and.callThrough();
            dateTimeService.isRelativeTimeMatchForUtcTimestamp.and.callThrough();

            await promoStateCollection.insertOne({
                arePricesCalculated: true,
                priceListId: priceList.id,
            });

            await PriceListService.checkPromoPriceListSchedules();

            expect(priceElementChangeService.queuePriceElementChangeJob).not.toHaveBeenCalled();
        });

        it('should not queue a priceElementChange if prices are not calculated but it is already queued', async () => {
            const timezone = 'Etc/GMT+1';
            const startMoment = moment.utc().add(2, 'hours').tz(timezone);
            const endMoment = moment.utc().add(72, 'hours').tz(timezone);
            const priceList = getPriceList({
                accountId: 'accountId1',
                schedule: {
                    endDate: endMoment.format('Y-MM-DD'),
                    endTimeHour: endMoment.format('hh'),
                    endTimeMeridiem: endMoment.format('a') as 'am' | 'pm',
                    endTimeMinute: endMoment.format('mm'),
                    startDate: startMoment.format('Y-MM-DD'),
                    startTimeHour: startMoment.format('hh'),
                    startTimeMeridiem: startMoment.format('a') as 'am' | 'pm',
                    startTimeMinute: startMoment.format('mm'),
                },
                type: 'promotional',
            });
            delete priceList._id;
            const insertResult = await priceListCollection.insertOne(_.cloneDeep(priceList));
            priceList.id = insertResult.insertedId.toString();
            dateTimeService.get24HourWithMeridiem.and.callThrough();
            dateTimeService.isRelativeTimeMatchForUtcTimestamp.and.callThrough();

            await promoStateCollection.insertOne({
                arePricesCalculated: false,
                isQueued: true,
                priceListId: priceList.id,
            });

            await PriceListService.checkPromoPriceListSchedules();

            expect(priceElementChangeService.queuePriceElementChangeJob).not.toHaveBeenCalled();
        });

    });

    describe('clonePriceList', () => {

        let priceList1: PriceList;
        let priceList2: PriceList;
        let priceRule1: PriceRule;
        let priceRule2: PriceRule;
        let queryPriceListSpy;
        let savePriceListSpy;
        beforeEach(() => {
            priceList1 = getPriceList({
                accountId,
                id: 'priceListId1',
                layout: {
                    saleIcon: {
                        uuid: 'uuid1',
                    },
                },
                schedule: {
                    endDate: '2021-05-11',
                    endTimeHour: '11',
                    endTimeMeridiem: 'pm',
                    endTimeMinute: '59',
                    startDate: '2021-05-11',
                    startTimeHour: '12',
                    startTimeMeridiem: 'am',
                    startTimeMinute: '00',
                },
            });

            priceList2 = getPriceList({
                accountId,
                id: 'priceListId2',
                layout: {
                    saleIcon: {
                        uuid: 'uuid2',
                    },
                },
                schedule: {
                    endDate: '2021-05-13',
                    endTimeHour: '10',
                    endTimeMeridiem: 'am',
                    endTimeMinute: '30',
                    startDate: '2021-05-12',
                    startTimeHour: '11',
                    startTimeMeridiem: 'pm',
                    startTimeMinute: '45',
                },
            });

            priceRule1 = getPriceRule({
                accountId,
                id: 'priceRuleId1',
                priceListId: priceList1.id,
            });

            priceRule2 = getPriceRule({
                accountId,
                id: 'priceRuleId2',
                priceListId: priceList1.id,
            });

            const priceRule3: PriceRule = getPriceRule({
                accountId,
                id: 'priceRuleId3',
                priceListId: priceList2.id,
            });

            const priceRule4: PriceRule = getPriceRule({
                accountId,
                id: 'priceRuleId4',
                priceListId: priceList2.id,
            });

            priceList1.priceRuleIds.push(
                priceRule1.id,
                priceRule2.id,
            );

            priceList2.priceRuleIds.push(
                priceRule3.id,
                priceRule4.id,
            );

            const existingPriceListsMap: {[id: string]: PriceList} = {
                [priceList1.id]: priceList1,
                [priceList2.id]: priceList2,
            };

            const existingPriceRulesMap: {[id: string]: PriceList} = {
                [priceRule1.id]: priceRule1,
                [priceRule2.id]: priceRule2,
                [priceRule3.id]: priceRule3,
                [priceRule4.id]: priceRule4,
            };

            queryPriceListSpy = spyOn(priceListService, 'queryPriceList');
            queryPriceListSpy.and.callFake(
                (query) => Promise.resolve(_.values(_.pick(existingPriceListsMap, query.ids)))
            );
            priceRuleService.queryPriceRule.and.callFake(
                (query) => Promise.resolve(_.values(_.pick(existingPriceRulesMap, query.ids)))
            );

            savePriceListSpy = spyOn(priceListService, 'savePriceList');
        });

        it('should clone simple properties from the same existing price list', async () => {
            const cloneConfig: Components['schemas']['PriceListCloneConfig'] = {
                accountId,
                clonePropertyMap: {
                    layout: priceList1.id,
                    schedule: priceList1.id,
                },
            };

            const newPriceListId = 'newPriceListId';
            savePriceListSpy.and.returnValue(Promise.resolve({
                id: newPriceListId,
            }));
            expect(await priceListService.clonePriceList(cloneConfig)).toEqual({id: newPriceListId});
            expect(savePriceListSpy).toHaveBeenCalledWith({
                accountId,
                layout: priceList1.layout,
                schedule: priceList1.schedule,
            });
        });

        it('should clone simple properties from different existing price lists', async () => {
            const cloneConfig: Components['schemas']['PriceListCloneConfig'] = {
                accountId,
                clonePropertyMap: {
                    layout: priceList1.id,
                    schedule: priceList2.id,
                },
            };

            const newPriceListId = 'newPriceListId';
            savePriceListSpy.and.returnValue(Promise.resolve({
                id: newPriceListId,
            }));
            expect(await priceListService.clonePriceList(cloneConfig)).toEqual({id: newPriceListId});
            expect(savePriceListSpy).toHaveBeenCalledWith({
                accountId,
                layout: priceList1.layout,
                schedule: priceList2.schedule,
            });
        });

        it('should clone price rules from an existing price list', async () => {
            const cloneConfig: Components['schemas']['PriceListCloneConfig'] = {
                accountId,
                clonePropertyMap: {
                    priceRuleIds: priceList1.id,
                },
            };

            const newPriceListId = 'newPriceListId';
            savePriceListSpy.and.returnValue(Promise.resolve({
                id: newPriceListId,
            }));
            expect(await priceListService.clonePriceList(cloneConfig)).toEqual({id: newPriceListId});
            expect(savePriceListSpy).toHaveBeenCalledWith({
                accountId,
            });

            const savePriceRuleArgs = priceRuleService.savePriceRule.calls.allArgs();
            expect(savePriceRuleArgs[0][0]).toEqual(_.extend({}, _.omit(priceRule2, ['id']), {
                priceListId: newPriceListId,
            }));
            expect(savePriceRuleArgs[1][0]).toEqual(_.extend({}, _.omit(priceRule1, ['id']), {
                priceListId: newPriceListId,
            }));
        });
    });

    describe('deletePriceListsWithAccountId', () => {
        it('should delete price lists from the collection', async () => {
            const deleteManySpy = spyOn(priceListCollection, 'deleteMany');
            await PriceListService.deletePriceListsWithAccountId(accountId);
            expect(deleteManySpy).toHaveBeenCalledWith({accountId});
        });
    });

    describe('deletePriceListWithId', () => {

        it('should delete the price list from the DB', async () => {
            const priceList = getPriceList({
                accountId,
            });
            priceList._id = new ObjectId();
            await priceListCollection.insertOne(priceList);
            const priceListId = priceList._id.toString();

            expect(await priceListCollection.findOne({})).not.toBeNull();
            await priceListService.deletePriceListWithId(priceListId);
            expect(await priceListCollection.findOne({})).toBeNull();
        });

        it('should delete price rules by price list id', async () => {
            const priceListId = new ObjectId();
            await priceListService.deletePriceListWithId(priceListId.toString());
            expect(priceRuleService.deletePriceRulesWithPriceListId).toHaveBeenCalledWith(priceListId.toString());
        });

        it('should delete product prices by price list id', async () => {
            const priceListId = new ObjectId();
            await priceListService.deletePriceListWithId(priceListId.toString());
            expect(productPriceService.deleteProductPricesWithPriceListId).toHaveBeenCalledWith(priceListId.toString());
        });
    });

    describe('getExpirationDateForPromoPriceList', () => {
        it('should return the current date for an invalid price list schedule', () => {
            const priceList: PriceList = getPriceList();

            const actualDate = PriceListService.getExpirationDateForPromoPriceList(priceList);
            expect(moment().diff(moment(actualDate), 's')).toBeLessThan(1);
        });

        it('should return return a valid date based on the scheduled end date and time in utc', () => {
            const endDate = new Date('2022-05-24T12:00:00Z');
            const priceList: PriceList = getPriceList({schedule: {
                endDate: '2022-05-24',
                endTimeHour: '12',
                endTimeMeridiem: 'pm',
                endTimeMinute: '00',
            }});

            dateTimeService.get24HourWithMeridiem.and.callThrough();

            const actualDate = PriceListService.getExpirationDateForPromoPriceList(priceList);
            expect(actualDate).toEqual(endDate);
        });
    });

    describe('getPromoPriceListStatus', () => {

        it('should be null for a non-promotional price list', () => {
            const priceList = getPriceList({
                type: 'default',
            });
            const timezone = 'Etc/GMT+1';

            expect(PriceListService.getPromoPriceListStatus(priceList, timezone)).toBe(null);
        });

        it('should be active if we are within the start and end times for the respective timezone', () => {
            const timezone = 'Etc/GMT+1';
            const startMoment = moment.utc().subtract(30, 'hours').tz(timezone);
            const endMoment = moment.utc().add(30, 'hours').tz(timezone);
            const priceList = getPriceList({
                schedule: {
                    endDate: endMoment.format('Y-MM-DD'),
                    endTimeHour: endMoment.format('hh'),
                    endTimeMeridiem: endMoment.format('a') as 'am' | 'pm',
                    endTimeMinute: endMoment.format('mm'),
                    startDate: startMoment.format('Y-MM-DD'),
                    startTimeHour: startMoment.format('hh'),
                    startTimeMeridiem: startMoment.format('a') as 'am' | 'pm',
                    startTimeMinute: startMoment.format('mm'),
                },
                type: 'promotional',
            });

            dateTimeService.convertRelativeTimestampToDate.and.callThrough();
            dateTimeService.get24HourWithMeridiem.and.callThrough();

            expect(PriceListService.getPromoPriceListStatus(priceList, timezone))
                .toBe(PromotionalPriceListStatus.active);
        });

        it('should be inactive if it is currently after the start and end times for the respective timezone', () => {
            const timezone = 'Etc/GMT+1';
            const startMoment = moment.utc().subtract(30, 'hours').tz(timezone);
            const endMoment = moment.utc().subtract(1, 'hours').tz(timezone);
            const priceList = getPriceList({
                schedule: {
                    endDate: endMoment.format('Y-MM-DD'),
                    endTimeHour: endMoment.format('hh'),
                    endTimeMeridiem: endMoment.format('a') as 'am' | 'pm',
                    endTimeMinute: endMoment.format('mm'),
                    startDate: startMoment.format('Y-MM-DD'),
                    startTimeHour: startMoment.format('hh'),
                    startTimeMeridiem: startMoment.format('a') as 'am' | 'pm',
                    startTimeMinute: startMoment.format('mm'),
                },
                type: 'promotional',
            });

            dateTimeService.convertRelativeTimestampToDate.and.callThrough();
            dateTimeService.get24HourWithMeridiem.and.callThrough();

            expect(PriceListService.getPromoPriceListStatus(priceList, timezone))
                .toBe(PromotionalPriceListStatus.inactive);
        });

        it('should be planned if it is currently before the start and end times for the respective timezone', () => {
            const timezone = 'Etc/GMT+1';
            const startMoment = moment.utc().add(1, 'hours').tz(timezone);
            const endMoment = moment.utc().add(30, 'hours').tz(timezone);
            const priceList = getPriceList({
                schedule: {
                    endDate: endMoment.format('Y-MM-DD'),
                    endTimeHour: endMoment.format('hh'),
                    endTimeMeridiem: endMoment.format('a') as 'am' | 'pm',
                    endTimeMinute: endMoment.format('mm'),
                    startDate: startMoment.format('Y-MM-DD'),
                    startTimeHour: startMoment.format('hh'),
                    startTimeMeridiem: startMoment.format('a') as 'am' | 'pm',
                    startTimeMinute: startMoment.format('mm'),
                },
                type: 'promotional',
            });

            dateTimeService.convertRelativeTimestampToDate.and.callThrough();
            dateTimeService.get24HourWithMeridiem.and.callThrough();

            expect(PriceListService.getPromoPriceListStatus(priceList, timezone))
                .toBe(PromotionalPriceListStatus.planned);
        });
    });

    describe('hasPromoPricesCalculated', () => {

        it('should return false when no record exists in the promoState collection', async () => {
            expect(await priceListService.hasPromoPricesCalculated('priceListId1')).toBe(false);
        });

        it('should return false when prices are not yet calculated', async () => {
            const priceListId = 'priceListId1';
            await promoStateCollection.insertOne({
                arePricesCalculated: false,
                priceListId,
            });
            expect(await priceListService.hasPromoPricesCalculated(priceListId)).toBe(false);
        });

        it('should return true when prices are calculated', async () => {
            const priceListId = 'priceListId1';
            await promoStateCollection.insertOne({
                arePricesCalculated: true,
                priceListId,
            });
            expect(await priceListService.hasPromoPricesCalculated(priceListId)).toBe(true);
        });
    });

    describe('isActivePromotionalPriceList', () => {

        it('should return false for a non-promotional price list', () => {
            const priceList = getPriceList({
                type: 'default',
            });
            const timezone = 'Etc/GMT+1';

            expect(priceListService.isActivePromotionalPriceList(priceList, timezone)).toBe(false);
        });

        it('should return true if the promo price list status is active', () => {
            const timezone = 'Etc/GMT+1';
            const startMoment = moment.utc().subtract(30, 'hours').tz(timezone);
            const endMoment = moment.utc().add(30, 'hours').tz(timezone);
            const priceList = getPriceList({
                schedule: {
                    endDate: endMoment.format('Y-MM-DD'),
                    endTimeHour: endMoment.format('hh'),
                    endTimeMeridiem: endMoment.format('a') as 'am' | 'pm',
                    endTimeMinute: endMoment.format('mm'),
                    startDate: startMoment.format('Y-MM-DD'),
                    startTimeHour: startMoment.format('hh'),
                    startTimeMeridiem: startMoment.format('a') as 'am' | 'pm',
                    startTimeMinute: startMoment.format('mm'),
                },
                type: 'promotional',
            });

            spyOn(PriceListService, 'getPromoPriceListStatus').and.returnValue(PromotionalPriceListStatus.active);

            expect(priceListService.isActivePromotionalPriceList(priceList, timezone)).toBe(true);
        });

        it('should return false if the promo price list status is inactive', () => {
            const timezone = 'Etc/GMT+1';
            const startMoment = moment.utc().subtract(30, 'hours').tz(timezone);
            const endMoment = moment.utc().subtract(1, 'hours').tz(timezone);
            const priceList = getPriceList({
                schedule: {
                    endDate: endMoment.format('Y-MM-DD'),
                    endTimeHour: endMoment.format('hh'),
                    endTimeMeridiem: endMoment.format('a') as 'am' | 'pm',
                    endTimeMinute: endMoment.format('mm'),
                    startDate: startMoment.format('Y-MM-DD'),
                    startTimeHour: startMoment.format('hh'),
                    startTimeMeridiem: startMoment.format('a') as 'am' | 'pm',
                    startTimeMinute: startMoment.format('mm'),
                },
                type: 'promotional',
            });

            spyOn(PriceListService, 'getPromoPriceListStatus').and.returnValue(PromotionalPriceListStatus.inactive);

            expect(priceListService.isActivePromotionalPriceList(priceList, timezone)).toBe(false);
        });

        it('should be planned if the promo price list status is planned', () => {
            const timezone = 'Etc/GMT+1';
            const startMoment = moment.utc().add(1, 'hours').tz(timezone);
            const endMoment = moment.utc().add(30, 'hours').tz(timezone);
            const priceList = getPriceList({
                schedule: {
                    endDate: endMoment.format('Y-MM-DD'),
                    endTimeHour: endMoment.format('hh'),
                    endTimeMeridiem: endMoment.format('a') as 'am' | 'pm',
                    endTimeMinute: endMoment.format('mm'),
                    startDate: startMoment.format('Y-MM-DD'),
                    startTimeHour: startMoment.format('hh'),
                    startTimeMeridiem: startMoment.format('a') as 'am' | 'pm',
                    startTimeMinute: startMoment.format('mm'),
                },
                type: 'promotional',
            });

            spyOn(PriceListService, 'getPromoPriceListStatus').and.returnValue(PromotionalPriceListStatus.planned);

            expect(priceListService.isActivePromotionalPriceList(priceList, timezone)).toBe(false);
        });

    });

    describe('isRematchingRequired', () => {

        let priceListBefore: PriceList;
        beforeEach(() => {
            priceListBefore = getPriceList({
                accountId,
                id: 'priceListId1',
                name: 'Test',
            });
        });

        it('should be required when there has been a change to priceRuleIds', () => {
            const priceListAfter = _.cloneDeep(priceListBefore);
            priceListAfter.priceRuleIds = ['priceRuleId1'];

            expect(priceListService.isRematchingRequired(
                priceListBefore,
                priceListAfter,
            )).toBe(true);
        });

        it('should not be required when there has been a change to name', () => {
            const priceListAfter = _.cloneDeep(priceListBefore);
            priceListAfter.name = 'Updated';

            expect(priceListService.isRematchingRequired(
                priceListBefore,
                priceListAfter,
            )).toBe(false);
        });

        it('should not be required when there is no priceList after change', () => {
            const priceListAfter = null;

            expect(priceListService.isRematchingRequired(
                priceListBefore,
                priceListAfter,
            )).toBe(false);
        });

    });

    describe('markPromoPricesCalculated', () => {

        it('should set arePricesCalculated to true and isQueued to false', async () => {
            const priceListId = 'priceListId1';
            await promoStateCollection.insertOne({
                arePricesCalculated: false,
                isQueued: true,
                priceListId,
            });

            await priceListService.markPromoPricesCalculated(priceListId);

            const promoStateDoc = await promoStateCollection.findOne({priceListId});
            expect(promoStateDoc.arePricesCalculated).toBe(true);
            expect(promoStateDoc.isQueued).toBe(false);
        });

    });

    describe('queryPriceList', () => {

        const accountId1 = 'accountId1';
        const accountId2 = 'accountId2';
        let priceList1Id;
        let priceList2Id;
        let priceList3Id;
        beforeEach(async () => {
            const priceLists = [];

            // Account 1 default price list
            priceLists.push(getPriceList({
                accountId: accountId1,
                type: 'default',
            }));

            // Account 1 promo 1 (order: 1)
            priceLists.push(getPriceList({
                accountId: accountId1,
                order: 1,
                type: 'promotional',
            }));

            // Account 1 promo 2 (order: 0)
            priceLists.push(getPriceList({
                accountId: accountId1,
                order: 0,
                type: 'promotional',
            }));

            // Account 2 default price list
            priceLists.push(getPriceList({
                accountId: accountId2,
                type: 'default',
            }));

            // Account 2 promo 1 (order: 0)
            priceLists.push(getPriceList({
                accountId: accountId2,
                order: 0,
                type: 'promotional',
            }));

            const insertManyResult = await priceListCollection.insertMany(priceLists);
            [
                priceList1Id,
                priceList2Id,
                priceList3Id,
            ] = insertManyResult.insertedIds;
        });

        it('should query by accountId', async () => {
            const priceLists = await priceListService.queryPriceList({accountId: accountId1});
            expect(_.size(priceLists)).toBe(3);
        });

        it('should query by ids', async () => {
            const priceLists = await priceListService.queryPriceList({ids: [priceList1Id, priceList2Id]});
            expect(_.size(priceLists)).toBe(2);
        });

        it('should not query by type alone', async () => {
            try {
                await priceListService.queryPriceList({type: 'promotional'});
                fail('Querying by type alone did not throw an exception');
            } catch (err) {
                expect(err.message).toBe('You must specify either ids or accountId when querying Price Lists');
            }
        });

        it('should return promo price lists in priority order', async () => {
            const findSpy = spyOn(priceListCollection, 'find');
            findSpy.and.returnValue({
                toArray: () => Promise.resolve([]),
            });

            await priceListService.queryPriceList({ids: [priceList2Id, priceList3Id]});
            const lastCallArgs = findSpy.calls.mostRecent().args;
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            expect(lastCallArgs[1].sort).toEqual({
                order: 1,
            });
        });

        it('should return the order property when all properties are requested', async () => {
            const priceLists = await priceListService.queryPriceList({ids: [priceList2Id]}, true);
            const hasOrder = !_.isNil(_.get(_.first(priceLists), 'order'));
            expect(hasOrder).toBe(true);
        });
    });

    describe('savePriceList', () => {

        let isRematchingRequiredSpy;
        beforeEach(() => {
            isRematchingRequiredSpy = spyOn(priceListService, 'isRematchingRequired');
            isRematchingRequiredSpy.and.returnValue(false);

            priceListCollection.findOneAndReplace = jasmine.createSpy('priceListCollection.findOneAndReplace');
        });

        it('should create a new price list', async () => {
            const priceList = getPriceList({
                accountId,
                name: 'Test',
            });

            const saveResponse = await priceListService.savePriceList(priceList);
            const priceLists = await priceListCollection.find({}).toArray() as PriceList[];
            const savedPriceList = _.first(priceLists);
            expect(savedPriceList.name).toEqual(priceList.name);
            expect(saveResponse).toEqual({
                // eslint-disable-next-line no-underscore-dangle
                id: savedPriceList._id.toString(),
                name: savedPriceList.name,
            });
        });

        it('should replace an existing price list', async () => {
            const priceList = getPriceList({
                accountId,
                name: 'Test',
            });
            const priceListReplacement = _.cloneDeep(priceList);
            const insertResult = await priceListCollection.insertOne(priceList);
            const priceListId = insertResult.insertedId.toString();

            let savedPriceList;
            priceListCollection.findOneAndReplace.and.callFake((filter, replacement) => {
                savedPriceList = _.cloneDeep(replacement);
                return Promise.resolve({
                    value: _.cloneDeep(replacement),
                });
            });

            priceListReplacement.name = 'Updated';
            priceListReplacement.id = priceListId;
            expect(await priceListService.savePriceList(priceListReplacement)).toEqual({
                id: priceListId,
                name: priceListReplacement.name,
            });

            expect(savedPriceList.name).toEqual(priceListReplacement.name);
        });

        it('should partially update an existing price list', async () => {
            const priceList = getPriceList({
                accountId,
                name: 'Test',
            });
            const insertResult = await priceListCollection.insertOne(priceList);
            const priceListId = insertResult.insertedId.toString();

            let savedPriceList;
            priceListCollection.findOneAndReplace.and.callFake((filter, replacement) => {
                savedPriceList = _.cloneDeep(replacement);
                return Promise.resolve({
                    value: _.cloneDeep(replacement),
                });
            });

            const priceListReplacement = {
                id: priceListId,
                name: 'Updated',
            };
            expect(await priceListService.savePriceList(priceListReplacement, true)).toEqual({
                id: priceListId,
                name: savedPriceList.name,
            });

            expect(savedPriceList.name).toEqual(priceListReplacement.name);
        });

        it('should call findOneAndReplace method with expected values', async () => {
            const priceList = getPriceList({
                accountId,
                name: 'Test',
            });
            const insertResult = await priceListCollection.insertOne(priceList);
            const priceListId = insertResult.insertedId.toString();

            priceListCollection.findOneAndReplace.and.callFake((filter, replacement) => Promise.resolve({
                value: _.cloneDeep(replacement),
            }));

            const priceListReplacement = {
                id: priceListId,
                name: 'Updated',
            };
            await priceListService.savePriceList(priceListReplacement, true);
            delete priceListReplacement.id;

            const findOneAndReplaceArgs = priceListCollection.findOneAndReplace.calls.first().args;
            expect(findOneAndReplaceArgs[0]).toEqual({_id: new ObjectId(priceListId)});
            expect(findOneAndReplaceArgs[1]).toEqual({ ...priceList, ...priceListReplacement });
            expect(findOneAndReplaceArgs[2]).toEqual({
                includeResultMetadata: true,
                projection: {
                    _id: 0,
                },
                returnDocument: 'after',
            });
        });

        it('should queue a price element change if rematching is required', async () => {
            const priceList = getPriceList({
                accountId,
                name: 'Test',
            });
            // eslint-disable-next-line no-underscore-dangle
            delete priceList._id;
            delete priceList.id;
            const priceListReplacement = _.cloneDeep(priceList);
            const insertResult = await priceListCollection.insertOne(priceList);
            const priceListId = insertResult.insertedId.toString();
            priceList.id = priceListId;

            priceListCollection.findOneAndReplace.and.callFake((filter, replacement) => Promise.resolve({
                value: _.cloneDeep(replacement),
            }));

            const priceElementChangeJobId = 'priceElementChangeJobId1';
            let actualPriceElementChangeJob;
            priceElementChangeService.queuePriceElementChangeJob.and.callFake((priceElementJob) => {
                actualPriceElementChangeJob = priceElementJob;

                // There is a bug in MongoMock that does not support removing _id via projection from findOneAndUpdate
                // eslint-disable-next-line no-underscore-dangle
                delete actualPriceElementChangeJob.elementAfterChange._id;

                return Promise.resolve(priceElementChangeJobId);
            });

            isRematchingRequiredSpy.and.returnValue(true);

            priceListReplacement.priceRuleIds = ['priceRuleId1'];
            priceListReplacement.id = priceListId;
            expect(await priceListService.savePriceList(priceListReplacement)).toEqual({
                id: priceListId,
                name: priceListReplacement.name,
                priceElementChangeJobId,
            });
            const elementAfterChange = {...priceListReplacement, id: priceListId};

            expect(actualPriceElementChangeJob).toEqual({
                accountId,
                elementAfterChange,
                elementBeforeChange: priceList,
                elementId: priceListId,
                elementType: 'priceList',
            });
        });

        it('should retain the existing order when doing a replace', async () => {
            const priceList = getPriceList({
                accountId,
                name: 'Test',
                order: 1,
                type: 'promotional',
            });
            const priceListReplacement = _.omit(_.cloneDeep(priceList), 'order');
            const insertResult = await priceListCollection.insertOne(priceList);
            const priceListId = insertResult.insertedId.toString();

            let savedPriceList;
            priceListCollection.findOneAndReplace.and.callFake((filter, replacement) => {
                savedPriceList = _.cloneDeep(replacement);
                return Promise.resolve({
                    value: _.cloneDeep(replacement),
                });
            });

            priceListReplacement.name = 'Updated';
            priceListReplacement.id = priceListId;
            expect(await priceListService.savePriceList(priceListReplacement)).toEqual({
                id: priceListId,
                name: priceListReplacement.name,
            });

            expect(savedPriceList.order).toEqual(1);
        });

        it('should update the promo state expiration date if the end schedule changes', async () => {
            const priceList = getPriceList({
                accountId,
                name: 'Test',
                order: 1,
                schedule: {
                    endDate: '2021-09-02',
                    endTimeHour: '10',
                    endTimeMeridiem: 'pm',
                    endTimeMinute: '00',
                    startDate: '2021-09-01',
                    startTimeHour: '10',
                    startTimeMeridiem: 'am',
                    startTimeMinute: '00',
                },
                type: 'promotional',
            });
            const priceListReplacement = _.omit(_.cloneDeep(priceList), 'order');
            const insertResult = await priceListCollection.insertOne(priceList);
            const priceListId = insertResult.insertedId.toString();

            priceListCollection.findOneAndReplace.and.callFake((filter, replacement) => Promise.resolve({
                value: _.cloneDeep(replacement),
            }));

            const updateOneSpy = spyOn(promoStateCollection, 'updateOne');
            updateOneSpy.and.returnValue(Promise.resolve());

            dateTimeService.get24HourWithMeridiem.and.callThrough();

            priceListReplacement.schedule.endDate = '2021-09-03';
            priceListReplacement.id = priceListId;
            expect(await priceListService.savePriceList(priceListReplacement)).toEqual({
                id: priceListId,
                name: priceListReplacement.name,
            });

            const updateOneArgs = updateOneSpy.calls.mostRecent().args;
            const pricesExpireAt = new Date('2021-09-03T22:00:00Z');
            expect(updateOneArgs).toEqual([
                {priceListId},
                {
                    $set: {
                        pricesExpireAt,
                    },
                },
                MongoHelperService.getDefaultWriteConcern(),
            ]);
            expect(productPriceService.updateExpirationDateForPriceListId).toHaveBeenCalledWith(
                priceListId,
                pricesExpireAt,
            );
        });

        it('should add and adjust existing order when adding new promo price list', async () => {
            const defaultPriceList = getPriceList({
                accountId,
                name: 'default',
                type: 'default',
            });

            const existingPromoPriceList = getPriceList({
                accountId,
                name: 'promo 1',
                order: 0,
                type: 'promotional',
            });

            const insertManyResult = await priceListCollection.insertMany([
                defaultPriceList,
                existingPromoPriceList,
            ]);

            const [defaultPriceListId, existingPromoPriceListId] = insertManyResult.insertedIds;
            const newPromoPriceList = getPriceList({
                accountId,
                name: 'promo 2',
                type: 'promotional',
            });

            const savedResult = await priceListService.savePriceList(newPromoPriceList);
            const priceLists = await priceListCollection.find({}).toArray();

            expect(_.size(priceLists)).toBe(3);

            _.forEach(priceLists, (priceList) => {
                switch (priceList._id.toString()) {

                    case defaultPriceListId.toString():
                        expect(priceList.order).not.toBeDefined();
                        break;

                    case existingPromoPriceListId.toString():
                        expect(priceList.order).toBe(1);
                        break;

                    case savedResult.id:
                        expect(priceList.order).toBe(0);
                }
            });
        });
    });

    describe('savePriceListIdsOrder', () => {

        it('should throw an exception if the ordered price lists ids is empty', async () => {
            try {
                await priceListService.savePriceListIdsOrder([]);
                fail('Exception was not thrown for empty price list ids');
            } catch (err) {
                expect(err.message).toEqual('Ordered price list IDs cannot be empty');
            }
        });
    });

    describe('prependPriceRuleIdForPriceList', () => {
        it('should prepend a price rule into a price list', async () => {

            spyOn(priceListCollection, 'updateOne').and.returnValue({});

            const priceListId = new ObjectId().toString();
            const priceRuleId = new ObjectId().toString();
            await priceListService.prependPriceRuleIdForPriceList(priceRuleId, priceListId);
            expect(priceListCollection.updateOne).toHaveBeenCalledTimes(1);
        });
    });

    describe('pullPriceRuleIdForPriceList', () => {
        it('should pull a price rule from a price list', async () => {
            spyOn(priceListCollection, 'updateOne').and.returnValue({});

            const priceListId = new ObjectId().toString();
            const priceRuleId = new ObjectId().toString();
            await priceListService.pullPriceRuleIdForPriceList(priceRuleId, priceListId);
            expect(priceListCollection.updateOne).toHaveBeenCalledTimes(1);
        });
    });
});
