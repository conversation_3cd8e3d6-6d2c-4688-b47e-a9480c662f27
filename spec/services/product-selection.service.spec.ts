import * as Moxios from 'moxios';
import {ProductSelectionService} from '../../src/services/product-selection.service';
import {ProductRef} from '../../src/models/product-ref';
import {Product} from '../../src/models/product';
import {ProductPackageData} from '../../src/models/product-package-data';
import {Components} from '../../src/models/contracts';

describe('ProductSelectionService', () => {
    const accountId = 'accountId1';
    const brand = 'brand1';
    const sku1 = 'sku1';
    const sku2 = 'sku2';
    const sku3 = 'sku3';
    const config = process.env;
    const baseUrl = config.pssUrl;
    let products: ProductRef[];

    beforeEach(() => {
        Moxios.install();
    });

    afterEach(() => {
        Moxios.uninstall();
    });

    it('should exist', () => {
        expect(ProductSelectionService).toBeTruthy();
    });

    describe('fetch', () => {
        const url = `${baseUrl}/fetch`;

        it('should return the results from the network request', async () => {
            products = [
                {
                    brand,
                    sku: sku1,
                },
                {
                    brand,
                    sku: sku2,
                },
            ];

            const returnedProducts: Product[] = [
                {
                    basePrice: 100,
                    brand,
                    sku: sku1,
                },
                {
                    basePrice: 200,
                    brand,
                    sku: sku2,
                },
            ];

            Moxios.stubRequest(url, {
                response: returnedProducts,
                status: 200,
            });

            const fields = ['brand'];
            const actualProducts = await ProductSelectionService.fetch(accountId, products, fields);

            const request = Moxios.requests.mostRecent();
            const requestBody = JSON.parse(request.config.data);
            expect(returnedProducts).toEqual(actualProducts);
            expect(requestBody).toEqual({
                accountId,
                fields,
                products,
            });
        });
    });

    describe('fetchPackageData', () => {
        const url = `${baseUrl}/fetch/package-data`;

        it('should return the results from the network request', async () => {
            products = [
                {
                    brand,
                    sku: sku1,
                },
                {
                    brand,
                    sku: sku2,
                },
            ];

            const returnedPackageData: ProductPackageData[] = [
                {
                    brand,
                    components: [
                        {
                            brand,
                            quantity: 1,
                            sku: sku2,
                        },
                        {
                            brand,
                            quantity: 1,
                            sku: sku3,
                        },
                    ],
                    isPackage: true,
                    sku: sku1,

                },
                {
                    brand,
                    isComponent: true,
                    packages: [
                        {
                            brand,
                            quantity: 1,
                            sku: sku1,
                        },
                    ],
                    sku: sku2,
                },
                {
                    brand,
                    isComponent: true,
                    packages: [
                        {
                            brand,
                            quantity: 1,
                            sku: sku1,
                        },
                    ],
                    sku: sku3,
                },
            ];

            Moxios.stubRequest(url, {
                response: returnedPackageData,
                status: 200,
            });

            const actualPackageData = await ProductSelectionService.fetchPackageData(accountId, products);

            const request = Moxios.requests.mostRecent();
            const requestBody = JSON.parse(request.config.data);
            expect(returnedPackageData).toEqual(actualPackageData);
            expect(requestBody).toEqual({
                accountId,
                products,
            });
        });
    });

    describe('match', () => {
        const url = `${baseUrl}/match`;

        it('should return the results from the network request', async () => {
            products = [
                {
                    brand,
                    sku: sku1,
                },
                {
                    brand,
                    sku: sku2,
                },
            ];

            const returnedMatches: Array<ProductRef & { selectionId: string }> = [
                {
                    brand,
                    selectionId: 'selectionId1',
                    sku: sku1,
                },
                {
                    brand,
                    selectionId: 'selectionId1',
                    sku: sku2,
                },
            ];

            Moxios.stubRequest(url, {
                response: returnedMatches,
                status: 200,
            });

            const useProductsAsProvided = false;
            const selectionGroupsOptions: Array<{
                id: string;
                isSelectAll?: boolean;
                selectionGroups: Components['schemas']['PriceRuleWithId']['productSelectionCriteria'];
                selectionGroupsOperator: Components['schemas']['PriceRuleWithId']['productSelectionCriteriaOperator'];
            }> = [
                {
                    id: 'selectionId1',
                    isSelectAll: true,
                    selectionGroups: [],
                    selectionGroupsOperator: 'and',
                },
            ];

            const actualPackageData = await ProductSelectionService.match(
                accountId,
                products,
                selectionGroupsOptions,
                useProductsAsProvided,
            );

            const request = Moxios.requests.mostRecent();
            const requestBody = JSON.parse(request.config.data);
            expect(returnedMatches).toEqual(actualPackageData);
            expect(requestBody).toEqual({
                accountId,
                products,
                selectionGroupsOptions,
                useProductsAsProvided,
            });
        });
    });
});
