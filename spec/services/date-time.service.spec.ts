import {createMongoServiceSpy, getCollection, registerCollection} from '@wondersign/serverless-services';
import * as _ from 'lodash';
import {DateTimeService} from '../../src/services/date-time.service';

describe('DateTimeService', () => {

    let timezoneCollection;
    const timezoneCollectionName = 'timezone';
    beforeEach(async () => {
        const mongoService = createMongoServiceSpy();

        await registerCollection(timezoneCollectionName);
        timezoneCollection = getCollection(timezoneCollectionName);
        await new Promise((resolve) => timezoneCollection.deleteMany({}, resolve));

        mongoService.getCollection.and.callFake(
            (collectionName) => Promise.resolve(getCollection(collectionName)),
        );
    });

    describe('convertRelativeTimestampToDate', () => {

        it('should convert a relative time to a UTC Date', () => {
            const timestamp = '2021-01-01 01:30';
            const timezone = 'Etc/GMT+1';

            const expectedUtcDate = new Date('2021-01-01T02:30:00Z');
            const utcDate = DateTimeService.convertRelativeTimestampToDate(timestamp, timezone);
            expect(utcDate).toEqual(expectedUtcDate);
        });

    });

    describe('get24HourWithMeridiem', () => {

        it('should return the proper hour for am meridiem', () => {
            const baseHour = '11';
            const meridiem = 'am';
            expect(DateTimeService.get24HourWithMeridiem(baseHour, meridiem)).toEqual('11');
        });

        it('should return the proper hour for pm meridiem', () => {
            const baseHour = '11';
            const meridiem = 'pm';
            expect(DateTimeService.get24HourWithMeridiem(baseHour, meridiem)).toEqual('23');
        });

        it('should return the proper hour for 12am', () => {
            const baseHour = '12';
            const meridiem = 'am';
            expect(DateTimeService.get24HourWithMeridiem(baseHour, meridiem)).toEqual('00');
        });

        it('should return the proper hour for 12pm', () => {
            const baseHour = '12';
            const meridiem = 'pm';
            expect(DateTimeService.get24HourWithMeridiem(baseHour, meridiem)).toEqual('12');
        });
    });


    describe('isRelativeTimeMatchForUtcTimestamp', () => {
        it('should return true when there is a match between the relative and utc time', () => {
            const relativeDate = '2021-01-01';
            const relativeHour = '01';
            const relativeMinute = '30';
            const timezone = 'Etc/GMT+1';
            const utcTimestamp = '2021-01-01T02:30:00Z';

            expect(DateTimeService.isRelativeTimeMatchForUtcTimestamp(
                relativeDate,
                relativeHour,
                relativeMinute,
                timezone,
                utcTimestamp,
            )).toBe(true);
        });

        it('should return false when there is a mismatch between the relative and utc time', () => {
            const relativeDate = '2021-01-01';
            const relativeHour = '01';
            const relativeMinute = '30';
            const timezone = 'Etc/GMT+1';
            const utcTimestamp = '2021-01-01T05:30:00Z';

            expect(DateTimeService.isRelativeTimeMatchForUtcTimestamp(
                relativeDate,
                relativeHour,
                relativeMinute,
                timezone,
                utcTimestamp,
            )).toBe(false);
        });
    });

    describe('trackTimezoneForAccountId', () => {
        it('should insert a record into the timezone collection', async () => {
            const accountId = 'accountId1';
            const timezone = 'Etc/GMT+1';
            await DateTimeService.trackTimezoneForAccountId(timezone, accountId);
            const timezoneDoc = await timezoneCollection.findOne({accountId, timezone});

            expect(_.omit(timezoneDoc, ['_id'])).toEqual({
                accountId,
                lastRequestedAt: jasmine.any(Object),
                offset: '-01:00',
                timezone,
            });
        });
    });
});
