import * as _ from 'lodash';
import {createMongoServiceSpy, getCollection, registerCollection} from '@wondersign/serverless-services';
import {ObjectId} from 'mongodb';
import {createPriceRuleServiceSpy} from '../mocks/price-rule.service.mock';
import {RentalTermService} from '../../src/services/rental-term.service';
import {createProductPriceServiceSpy} from '../mocks/product-price.service.mock';
import {RentalTerm} from '../../src/models/rental-term';
import {getRentalTerm} from '../mocks/rental-term.mock';
import {createPriceElementChangeServiceSpy} from '../mocks/price-element-change.service.mock';

describe('RentalTermService', () => {

    let accountId: string;
    const rentalTermCollectionName = 'rentalTerm';
    let mongoService;
    let priceRuleService;
    let productPriceService;
    let rentalTermCollection;
    let rentalTermService: RentalTermService;
    let rentalTermFindOneAndUpdate;
    beforeEach(async () => {
        accountId = 'accountId1';
        mongoService = createMongoServiceSpy();
        createPriceElementChangeServiceSpy();
        priceRuleService = createPriceRuleServiceSpy();
        productPriceService = createProductPriceServiceSpy();

        // Register the collection and ensure it's empty for each run
        await registerCollection(rentalTermCollectionName);
        rentalTermCollection = getCollection(rentalTermCollectionName);
        await new Promise((resolve) => rentalTermCollection.deleteMany({}, resolve));

        mongoService.getCollection.and.callFake(
            (collectionName) => Promise.resolve(getCollection(collectionName)),
        );

        rentalTermService = new RentalTermService();

        // We're keeping the original implementation of findOneAndUpdate
        // for rental term collection before each test.
        rentalTermFindOneAndUpdate = rentalTermCollection.findOneAndUpdate;
    });

    afterEach(() => {
        // After each test we're restoring the original implementation of findOneAndUpdate
        // for rental term collection to clear any spy created for that method
        rentalTermCollection.findOneAndUpdate = rentalTermFindOneAndUpdate;
    });

    it('should exist', () => {
        expect(rentalTermService).toBeTruthy();
    });

    describe('deleteRentalTermsWithAccountId', () => {
        it('should delete rental terms from the collection', async () => {
            const deleteManySpy = spyOn(rentalTermCollection, 'deleteMany');
            await RentalTermService.deleteRentalTermsWithAccountId(accountId);
            expect(deleteManySpy).toHaveBeenCalledWith({accountId});
        });
    });

    describe('deleteRentalTermWithId', () => {

        it('should delete the rental term from the collection', async () => {
            const previousRentalTermId = (new ObjectId()).toString();

            rentalTermCollection.findOneAndDelete = jasmine.createSpy('rentalTermCollection.findOneAndDelete');

            let findOneAndDeleteFilter;
            rentalTermCollection.findOneAndDelete.and.callFake((filter) => {
                findOneAndDeleteFilter = filter;
                return Promise.resolve({
                    value: {
                        accountId,
                    },
                });
            });

            expect(await rentalTermService.deleteRentalTermWithId(previousRentalTermId)).toBeUndefined();
            expect(findOneAndDeleteFilter).toEqual({
                _id: new ObjectId(previousRentalTermId),
            });
        });

        it('should delete the rental term from product prices', async () => {
            const previousRentalTermId = (new ObjectId()).toString();

            rentalTermCollection.findOneAndDelete = jasmine.createSpy('rentalTermCollection.findOneAndDelete');
            rentalTermCollection.findOneAndDelete.and.returnValue(Promise.resolve({
                value: {
                    accountId,
                },
            }));

            expect(await rentalTermService.deleteRentalTermWithId(previousRentalTermId)).toBeUndefined();
            expect(productPriceService.unsetRentalTermIdForAccountId)
                .toHaveBeenCalledWith(previousRentalTermId, accountId);
        });

        it('should delete the rental term from price rules', async () => {
            const previousRentalTermId = (new ObjectId()).toString();

            rentalTermCollection.findOneAndDelete = jasmine.createSpy('rentalTermCollection.findOneAndDelete');
            rentalTermCollection.findOneAndDelete.and.returnValue(Promise.resolve({
                value: {
                    accountId,
                },
            }));

            expect(await rentalTermService.deleteRentalTermWithId(previousRentalTermId)).toBeUndefined();
            expect(priceRuleService.unsetRentalTermIdForAccountId)
                .toHaveBeenCalledWith(previousRentalTermId, accountId);
        });

        it('should call the findOneAndDelete method with expected values', async () => {
            const previousRentalTermId = (new ObjectId()).toString();

            rentalTermCollection.findOneAndDelete = jasmine.createSpy('rentalTermCollection.findOneAndDelete');
            rentalTermCollection.findOneAndDelete.and.returnValue(Promise.resolve({
                value: {
                    accountId,
                },
            }));

            await rentalTermService.deleteRentalTermWithId(previousRentalTermId);
            const findOneAndUpdateArgs = rentalTermCollection.findOneAndDelete.calls.first().args;
            expect(findOneAndUpdateArgs[0]).toEqual({_id: new ObjectId(previousRentalTermId)});
            expect(findOneAndUpdateArgs[1]).toEqual({
                includeResultMetadata: true,
            });
        });

    });

    describe('queryRentalTerm', () => {

        it('should return rental terms that match an accountId', async () => {
            const rentalTerm = getRentalTerm({
                accountId,
                installments: 12,
                interval: 'monthly',
                name: 'Test',
            });
            const insertOneResult = await rentalTermCollection.insertOne(_.cloneDeep(rentalTerm));
            rentalTerm.id = insertOneResult.insertedId.toString();

            const rentalTerms = await rentalTermService.queryRentalTerm({
                accountId,
            });
            expect(_.size(rentalTerms)).toEqual(1);
            expect(rentalTerms[0]).toEqual(rentalTerm);
        });

        it('should return rental terms that match ids', async () => {
            const rentalTerm = getRentalTerm({
                accountId,
                installments: 12,
                interval: 'monthly',
                name: 'Test',
            });
            const insertOneResult = await rentalTermCollection.insertOne(_.cloneDeep(rentalTerm));
            rentalTerm.id = insertOneResult.insertedId.toString();

            const rentalTerms = await rentalTermService.queryRentalTerm({
                ids: [rentalTerm.id],
            });
            expect(_.size(rentalTerms)).toEqual(1);
            expect(rentalTerms[0]).toEqual(rentalTerm);
        });

        it('should return rental terms sorted by order', async () => {
            const rentalTermCollectionFindToArray = jasmine.createSpy('rentalTermCollection.find.toArray');
            let findQuery;
            let findOptions;

            mongoService.getCollection.and.callFake(() => ({
                find: (query, options) => {
                    findQuery = query;
                    findOptions = options;
                    return {
                        toArray: rentalTermCollectionFindToArray,
                    };
                },
            }));

            const rentalTerm1 = getRentalTerm({
                _id: new ObjectId(),
                name: 'First',
            });
            const rentalTerm2 = getRentalTerm({
                _id: new ObjectId(),
                name: 'Second',
            });
            rentalTermCollectionFindToArray.and.returnValue(Promise.resolve([
                rentalTerm1,
                rentalTerm2,
            ]));

            const expectedRentalTerm1 = _.cloneDeep(rentalTerm1);
            // eslint-disable-next-line no-underscore-dangle
            expectedRentalTerm1.id = expectedRentalTerm1._id.toString();
            // eslint-disable-next-line no-underscore-dangle
            delete expectedRentalTerm1._id;

            const expectedRentalTerm2 = _.cloneDeep(rentalTerm2);
            // eslint-disable-next-line no-underscore-dangle
            expectedRentalTerm2.id = expectedRentalTerm2._id.toString();
            // eslint-disable-next-line no-underscore-dangle
            delete expectedRentalTerm2._id;

            const rentalTerms = await rentalTermService.queryRentalTerm({
                accountId,
            });
            expect(rentalTerms).toEqual([
                expectedRentalTerm1,
                expectedRentalTerm2,
            ]);
            expect(findQuery).toEqual({
                accountId,
            });
            expect(_.pick(findOptions, ['sort'])).toEqual({
                sort: {
                    order: 1,
                },
            });

        });

    });

    describe('saveRentalTerm', () => {

        let rentalTerm: RentalTerm;

        it('should save a new valid rental term', async () => {
            rentalTerm = getRentalTerm({
                accountId,
                installments: 12,
                interval: 'monthly',
                name: 'Test',
            });

            expect(await rentalTermService.saveRentalTerm(rentalTerm))
                .toEqual({id: jasmine.any(String) as unknown as string});
            const existingRentalTerm = await rentalTermCollection.findOne({});
            expect(_.pick(existingRentalTerm, _.keys(rentalTerm))).toEqual(rentalTerm);
        });

        it('should save an valid update to an existing rental term', async () => {
            rentalTerm = getRentalTerm({
                accountId,
                installments: 12,
                interval: 'monthly',
                name: 'Test',
            });
            const insertOneResult = await rentalTermCollection.insertOne(_.cloneDeep(rentalTerm));
            rentalTerm.id = insertOneResult.insertedId.toString();

            const updatedRentalTerm = _.cloneDeep(rentalTerm);
            updatedRentalTerm.name = 'Updated';
            updatedRentalTerm.installments = 52;
            updatedRentalTerm.interval = 'weekly';

            expect(await rentalTermService.saveRentalTerm(updatedRentalTerm))
                .toEqual({id: jasmine.any(String) as unknown as string});
            const existingRentalTerm = await rentalTermCollection.findOne({
                _id: new ObjectId(rentalTerm.id),
            });
            expect(_.pick(existingRentalTerm, _.keys(updatedRentalTerm))).toEqual(updatedRentalTerm);
        });

        it('should save a new valid rental term with order 0', async () => {
            rentalTerm = getRentalTerm({
                accountId,
                installments: 12,
                interval: 'monthly',
                name: 'Test',
            });

            expect(await rentalTermService.saveRentalTerm(rentalTerm))
                .toEqual({id: jasmine.any(String) as unknown as string});
            const existingRentalTerm = await rentalTermCollection.findOne({});
            expect(existingRentalTerm.order).toEqual(0);
        });

        it('should increment order values for existing rental terms when saving a new one', async () => {
            rentalTerm = getRentalTerm({
                accountId,
                installments: 12,
                interval: 'monthly',
                name: 'Previous',
                order: 0,
            });
            const insertOneResult = await rentalTermCollection.insertOne(_.cloneDeep(rentalTerm));
            const previousRentalTermId = insertOneResult.insertedId;

            rentalTerm = getRentalTerm({
                accountId,
                installments: 12,
                interval: 'monthly',
                name: 'Test',
            });

            expect(await rentalTermService.saveRentalTerm(rentalTerm))
                .toEqual({id: jasmine.any(String) as unknown as string});
            const existingRentalTerm = await rentalTermCollection.findOne({
                _id: previousRentalTermId,
            });
            expect(existingRentalTerm.order).toEqual(1);
        });

        it('should call the findOneAndUpdate method with expected values', async () => {
            rentalTerm = getRentalTerm({
                accountId,
                installments: 12,
                interval: 'monthly',
                name: 'Previous',
                order: 0,
            });
            const insertOneResult = await rentalTermCollection.insertOne(_.cloneDeep(rentalTerm));
            const rentalTermId = insertOneResult.insertedId;

            rentalTerm.id = rentalTermId.toString();

            rentalTermCollection.findOneAndUpdate = jasmine.createSpy('rentalTermCollection.findOneAndUpdate');
            rentalTermCollection.findOneAndUpdate.and.returnValue(Promise.resolve({
                value: {
                    ...rentalTerm,
                },
            }));
            const result = await rentalTermService.saveRentalTerm(rentalTerm);

            const findOneAndUpdateArgs = rentalTermCollection.findOneAndUpdate.calls.first().args;
            expect(findOneAndUpdateArgs[0]).toEqual({_id: new ObjectId(result.id)});
            expect(findOneAndUpdateArgs[1]).toEqual({
                $set: rentalTerm,
            });
            expect(findOneAndUpdateArgs[2]).toEqual({
                includeResultMetadata: true,
                projection: {
                    _id: false,
                },
                returnDocument: 'after',
                writeConcern: {
                    j: true,
                    w: 'majority',
                },
            });
        });
    });

    describe('saveRentalTermIdsOrder', () => {

        it('should save the order of provided rentalTermIds', async () => {
            const rentalTerm1Id = (new ObjectId()).toString();
            const rentalTerm2Id = (new ObjectId()).toString();

            rentalTermCollection.bulkWrite = jasmine.createSpy('rentalTermCollection.bulkWrite');

            let bulkWriteOperations;
            rentalTermCollection.bulkWrite.and.callFake((operations) => {
                bulkWriteOperations = operations;
                return Promise.resolve();
            });

            expect(await rentalTermService.saveRentalTermIdsOrder([
                rentalTerm1Id,
                rentalTerm2Id,
            ])).toBeUndefined();
            expect(bulkWriteOperations).toEqual([
                {
                    updateOne: {
                        filter: {_id: new ObjectId(rentalTerm1Id)},
                        update: {
                            $set: {
                                order: 0,
                            },
                        },
                    },
                },
                {
                    updateOne: {
                        filter: {_id: new ObjectId(rentalTerm2Id)},
                        update: {
                            $set: {
                                order: 1,
                            },
                        },
                    },
                },
            ]);
        });

    });
});
