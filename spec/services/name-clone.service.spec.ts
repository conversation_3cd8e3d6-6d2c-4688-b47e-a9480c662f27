import {NameCloneService} from '../../src/services/name-clone.service';

describe('NameCloneService', () => {

    describe('getUniqueNameOptionallyCloned', () => {

        it('should not require clone name when there is no existing match', () => {
            const providedName = 'Existing One';
            const existingNames = [
                'Existing One - Clone 1',
            ];

            const cloneName = NameCloneService.getUniqueNameOptionallyCloned(providedName, existingNames);
            expect(cloneName).toEqual(providedName);
        });

        it('should generate a clone name when there is an existing non-clone match', () => {
            const providedName = 'New One';
            const existingNames = [
                providedName,
            ];

            const generateCloneNameSpy = spyOn(NameCloneService, 'generateCloneName');
            NameCloneService.getUniqueNameOptionallyCloned(providedName, existingNames);
            expect(generateCloneNameSpy).toHaveBeenCalledWith(providedName, existingNames);
        });

        it('should generate a clone name when there is an existing clone match', () => {
            const providedName = 'New One - Clone 1';
            const existingNames = [
                providedName,
            ];

            const generateCloneNameSpy = spyOn(NameCloneService, 'generateCloneName');
            NameCloneService.getUniqueNameOptionallyCloned(providedName, existingNames);
            expect(generateCloneNameSpy).toHaveBeenCalledWith(providedName, existingNames);
        });

    });

    describe('generateCloneName', () => {

        it('should append - clone 1 to the first cloned item', () => {
            const providedName = 'New One';
            const existingNames = [
                providedName,
            ];

            const cloneName = NameCloneService.generateCloneName(providedName, existingNames);
            expect(cloneName).toEqual(providedName + ' - Clone 1');
        });

        it('should append the next clone increment when there is an existing base name clone', () => {
            const providedName = 'New One';
            const existingNames = [
                providedName + ' - Clone 1',
            ];

            const cloneName = NameCloneService.generateCloneName(providedName, existingNames);
            expect(cloneName).toEqual(providedName + ' - Clone 2');
        });
    });
});
