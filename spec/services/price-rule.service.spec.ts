import * as _ from 'lodash';
import {createMongoServiceSpy, getCollection, registerCollection} from '@wondersign/serverless-services';
import {ObjectId} from 'mongodb';
import {createPriceListServiceSpy} from '../mocks/price-list.service.mock';
import {createPriceElementChangeServiceSpy} from '../mocks/price-element-change.service.mock';
import {PriceRuleService} from '../../src/services/price-rule.service';
import {getPriceRule} from '../mocks/price-rule.mock';
import {PriceRule} from '../../src/models/price-rule';
import {ObjectHelperService} from '../../src/services/object-helper.service';
import { createValidationServiceServiceSpy } from '../mocks/validation-service.mock';

describe('PriceRuleService', () => {

    let accountId;
    let mongoService;
    let priceElementChangeService;
    let priceRuleArchiveCollection;
    let priceRuleCollection;
    const priceRuleArchiveCollectionName = 'priceRuleArchive';
    const priceRuleCollectionName = 'priceRule';
    let priceListService;
    let priceRuleService: PriceRuleService;
    let validationService;
    beforeEach(async () => {
        accountId = 'accountId1';
        mongoService = createMongoServiceSpy();
        priceElementChangeService = createPriceElementChangeServiceSpy();
        priceListService = createPriceListServiceSpy();
        validationService = createValidationServiceServiceSpy();

        // Register the collection and ensure it's empty for each run
        await registerCollection(priceRuleArchiveCollectionName);
        await registerCollection(priceRuleCollectionName);
        priceRuleArchiveCollection = getCollection(priceRuleArchiveCollectionName);
        priceRuleCollection = getCollection(priceRuleCollectionName);
        await new Promise((resolve) => priceRuleArchiveCollection.deleteMany({}, resolve));
        await new Promise((resolve) => priceRuleCollection.deleteMany({}, resolve));

        mongoService.getCollection.and.callFake(
            (collectionName) => Promise.resolve(getCollection(collectionName)),
        );

        priceRuleService = new PriceRuleService();
    });

    it('should exists', () => {
        expect(priceRuleService).toBeTruthy();
    });

    describe('deletePriceRulesWithAccountId', () => {
        it('should delete price rules from the collection and archive', async () => {
            const priceRuleCollectionDeleteManySpy = spyOn(priceRuleCollection, 'deleteMany');
            const priceRuleArchiveCollectionDeleteManySpy = spyOn(priceRuleArchiveCollection, 'deleteMany');
            await PriceRuleService.deletePriceRulesWithAccountId(accountId);
            expect(priceRuleCollectionDeleteManySpy).toHaveBeenCalledWith({accountId});
            expect(priceRuleArchiveCollectionDeleteManySpy).toHaveBeenCalledWith({accountId});
        });
    });

    describe('isRecalculatingRequired', () => {

        let priceRuleBefore: PriceRule;
        beforeEach(() => {
            priceRuleBefore = getPriceRule({
                accountId,
                calculation: {
                    retail: {
                        basePriceDef: 'cost',
                    },
                },
                id: 'priceListId1',
            });
        });

        it('should be required when there has been a calculation change', () => {
            const priceRuleAfter = _.cloneDeep(priceRuleBefore);
            priceRuleAfter.calculation.retail.basePriceDef = 'msrp';

            expect(priceRuleService.isRecalculatingRequired(
                priceRuleBefore,
                priceRuleAfter,
            )).toBe(true);
        });

        it('should not be required when there has not been a calculation change', () => {
            const priceRuleAfter = _.cloneDeep(priceRuleBefore);
            priceRuleAfter.name = 'Update';

            expect(priceRuleService.isRecalculatingRequired(
                priceRuleBefore,
                priceRuleAfter,
            )).toBe(false);
        });

        it('should not be required when there is no updated value', () => {
            const priceRuleAfter = null;

            expect(priceRuleService.isRecalculatingRequired(
                priceRuleBefore,
                priceRuleAfter,
            )).toBe(false);
        });
    });

    describe('isRematchingRequired', () => {

        let priceRuleBefore: PriceRule;
        beforeEach(() => {
            priceRuleBefore = getPriceRule({
                accountId,
                enabled: true,
                id: 'priceListId1',
                productSelectionCriteria: [],
                productSelectionType: 'custom',
            });
        });

        it('should be required when there has been a change to enabled', () => {
            const priceRuleAfter = _.cloneDeep(priceRuleBefore);
            priceRuleAfter.enabled = false;

            expect(priceRuleService.isRematchingRequired(
                priceRuleBefore,
                priceRuleAfter,
            )).toBe(true);
        });

        it('should be required when there has been a change to product selection criteria', () => {
            const priceRuleAfter = _.cloneDeep(priceRuleBefore);
            priceRuleAfter.productSelectionCriteria.push(
                {
                    conditions: [
                        {
                            field: 'brand',
                            operator: 'in',
                            value: ['Ashley Furniture'],
                        },
                    ],
                    operation: 'and',
                },
            );

            expect(priceRuleService.isRematchingRequired(
                priceRuleBefore,
                priceRuleAfter,
            )).toBe(true);
        });

        it('should be required when there has been a change to product selection type', () => {
            const priceRuleAfter = _.cloneDeep(priceRuleBefore);
            priceRuleAfter.productSelectionType = 'all';

            expect(priceRuleService.isRematchingRequired(
                priceRuleBefore,
                priceRuleAfter,
            )).toBe(true);
        });

        it('should not be required when there has been a change to name', () => {
            const priceRuleAfter = _.cloneDeep(priceRuleBefore);
            priceRuleAfter.name = 'Updated';

            expect(priceRuleService.isRematchingRequired(
                priceRuleBefore,
                priceRuleAfter,
            )).toBe(false);
        });
    });

    describe('queryPriceRuleArchive', () => {

        it('should return all archived copies of price rules for a given priceList', async () => {
            const priceListId = 'priceListId1';
            const userEmail = '<EMAIL>';
            const priceRule1 = getPriceRule({
                accountId,
                calculation: {
                    retail: {
                        basePriceDef: 'cost',
                        expressions: [],
                        minIsMAP: false,
                    },
                },
                id: (new ObjectId()).toString(),
                name: 'Test 1',
                priceListId,
            });
            const priceRule2 = getPriceRule({
                accountId,
                calculation: {
                    retail: {
                        basePriceDef: 'cost',
                        expressions: [],
                        minIsMAP: false,
                    },
                },
                id: (new ObjectId()).toString(),
                name: 'Test 2',
                priceListId,
            });
            await priceRuleArchiveCollection.insertMany([
                {
                    accountId,
                    priceListId,
                    priceRule: priceRule1,
                    priceRuleId: priceRule1.id,
                    timestamp: new Date('2021-05-01T12:00:00Z'),
                    userEmail,
                },
                {
                    accountId,
                    priceListId,
                    priceRule: priceRule2,
                    priceRuleId: priceRule2.id,
                    timestamp: new Date('2021-05-02T12:00:00Z'),
                    userEmail,
                },
                {
                    accountId,
                    priceListId,
                    priceRule: _.extend({}, priceRule2, {name: 'Test 2a'}),
                    priceRuleId: priceRule2.id,
                    timestamp: new Date('2021-05-03T12:00:00Z'),
                    userEmail,
                },
            ]);

            const archivedPriceRules = await priceRuleService.queryPriceRuleArchive({priceListId});
            expect(archivedPriceRules.totalCount).toEqual(3);

            expect(_.map(archivedPriceRules.data, 'priceRule.name')).toEqual([
                'Test 2a',
                priceRule2.name,
                priceRule1.name,
            ]);
        });

    });

    describe('savePriceRule', () => {

        let isRecalculatingRequiredSpy;
        let isRematchingRequiredSpy;
        let priceListId: string;
        beforeEach(() => {
            priceListId = 'priceListId1';
            isRecalculatingRequiredSpy = spyOn(priceRuleService, 'isRecalculatingRequired');
            isRematchingRequiredSpy = spyOn(priceRuleService, 'isRematchingRequired');

            priceRuleCollection.findOneAndReplace = jasmine.createSpy('priceRuleCollection.findOneAndReplace');

            isRecalculatingRequiredSpy.and.returnValue(false);
            isRematchingRequiredSpy.and.returnValue(false);
        });

        const expectPriceRuleToSave = async (priceRule: PriceRule) => {
            const saveResponse = await priceRuleService.savePriceRule(priceRule);
            const priceRules = await priceRuleCollection.find({}).toArray() as PriceRule[];
            const savedPriceRule = _.first(priceRules);
            expect(savedPriceRule.name).toEqual(priceRule.name);
            expect(saveResponse).toEqual({
                // eslint-disable-next-line no-underscore-dangle
                id: savedPriceRule._id.toString(),
                name: savedPriceRule.name,
            });

            const archivedPriceRule = await priceRuleArchiveCollection.findOne({});
            expect(archivedPriceRule.priceRule).toEqual(priceRule);
        };

        it('should create a new price rule', async () => {
            const priceRule = _.omit(getPriceRule({
                accountId,
                calculation: {
                    retail: {
                        basePriceDef: 'cost',
                        expressions: [
                            {
                                expressionType: 'math',
                                operation: 'multiply',
                                value: 3.5,
                            },
                            {
                                decimalValue: '34',
                                direction: 'up',
                                expressionType: 'round',
                                integerValue: null,
                                maxAdjustment: {
                                    enabled: false,
                                    unit: 'finite',
                                    value: null,
                                },
                            },
                        ],
                        minIsMAP: false,
                    },
                },
                name: 'Test',
                priceListId,
                productSelectionType: 'all',
            }), '_id');

            await expectPriceRuleToSave(priceRule);
        });

        it('should prepend a new price rule ID to the top of the respective price list', async () => {
            const priceRule = getPriceRule({
                accountId,
                name: 'Test',
                priceListId,
            });

            await priceRuleService.savePriceRule(priceRule);
            const priceRules = await priceRuleCollection.find({}).toArray() as PriceRule[];
            const savedPriceRule = _.first(priceRules);
            expect(priceListService.prependPriceRuleIdForPriceList)
                // eslint-disable-next-line no-underscore-dangle
                .toHaveBeenCalledWith(savedPriceRule._id.toString(), priceListId);
        });

        it('should replace an existing price rule', async () => {
            const priceRule = _.omit(getPriceRule({
                accountId,
                name: 'Test',
                priceListId,
                productSelectionType: 'all',
            }), ['_id']);
            const priceRuleReplacement = _.cloneDeep(priceRule);
            const insertResult = await priceRuleCollection.insertOne(priceRule);
            const priceRuleId = insertResult.insertedId.toString();

            let savedPriceRule;
            priceRuleCollection.findOneAndReplace.and.callFake((filter, replacement) => {
                savedPriceRule = _.cloneDeep(replacement);
                return Promise.resolve({
                    value: _.cloneDeep(replacement),
                });
            });

            priceRuleReplacement.name = 'Updated';
            priceRuleReplacement.id = priceRuleId;
            expect(await priceRuleService.savePriceRule(priceRuleReplacement)).toEqual({
                id: priceRuleId,
                name: priceRuleReplacement.name,
            });

            expect(savedPriceRule.name).toEqual(priceRuleReplacement.name);

            const archivedPriceRule = await priceRuleArchiveCollection.findOne({});
            expect(archivedPriceRule.priceRule).toEqual(ObjectHelperService.removeNil(priceRuleReplacement));
        });

        it('should partially update an existing price rule', async () => {
            const priceRule = getPriceRule({
                accountId,
                name: 'Test',
                priceListId,
            });
            const insertResult = await priceRuleCollection.insertOne(priceRule);
            const priceRuleId = insertResult.insertedId.toString();

            let savedPriceRule;
            priceRuleCollection.findOneAndReplace.and.callFake((filter, replacement) => {
                savedPriceRule = _.cloneDeep(replacement);
                return Promise.resolve({
                    value: _.cloneDeep(replacement),
                });
            });

            const priceRuleReplacement = {
                id: priceRuleId,
                name: 'Updated',
            };
            expect(await priceRuleService.savePriceRule(priceRuleReplacement, true)).toEqual({
                id: priceRuleId,
                name: priceRuleReplacement.name,
            });
            expect(savedPriceRule.name).toEqual(priceRuleReplacement.name);
        });

        it('should queue a price element change if rematching is required', async () => {
            let priceRule = getPriceRule({
                accountId,
                enabled: true,
                name: 'Test',
                priceListId,
            });
            // eslint-disable-next-line no-underscore-dangle
            delete priceRule._id;
            const priceRuleReplacement = _.cloneDeep(priceRule);
            priceRule = ObjectHelperService.removeNil(priceRule);
            const insertResult = await priceRuleCollection.insertOne(priceRule);
            const priceRuleId = insertResult.insertedId.toString();
            priceRule.id = priceRuleId;

            priceRuleCollection.findOneAndReplace.and.callFake((filter, replacement) => Promise.resolve({
                value: _.cloneDeep(replacement),
            }));

            const priceElementChangeJobId = 'priceElementChangeJobId1';
            let actualPriceElementChangeJob;
            priceElementChangeService.queuePriceElementChangeJob.and.callFake((priceElementJob) => {
                actualPriceElementChangeJob = priceElementJob;

                // There is a bug in MongoMock that does not support removing _id via projection from findOneAndUpdate
                // eslint-disable-next-line no-underscore-dangle
                delete actualPriceElementChangeJob.elementAfterChange._id;

                return Promise.resolve(priceElementChangeJobId);
            });

            isRematchingRequiredSpy.and.returnValue(true);

            priceRuleReplacement.enabled = false;
            priceRuleReplacement.id = priceRuleId;
            expect(await priceRuleService.savePriceRule(priceRuleReplacement)).toEqual({
                id: priceRuleId,
                name: priceRuleReplacement.name,
                priceElementChangeJobId,
            });
            const elementAfterChange = ObjectHelperService.removeNil({...priceRuleReplacement, id: priceRuleId});

            expect(actualPriceElementChangeJob).toEqual({
                accountId,
                elementAfterChange,
                elementBeforeChange: priceRule,
                elementId: priceRuleId,
                elementType: 'priceRule',
            });
        });

        it('should queue a price element change if recalculating is required', async () => {
            const priceRule = getPriceRule({
                accountId,
                calculation: {
                    retail: {
                        basePriceDef: 'cost',
                    },
                },
                name: 'Test',
                priceListId,
            });
            // eslint-disable-next-line no-underscore-dangle
            delete priceRule._id;
            const priceRuleReplacement = _.cloneDeep(priceRule);
            const insertResult = await priceRuleCollection.insertOne(priceRule);
            const priceRuleId = insertResult.insertedId.toString();
            priceRule.id = priceRuleId;

            priceRuleCollection.findOneAndReplace.and.callFake((filter, replacement) => Promise.resolve({
                value: _.cloneDeep(replacement),
            }));

            const priceElementChangeJobId = 'priceElementChangeJobId1';
            let actualPriceElementChangeJob;
            priceElementChangeService.queuePriceElementChangeJob.and.callFake((priceElementJob) => {
                actualPriceElementChangeJob = priceElementJob;

                // There is a bug in MongoMock that does not support removing _id via projection from findOneAndUpdate
                // eslint-disable-next-line no-underscore-dangle
                delete actualPriceElementChangeJob.elementAfterChange._id;

                return Promise.resolve(priceElementChangeJobId);
            });

            isRecalculatingRequiredSpy.and.returnValue(true);

            priceRuleReplacement.calculation.retail.basePriceDef = 'msrp';
            priceRuleReplacement.id = priceRuleId;
            expect(await priceRuleService.savePriceRule(priceRuleReplacement)).toEqual({
                id: priceRuleId,
                name: priceRuleReplacement.name,
                priceElementChangeJobId,
            });
            const elementAfterChange = ObjectHelperService.removeNil({...priceRuleReplacement, id: priceRuleId});

            expect(actualPriceElementChangeJob).toEqual({
                accountId,
                elementAfterChange,
                elementBeforeChange: priceRule,
                elementId: priceRuleId,
                elementType: 'priceRule',
            });
        });

        it('should create a new price rule with targeting by basePrice', async () => {
            const priceRule = _.omit(getPriceRule({
                accountId,
                calculation: {
                    retail: {
                        basePriceDef: 'cost',
                        expressions: [
                            {
                                expressionType: 'math',
                                operation: 'multiply',
                                value: 3.5,
                            },
                        ],
                        minIsMAP: false,
                    },
                },
                name: 'Test',
                priceListId,
                productSelectionCriteria: [
                    {
                        conditions: [
                            {
                                field: 'basePrice',
                                operator: '>',
                                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                                // @ts-ignore
                                value: 200,
                            },
                        ],
                        operation: 'and',
                    },
                ],
                productSelectionType: 'custom',
            }), '_id');
            await expectPriceRuleToSave(priceRule);
        });

        it('should call findOneAndReplace method with expected values', async () => {
            const priceRule = getPriceRule({
                accountId,
                name: 'Test',
                priceListId,
            });

            const insertResult = await priceRuleCollection.insertOne(priceRule);
            const priceRuleId = insertResult.insertedId.toString();

            const insertedPriceRule = await priceRuleCollection.findOne({ _id: insertResult.insertedId });

            delete insertedPriceRule._id;
            delete insertedPriceRule.id;
            delete insertedPriceRule.productSelectionType;
            insertedPriceRule.calculation = { rental: {} };

            priceRuleCollection.findOneAndReplace.and.callFake((filter, replacement) => Promise.resolve({
                value: _.cloneDeep(replacement),
            }));

            const priceRuleReplacement = {
                id: priceRuleId,
                name: 'Updated',
            };
            await priceRuleService.savePriceRule(priceRuleReplacement, true);
            delete priceRuleReplacement.id;

            const findOneAndReplaceArgs = priceRuleCollection.findOneAndReplace.calls.first().args;
            expect(findOneAndReplaceArgs[0]).toEqual({_id: new ObjectId(priceRuleId)});
            expect(findOneAndReplaceArgs[1]).toEqual({
                ...insertedPriceRule,
                ...priceRuleReplacement,
            });
            expect(findOneAndReplaceArgs[2]).toEqual({
                includeResultMetadata: true,
                projection: {
                    _id: 0,
                },
                returnDocument: 'after',
            });
        });

        it('should throw an Http Error when validation fails', async () => {
            const priceRule = getPriceRule({
                accountId,
                calculation: {
                    retail: {
                        basePriceDef: 'cost',
                    },
                },
                name: 'Test',
                priceListId,
            });

            validationService.validate.and.callFake(() => Promise.resolve({
                errors: [{ dataPath: '.', message: 'fails' }],
                isValid: false,
            }));
            try {
                await priceRuleService.savePriceRule(priceRule);
            } catch (error) {
                expect(error.message).toEqual('priceRule . fails');
                expect(error.statusCode).toEqual(422);
            }
        });

    });

    describe('updateBrandFromProductSelectionCriteria', () => {
        it('should update brand value for document where brand is a productSelectionCriteria', async () => {
            const updateManySpy = spyOn(priceRuleCollection, 'updateMany');
            const brandCurrent = 'myBrand';
            const priceListId = 'testPriceListId1';

            const priceRule = _.omit(getPriceRule({
                priceListId,
                productSelectionCriteria: [{
                    conditions: [{
                        field: 'brand',
                        operator: '=',
                        value: [brandCurrent],
                    }],
                }],
            }), ['_id']);
            await priceRuleCollection.insertOne(priceRule);

            const brandNew = 'newBrand';
            const logPrefix = 'testLog';

            updateManySpy.and.returnValue({ matchedCount: 1, modifiedCount: 1 });

            await priceRuleService.updateBrandFromProductSelectionCriteria(brandCurrent, brandNew, logPrefix);

            expect(updateManySpy).toHaveBeenCalledTimes(1);
        });
    });

});
