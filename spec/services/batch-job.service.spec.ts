import {createMongoServiceSpy} from '@wondersign/serverless-services';
import {ObjectId} from 'mongodb';
import * as _ from 'lodash';
import {BatchJob} from '../../src/models/batch-job';
import {BatchJobService} from '../../src/services/batch-job.service';

describe('BatchJobService', () => {

    let batchJobCollection;
    let batchJobService: BatchJobService;
    let mongoService;
    beforeEach(() => {
        batchJobCollection = {
            deleteOne: jasmine.createSpy('batchJobCollection.deleteOne'),
            findOneAndUpdate: jasmine.createSpy('batchJobCollection.findOneAndUpdate'),
        };
        mongoService = createMongoServiceSpy();
        mongoService.getCollection.and.returnValue(Promise.resolve(batchJobCollection));

        batchJobService = new BatchJobService();
    });

    describe('receiveRequest', () => {

        it('should initialize or update a batch job in Mongo', async () => {
            const consumerId = 'consumerId1';
            const jobId = 'jobId1';
            const batchJob: BatchJob = {
                consumerId,
                jobId,
                requestTotalCount: 1,
            };

            const batchJobOid = new ObjectId();
            batchJobCollection.findOneAndUpdate.and.returnValue(Promise.resolve({
                ok: true,
                value: _.extend({}, batchJob, {_id: batchJobOid}),
            }));

            const response = await BatchJobService.receiveRequest(batchJob);
            const findOneAndUpdateArgs = batchJobCollection.findOneAndUpdate.calls.first().args;
            expect(findOneAndUpdateArgs[0]).toEqual({consumerId, jobId});
            expect(findOneAndUpdateArgs[1]).toEqual({
                $inc: {
                    requestReceivedCount: 1,
                },
                $set: {
                    updatedAt: jasmine.any(Date),
                },
                $setOnInsert: {
                    ...batchJob,
                    createdAt: jasmine.any(Date),
                },
            });
            expect(findOneAndUpdateArgs[2]).toEqual({
                includeResultMetadata: true,
                returnDocument: 'after',
                upsert: true,
            });
            expect(response).toEqual(_.extend({}, batchJob, {id: batchJobOid.toString()}));

        });
    });

    describe('updateProcessedRequest', () => {

        let batchJob: BatchJob;
        let batchJobId: string;
        beforeEach(() => {
            const batchJobOid = new ObjectId();
            batchJobId = batchJobOid.toString();
            batchJob = {
                _id: batchJobOid,
                consumerId: 'consumerId1',
                createdAt: new Date(),
                jobId: 'jobId1',
                requestProcessedCount: 0,
                requestReceivedCount: 0,
                requestTotalCount: 0,
                updatedAt: new Date(),
            };

            batchJobCollection.findOneAndUpdate.and.returnValue(Promise.resolve({
                ok: true,
                value: batchJob,
            }));
        });

        it('should update an existing request', async () => {
            await batchJobService.updateProcessedRequest(batchJobId);
            const findOneAndUpdateArgs = batchJobCollection.findOneAndUpdate.calls.first().args;
            expect(findOneAndUpdateArgs[0]).toEqual({_id: new ObjectId(batchJobId)});
            expect(findOneAndUpdateArgs[1]).toEqual({
                $inc: {
                    requestProcessedCount: 1,
                },
                $set: {
                    updatedAt: jasmine.any(Date),
                },
            });
            expect(findOneAndUpdateArgs[2]).toEqual({
                includeResultMetadata: true,
                returnDocument: 'after',
            });
        });

        it('should call the completion handler on the last request if set', async () => {
            batchJob.requestReceivedCount = 1;
            batchJob.requestTotalCount = 1;
            batchJob.requestProcessedCount = 1;

            let completionHandlerCalled = false;
            batchJobService.setCompletionHandler(() => {
                completionHandlerCalled = true;
                return Promise.resolve();
            });
            await batchJobService.updateProcessedRequest(batchJobId);
            expect(completionHandlerCalled).toEqual(true);
        });

        it('should not call the completion handler before the last request', async () => {
            batchJob.requestReceivedCount = 1;
            batchJob.requestTotalCount = 2;
            batchJob.requestProcessedCount = 1;

            let completionHandlerCalled = false;
            batchJobService.setCompletionHandler(() => {
                completionHandlerCalled = true;
                return Promise.resolve();
            });
            await batchJobService.updateProcessedRequest(batchJobId);
            expect(completionHandlerCalled).toEqual(false);
        });

        it('should delete the entry if this is the last request', async () => {
            batchJob.requestReceivedCount = 1;
            batchJob.requestTotalCount = 1;
            batchJob.requestProcessedCount = 1;

            await batchJobService.updateProcessedRequest(batchJobId);
            expect(batchJobCollection.deleteOne).toHaveBeenCalledWith({_id: new ObjectId(batchJobId)});
        });
    });
});
