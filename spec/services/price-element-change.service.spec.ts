import {
    createAwsSnsServiceSpy, createAwsSqsServiceSpy,
    createMongoServiceSpy,
    getCollection,
    registerCollection,
} from '@wondersign/serverless-services';
import {ObjectId} from 'mongodb';
import {PriceElementChangeJob} from '../../src/models/price-element-change-job';
import {PriceElementChangeService} from '../../src/services/price-element-change.service';
import config from '../helpers/env';

describe('PriceElementChangeService', () => {

    const accountId = 'accountId1';
    let awsSqsService;
    let mongoService;
    let priceElementChangeJobCollection;
    const priceElementChangeJobCollectionName = 'priceElementChangeJob';
    let queueLockCollection;
    const queueLockCollectionName = 'priceElementChangeQueueLock';

    beforeEach(async () => {
        awsSqsService = createAwsSqsServiceSpy();
        mongoService = createMongoServiceSpy();

        await registerCollection(priceElementChangeJobCollectionName);
        priceElementChangeJobCollection = getCollection(priceElementChangeJobCollectionName);
        await new Promise((resolve) => priceElementChangeJobCollection.deleteMany({}, resolve));

        await registerCollection(queueLockCollectionName);
        queueLockCollection = getCollection(queueLockCollectionName);
        await new Promise((resolve) => queueLockCollection.deleteMany({}, resolve));

        mongoService.getCollection.and.callFake((collectionName) => Promise.resolve(getCollection(collectionName)));
    });

    describe('deletePriceElementChangeJobsWithAccountId', () => {
        it('should delete price element change jobs from the collection', async () => {
            const deleteManySpy = spyOn(priceElementChangeJobCollection, 'deleteMany');
            await PriceElementChangeService.deletePriceElementChangeJobsWithAccountId(accountId);
            expect(deleteManySpy).toHaveBeenCalledWith({accountId});
        });
    });

    describe('getPriceElementChangesForAccountId', () => {

        it('should override id with batchJobId if set', async () => {
            const batchJobId = 'batchJobId1';
            const jobBatch: PriceElementChangeJob = {
                accountId,
                batchJobId,
                createdAt: (new Date()).toISOString(),
                elementType: 'productEventBatch',
            };

            const jobRegular: PriceElementChangeJob = {
                accountId,
                createdAt: (new Date()).toISOString(),
                elementId: (new ObjectId()).toString(),
                elementType: 'priceRule',
            };

            const insertResults = await priceElementChangeJobCollection.insertMany([
                jobBatch,
                jobRegular,
            ]);

            const jobRegularOid = insertResults.insertedIds[1];
            const priceElementChanges = await PriceElementChangeService.getPriceElementChangesForAccountId(accountId);
            expect(priceElementChanges[0].id).toEqual(batchJobId);
            expect(priceElementChanges[1].id).toEqual(jobRegularOid.toString());
        });
    });

    describe('hasExistingJobForAccount', () => {
        it('should return true when there is an existing job for the account', async () => {
            await priceElementChangeJobCollection.insertOne({
                accountId,
            });

            expect(await PriceElementChangeService.hasExistingJobForAccount(accountId)).toBe(true);
        });

        it('should return false when there is not an existing job for the account', async () => {
            expect(await PriceElementChangeService.hasExistingJobForAccount(accountId)).toBe(false);
        });
    });

    describe('publishPriceElementChangeJobCompletion', () => {

        let awsSnsService;
        const batchJobId = 'batchJobId1';
        const jobId = 'jobId1';
        let job: PriceElementChangeJob;
        beforeEach(() => {
            job = {
                accountId,
                id: jobId,
            };

            awsSnsService = createAwsSnsServiceSpy();
        });

        it('should use the batchJobId as the id if set', async () => {
            job.batchJobId = batchJobId;

            await PriceElementChangeService.publishPriceElementChangeJobCompletion(job);
            expect(awsSnsService.sendSnsMessage).toHaveBeenCalledWith({
                accountId,
                id: batchJobId,
            }, config.priceElementChangeJobTopic, 'Price Element Change Job Completion', process.env.awsAccountId);
        });

        it('should use the id if batchJobId is not set', async () => {
            await PriceElementChangeService.publishPriceElementChangeJobCompletion(job);
            expect(awsSnsService.sendSnsMessage).toHaveBeenCalledWith({
                accountId,
                id: job.id,
            }, config.priceElementChangeJobTopic, 'Price Element Change Job Completion', process.env.awsAccountId);
        });
    });

    describe('queueAccountId', () => {
        const queueUrl = 'https://aws.sqs/queue';

        beforeEach(() => {
            awsSqsService.buildQueueUrlFromConfig.and.returnValue(queueUrl);
        });

        it('should queue a job if it can acquire queue lock', async () => {
            await PriceElementChangeService.queueAccountId(accountId);

            expect(awsSqsService.sendDataToQueue).toHaveBeenCalledWith(queueUrl, [{accountId}], 1);
        });

        it('should not queue a job if it cannot acquire queue lock', async () => {
            const insertOneSpy = spyOn(queueLockCollection, 'insertOne');
            insertOneSpy.and.throwError('Duplicate value for unique key');

            await PriceElementChangeService.queueAccountId(accountId);

            expect(insertOneSpy).toHaveBeenCalledWith({
                accountId,
                lockedAt: jasmine.any(Date),
            });
            expect(awsSqsService.sendDataToQueue).not.toHaveBeenCalled();
        });
    });

    describe('queuePriceElementChangeJob', () => {

        const batchJobId = 'batchJobId1';
        let job: PriceElementChangeJob;
        beforeEach(() => {
            job = {
                accountId,
            };
        });

        it('should return the batchJobId if set', async () => {
            job.batchJobId = batchJobId;
            expect(await PriceElementChangeService.queuePriceElementChangeJob(job)).toEqual(batchJobId);
        });

        it('should return the newly created ID if batchJobId is not set', async () => {
            expect(await PriceElementChangeService.queuePriceElementChangeJob(job)).not.toEqual(batchJobId);
            expect(await PriceElementChangeService.queuePriceElementChangeJob(job)).toEqual(jasmine.any(String));
        });
    });

    describe('releaseQueueLockForAccount', () => {
        it('should delete the lock document by accountId', async () => {
            await queueLockCollection.insertOne({
                accountId,
            });

            expect(await queueLockCollection.count({accountId})).toBe(1);

            await PriceElementChangeService.releaseQueueLockForAccount(accountId);

            expect(await queueLockCollection.count({accountId})).toBe(0);
        });
    });
});
