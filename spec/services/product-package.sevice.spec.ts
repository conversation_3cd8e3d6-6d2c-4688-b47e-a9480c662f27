import * as _ from 'lodash';
import {ProductPackageData} from '../../src/models/product-package-data';
import {ProductPackageService} from '../../src/services/product-package.service';
import {ProductRef} from '../../src/models/product-ref';

describe('ProductPackageService', () => {
    const brand = 'brand1';
    const component1Sku = 'component1Sku';
    const component2Sku = 'component2Sku';
    const component3Sku = 'component3Sku';
    const component4Sku = 'component4Sku';
    const package1Sku = 'package1Sku';
    const package2Sku = 'package2Sku';

    let productPackageData: ProductPackageData[];
    let productPackageService: ProductPackageService;

    beforeEach(() => {
        productPackageData = [
            {
                brand,
                components: [
                    {
                        brand,
                        quantity: 1,
                        sku: component1Sku,
                    },
                    {
                        brand,
                        quantity: 2,
                        sku: component2Sku,
                    },
                ],
                isPackage: true,
                sku: package1Sku,
            },
            {
                brand,
                components: [
                    {
                        brand,
                        quantity: 1,
                        sku: component2Sku,
                    },
                    {
                        brand,
                        quantity: 3,
                        sku: component3Sku,
                    },
                    {
                        brand,
                        quantity: 1,
                        sku: component4Sku,
                    },
                ],
                isPackage: true,
                sku: package2Sku,
            },
            {
                brand,
                isComponent: true,
                packages: [
                    {
                        brand,
                        quantity: 1,
                        sku: package1Sku,
                    },
                ],
                sku: component1Sku,
            },
            {
                brand,
                isComponent: true,
                packages: [
                    {
                        brand,
                        quantity: 2,
                        sku: package1Sku,
                    },
                    {
                        brand,
                        quantity: 1,
                        sku: package2Sku,
                    },
                ],
                sku: component2Sku,
            },
            {
                brand,
                isComponent: true,
                packages: [
                    {
                        brand,
                        quantity: 3,
                        sku: package2Sku,
                    },
                ],
                sku: component3Sku,
            },
            {
                brand,
                isComponent: true,
                packages: [
                    {
                        brand,
                        quantity: 1,
                        sku: package2Sku,
                    },
                ],
                sku: component4Sku,
            },
        ];

        productPackageService = new ProductPackageService();
    });

    describe('extractPackageProducts', () => {
        it('should extract data for select package products', () => {
            const expectedPackageData = [
                _.cloneDeep(productPackageData[0]),
                _.cloneDeep(productPackageData[1]),
            ];
            const productRefs = [
                {
                    brand,
                    sku: package1Sku,
                },
                {
                    brand,
                    sku: package2Sku,
                },
            ];
            expect(productPackageService.extractPackageProducts(productRefs, productPackageData))
                .toEqual(expectedPackageData);
        });

        it('should not return data for unselected package products', () => {
            const expectedPackageData = [
                _.cloneDeep(productPackageData[0]),
            ];
            const productRefs = [
                {
                    brand,
                    sku: package1Sku,
                },
            ];
            expect(productPackageService.extractPackageProducts(productRefs, productPackageData))
                .toEqual(expectedPackageData);
        });

        it('should not return data for non-package products', () => {
            const expectedPackageData = [];
            const productRefs = [
                {
                    brand,
                    sku: component1Sku,
                },
            ];
            expect(productPackageService.extractPackageProducts(productRefs, productPackageData))
                .toEqual(expectedPackageData);
        });
    });

    describe('filterPackageProducts', () => {
        it('should filter out package products', () => {
            const expectedPackageData = [
                _.cloneDeep(productPackageData[0]),
                _.cloneDeep(productPackageData[1]),
            ];
            const productRefs = [
                {
                    brand,
                    sku: package1Sku,
                },
                {
                    brand,
                    sku: package2Sku,
                },
            ];

            const expectedProductRefs = [];

            expect(productPackageService.filterPackageProducts(productRefs, productPackageData))
                .toEqual(expectedPackageData);

            expect(productRefs).toEqual(expectedProductRefs);
        });

        it('should not filter out component products', () => {
            const expectedPackageData = [];
            const productRefs = [
                {
                    brand,
                    sku: component1Sku,
                },
                {
                    brand,
                    sku: component2Sku,
                },
            ];

            const expectedProductRefs = _.cloneDeep(productRefs);

            expect(productPackageService.filterPackageProducts(productRefs, productPackageData))
                .toEqual(expectedPackageData);

            expect(productRefs).toEqual(expectedProductRefs);
        });
    });

    describe('getPackageProductsWithComponents', () => {
        it('should return the associated package product data for the supplied components', () => {
            const componentRefs: ProductRef[] = [
                {
                    brand,
                    sku: component3Sku,
                },
            ];

            const expectedPackageProducts: ProductPackageData[] = [
                productPackageData[1],
            ];

            expect(productPackageService.getPackageProductsWithComponents(componentRefs, productPackageData))
                .toEqual(expectedPackageProducts);
        });
    });

    describe('getUniqueComponentRefsFromPackageProducts', () => {
        it('should return unique components across package products', () => {
            const expectedComponents: ProductRef[] = [
                {
                    brand,
                    sku: component1Sku,
                },
                {
                    brand,
                    sku: component2Sku,
                },
                {
                    brand,
                    sku: component3Sku,
                },
                {
                    brand,
                    sku: component4Sku,
                },
            ];

            expect(ProductPackageService.getUniqueComponentRefsFromPackageProducts(productPackageData))
                .toEqual(expectedComponents);
        });
    });
});
