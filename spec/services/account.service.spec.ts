import {createPriceElementChangeServiceSpy} from '../mocks/price-element-change.service.mock';
import {AccountService} from '../../src/services/account.service';
import {createPriceListServiceSpy} from '../mocks/price-list.service.mock';
import {createPriceRuleServiceSpy} from '../mocks/price-rule.service.mock';
import {createProductPriceServiceSpy} from '../mocks/product-price.service.mock';
import {createRentalTermServiceSpy} from '../mocks/rental-term.service.mock';
import {createAccountSettingsServiceSpy} from '../mocks/account-settings.service.mock';

describe('accountService', () => {

    let accountId;
    let accountSettingsServiceSpy;
    let priceElementChangeServiceSpy;
    let priceListServiceSpy;
    let priceRuleServiceSpy;
    let productPriceServiceSpy;
    let rentalTermServiceSpy;

    beforeEach(() => {
        accountId = 'accountId1';
        accountSettingsServiceSpy = createAccountSettingsServiceSpy();
        priceElementChangeServiceSpy = createPriceElementChangeServiceSpy();
        priceListServiceSpy = createPriceListServiceSpy();
        priceRuleServiceSpy = createPriceRuleServiceSpy();
        productPriceServiceSpy = createProductPriceServiceSpy();
        rentalTermServiceSpy = createRentalTermServiceSpy();
    });

    it('should exist', () => {
        expect(AccountService).toBeTruthy();
    });

    describe('deleteAllPricingDataWithAccountId', () => {

        it('should call delete methods for all services which can delete account related pricing data', async () => {
            await AccountService.deleteAllPricingDataWithAccountId(accountId);
            expect(accountSettingsServiceSpy.deleteWithAccountId).toHaveBeenCalledWith(accountId);
            expect(priceElementChangeServiceSpy.deletePriceElementChangeJobsWithAccountId).toHaveBeenCalledWith(
                accountId
            );
            expect(priceListServiceSpy.deletePriceListsWithAccountId).toHaveBeenCalledWith(accountId);
            expect(priceRuleServiceSpy.deletePriceRulesWithAccountId).toHaveBeenCalledWith(accountId);
            expect(productPriceServiceSpy.deleteProductPricesWithAccountId).toHaveBeenCalledWith(accountId);
            expect(rentalTermServiceSpy.deleteRentalTermsWithAccountId).toHaveBeenCalledWith(accountId);
        });

    });
});
