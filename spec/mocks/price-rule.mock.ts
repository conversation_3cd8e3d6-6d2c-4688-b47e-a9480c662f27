import * as _ from 'lodash';
import {
    ExpressionTypeFreightPrice,
    ExpressionTypeMath,
    ExpressionTypeRound,
    PriceFieldCalculationRule,
    PriceRule,
} from '../../src/models/price-rule';

export function getPriceRule(initialValues?: Partial<PriceRule>): PriceRule {
    const priceRule: PriceRule = {
        _id: undefined,
        calculation: {
            list: null,
            rental: {},
            retail: null,
        },
        enabled: true,
        id: '',
        isBaseRule: false,
        name: '',
        priceListId: '',
        productSelectionCriteria: [],
        productSelectionType: undefined,
    };
    return _.extend(priceRule, initialValues);
}

export function getFieldCalculation(initialValues?: Partial<PriceFieldCalculationRule>): PriceFieldCalculationRule {
    const fieldCalculation: PriceFieldCalculationRule = {
        basePriceDef: 'cost',
        expressions: [],
        minIsMAP: false,
    };
    return _.extend(fieldCalculation, initialValues);
}

export function getExpressionTypeMath(initialValues?: Partial<ExpressionTypeMath>): ExpressionTypeMath {
    const expressionTypeMath: ExpressionTypeMath = {
        expressionType: 'math',
        operation: 'multiply',
        value: 1,
    };
    return _.extend(expressionTypeMath, initialValues);
}

export function getExpressionTypeRound(initialValues?: Partial<ExpressionTypeRound>): ExpressionTypeRound {
    const expressionTypeMath: ExpressionTypeRound = {
        decimalValue: null,
        direction: 'up',
        expressionType: 'round',
        integerValue: null,
        maxAdjustment: {},
    };
    return _.extend(expressionTypeMath, initialValues);
}

export function getExpressionFreightPrice(
    initialValues?: Partial<ExpressionTypeFreightPrice>
): ExpressionTypeFreightPrice {
    const expressionTypeFreightPrice: ExpressionTypeFreightPrice = {
        expressionType: 'freight',
        operation: 'Direct To Consumer',
    };
    return _.extend(expressionTypeFreightPrice, initialValues);
}
