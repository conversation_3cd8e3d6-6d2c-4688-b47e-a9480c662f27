import {createServiceSpy} from '@wondersign/serverless-services';
import {ProductPriceService} from '../../src/services/product-price.service';

export function createProductPriceServiceSpy(): any {
    const instanceMethods = createServiceSpy({
        calculateProductPricesWithPriceRule: undefined,
        deleteFixedPricesForPriceListIdAndProducts: Promise.resolve(),
        deletePricesForDeletedProducts: Promise.resolve(),
        extractBasePriceFromProduct: undefined,
        getActiveProductPrices: Promise.resolve(),
        getQueryTotalCount: undefined,
        initRentalTermMap: Promise.resolve(),
        matchPriceRulesForPriceListAndProducts: Promise.resolve(),
        queryProductPrices: Promise.resolve(),
        saveProductPrices: Promise.resolve(),
        unsetRentalTermIdForAccountId: Promise.resolve(),
        updatePricesForMutatedProducts: Promise.resolve(),
        updatePricesForPriceElementChange: Promise.resolve(),
    }, ProductPriceService.prototype);

    const classMethods = createServiceSpy({
        countProductPrices: Promise.resolve(),
        deleteProductPricesWithAccountId: Promise.resolve(),
        deleteProductPricesWithPriceListId: Promise.resolve(),
        updateExpirationDateForPriceListId: Promise.resolve(),
    }, ProductPriceService);

    return {
        ...classMethods,
        ...instanceMethods,
    };
}
