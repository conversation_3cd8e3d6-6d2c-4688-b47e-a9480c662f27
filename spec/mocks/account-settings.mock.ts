import * as _ from 'lodash';
import {AccountSettings, PackageCalculationMethod} from '../../src/models/account-settings';

export function getAccountSettings(initialValues?: Partial<AccountSettings>): AccountSettings {
    const accountSettings: AccountSettings = {
        accountId: undefined,
        packagedProductPriceCalculationMethod: PackageCalculationMethod.mergedCost,
        packagedProductShowPriceIfComponentZero: false,
    };
    return _.extend(accountSettings, initialValues);
}
