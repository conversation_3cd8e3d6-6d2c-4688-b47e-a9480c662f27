import * as _ from 'lodash';
import {ProductPrice} from '../../src/models/product-price';

export function getProductPrice(initialValues?: Partial<ProductPrice>): ProductPrice {
    const productPrice: ProductPrice = {
        accountId: '',
        brand: '',
        isFixed: false,
        priceListId: '',
        priceRuleId: '',
        prices: {
            list: null,
            rental: {},
            retail: null,
        },
        sku: '',
    };
    return _.extend(productPrice, initialValues);
}
