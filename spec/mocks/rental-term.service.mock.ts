import {createServiceSpy} from '@wondersign/serverless-services';
import {RentalTermService} from '../../src/services/rental-term.service';

export function createRentalTermServiceSpy(): any {
    const instanceMethods = createServiceSpy({
        deleteRentalTermWithId: Promise.resolve(),
        queryRentalTerm: Promise.resolve(),
        saveRentalTerm: Promise.resolve(),
        saveRentalTermIdsOrder: Promise.resolve(),
    }, RentalTermService.prototype);

    const classMethods = createServiceSpy({
        deleteRentalTermsWithAccountId: Promise.resolve(),
    }, RentalTermService);

    return {
        ...classMethods,
        ...instanceMethods,
    };
}
