import {createServiceSpy} from '@wondersign/serverless-services';
import {BatchJobService} from '../../src/services/batch-job.service';

export function createBatchJobServiceSpy(): any {
    const instanceMethods = createServiceSpy({
        setCompletionHandler: undefined,
        updateProcessedRequest: Promise.resolve(),
    }, BatchJobService.prototype);

    const classMethods = createServiceSpy({
        receiveRequest: Promise.resolve(),
    }, BatchJobService);

    return {
        ...classMethods,
        ...instanceMethods,
    };
}
