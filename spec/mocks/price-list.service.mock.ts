import {createServiceSpy} from '@wondersign/serverless-services';
import {PriceListService} from '../../src/services/price-list.service';

export function createPriceListServiceSpy(): any {
    const instanceMethods = createServiceSpy({
        deletePriceListWithId: Promise.resolve(),
        hasPromoPricesCalculated: Promise.resolve(),
        isActivePromotionalPriceList: false,
        markPromoPricesCalculated: Promise.resolve(),
        prependPriceRuleIdForPriceList: Promise.resolve(),
        queryPriceList: Promise.resolve(),
        savePriceList: Promise.resolve(),
    }, PriceListService.prototype);

    const classMethods = createServiceSpy({
        deletePriceListsWithAccountId: Promise.resolve(),
        getExpirationDateForPromoPriceList: null,
    }, PriceListService);

    return {
        ...classMethods,
        ...instanceMethods,
    };
}
