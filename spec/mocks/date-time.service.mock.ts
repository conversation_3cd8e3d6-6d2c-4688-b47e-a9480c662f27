import {createServiceSpy} from '@wondersign/serverless-services';
import {DateTimeService} from '../../src/services/date-time.service';

export function createDateTimeServiceSpy(): any {
    return createServiceSpy({
        convertRelativeTimestampToDate: undefined,
        get24HourWithMeridiem: undefined,
        getTimezonesGroupedByOffset: Promise.resolve(),
        isRelativeTimeMatchForUtcTimestamp: undefined,
        trackTimezoneForAccountId: Promise.resolve(),
    }, DateTimeService);
}
