import {createServiceSpy} from '@wondersign/serverless-services';
import {ProductPackageService} from '../../src/services/product-package.service';

export function createProductPackageServiceSpy(): any {
    const instanceMethods = createServiceSpy({
        extractPackageProducts: [],
        filterPackageProducts: [],
        getPackageProductsWithComponents: [],
    }, ProductPackageService.prototype);

    const staticMethods = createServiceSpy({
        getUniqueComponentRefsFromPackageProducts: [],
    }, ProductPackageService);

    return {
        ...staticMethods,
        ...instanceMethods,
    };
}
