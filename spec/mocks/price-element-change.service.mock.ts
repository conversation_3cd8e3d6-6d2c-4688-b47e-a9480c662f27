import {createServiceSpy} from '@wondersign/serverless-services';
import {PriceElementChangeService} from '../../src/services/price-element-change.service';

export function createPriceElementChangeServiceSpy(): any {
    return createServiceSpy({
        deletePriceElementChangeJobsWithAccountId: Promise.resolve(),
        getPriceElementChangesForAccountId: Promise.resolve(),
        hasExistingJobForAccount: Promise.resolve(),
        publishPriceElementChangeJobCompletion: Promise.resolve(),
        queueAccountId: Promise.resolve(),
        queuePriceElementChangeJob: Promise.resolve(),
        releaseQueueLockForAccount: Promise.resolve(),
        shiftPriceElementChangeForAccountId: Promise.resolve(),
    }, PriceElementChangeService);
}
