import {createServiceSpy} from '@wondersign/serverless-services';
import {PriceRuleService} from '../../src/services/price-rule.service';

export function createPriceRuleServiceSpy(): any {
    const instanceMethods = createServiceSpy({
        deletePriceRuleWithId: Promise.resolve(),
        queryPriceRule: Promise.resolve(),
        savePriceRule: Promise.resolve(),
        unsetRentalTermIdForAccountId: Promise.resolve(),
    }, PriceRuleService.prototype);

    const classMethods = createServiceSpy({
        deletePriceRulesWithAccountId: Promise.resolve(),
        deletePriceRulesWithPriceListId: Promise.resolve(),
    }, PriceRuleService);

    return {
        ...classMethods,
        ...instanceMethods,
    };
}
